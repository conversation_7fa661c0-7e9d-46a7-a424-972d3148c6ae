syntax = "proto3";

package testdatamaker.patientchartdatamaker;

import "ExaminationReportDataMaker.proto";
import "SubjectiveExaminationReportDataMaker.proto";
import "SurgeryReportDataMaker.proto";
import "CoreTypesDataMaker.proto";
import "alcon/interop/type/Patient.proto";
import "alcon/interop/type/PatientChart.proto";

message PatientChartParams {
  alcon.interop.type.Patient.Ref patientRef = 1;
  repeated examinationreportdatamaker.ExaminationReportDataMakerConfig examinationReportConfigs = 2;
  repeated subjectiveexaminationreportdatamaker.SubjectiveExaminationReportConfig subjectiveExaminationReports = 3;
  repeated surgeryreportdatamaker.SurgeryReportConfig surgeryReports = 4;
}

message PatientChartConfig {
  coretypes.GeneratingPolicy policy = 1;
  optional bool exists = 2;
  PatientChartParams params = 3;
  alcon.interop.type.PatientChart seed = 4;
}