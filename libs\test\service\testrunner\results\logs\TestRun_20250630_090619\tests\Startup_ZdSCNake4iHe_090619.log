2025-06-30T09:06:19.5006252+05:30 [Information] [----------] 1 step from Startup
2025-06-30T09:06:19.5034985+05:30 [Information] Set Value to Step 'e9472b7a-b7c7-406b-a6b5-28b5801fc292' name:
"Initialize Database"
2025-06-30T09:06:19.5037918+05:30 [Information] Set Value to Step 'e9472b7a-b7c7-406b-a6b5-28b5801fc292' description:
"Setup initial database state"
2025-06-30T09:06:19.5040247+05:30 [Information] [ RUN      ] Startup > Initialize Database
2025-06-30T09:06:19.5051728+05:30 [Information] Executing single instance of step 'Initialize Database'.
2025-06-30T09:06:19.5063943+05:30 [Information] Executing step 'Initialize Database' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "e9472b7a-b7c7-406b-a6b5-28b5801fc292",
        "tables": {},
        "variables": {},
        "name": "Initialize Database",
        "description": "Setup initial database state"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:06:19.5082730+05:30 [Information] Processing $var: path 'baseUrl'
2025-06-30T09:06:19.5101075+05:30 [Information] Checking if 'baseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:06:19.5120028+05:30 [Information] Resolving path 'baseUrl' using global variables.
2025-06-30T09:06:19.5122929+05:30 [Information] Resolved JPath '$var:baseUrl' to 'https://httpbin.org'
2025-06-30T09:06:19.5124347+05:30 [Information] Resolved template pattern '{{$var:baseUrl}}' to 'https://httpbin.org' in '{{$var:baseUrl}}/post'
2025-06-30T09:06:19.5127193+05:30 [Information] Resolved template '{{$var:baseUrl}}/post' to 'https://httpbin.org/post'
2025-06-30T09:06:19.5149771+05:30 [Information] Set Value to Step 'e9472b7a-b7c7-406b-a6b5-28b5801fc292' input:
{
  "Method": "POST",
  "RequestUri": "https://httpbin.org/post",
  "Headers": {
    "Content-Type": "application/json"
  },
  "Content": {
    "action": "initialize_db"
  }
}
2025-06-30T09:06:21.5258800+05:30 [Information] Set Value to Step 'e9472b7a-b7c7-406b-a6b5-28b5801fc292' output:
{
  "statusCode": 200,
  "reasonPhrase": "OK",
  "headers": [
    {
      "Key": "Date",
      "Value": [
        "Mon, 30 Jun 2025 03:36:26 GMT"
      ]
    },
    {
      "Key": "Connection",
      "Value": [
        "keep-alive"
      ]
    },
    {
      "Key": "Server",
      "Value": [
        "gunicorn/19.9.0"
      ]
    },
    {
      "Key": "Access-Control-Allow-Origin",
      "Value": [
        "*"
      ]
    },
    {
      "Key": "Access-Control-Allow-Credentials",
      "Value": [
        "true"
      ]
    }
  ],
  "content": {
    "args": {},
    "data": "{\r\n  \"action\": \"initialize_db\"\r\n}",
    "files": {},
    "form": {},
    "headers": {
      "Content-Length": "33",
      "Content-Type": "application/json; charset=utf-8",
      "Host": "httpbin.org",
      "X-Amzn-Trace-Id": "Root=1-6862063a-37778f8c62838a7d4754e6f8"
    },
    "json": {
      "action": "initialize_db"
    },
    "origin": "**************",
    "url": "https://httpbin.org/post"
  }
}
2025-06-30T09:06:21.5265029+05:30 [Information] Resolving path 'output.content.json.action' using step input/output.
2025-06-30T09:06:21.5267021+05:30 [Information] Resolved JPath 'output.content.json.action' to 'initialize_db'
2025-06-30T09:06:21.5277313+05:30 [Information] Set Value to Step 'e9472b7a-b7c7-406b-a6b5-28b5801fc292' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T03:36:19.505145Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:06:21.5304989+05:30 [Information] Run Asserter : {"Expression1":{"JPath":"$curStep:output.content.json.action","ErrorMesage":null},"Expression2":{"Value":null,"ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:06:21.5308752+05:30 [Information] Resolving path 'output.content.json.action' using $curStep:
2025-06-30T09:06:21.5310011+05:30 [Information] Resolved JPath '$curStep:output.content.json.action' to 'initialize_db'
2025-06-30T09:06:21.5324124+05:30 [Information] Error in EqualExpression: The method or operation is not implemented.
2025-06-30T09:06:21.5333555+05:30 [Information] Set Value to Step 'e9472b7a-b7c7-406b-a6b5-28b5801fc292' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 2017,
  "startTime": "2025-06-30T03:36:19.505145Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:06:21.5335353+05:30 [Information] Step 'Initialize Database' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "e9472b7a-b7c7-406b-a6b5-28b5801fc292",
        "tables": {},
        "variables": {},
        "name": "Initialize Database",
        "description": "Setup initial database state",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "action": "initialize_db"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 03:36:26 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"action\": \"initialize_db\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "33",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-6862063a-37778f8c62838a7d4754e6f8"
            },
            "json": {
              "action": "initialize_db"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 2017,
          "startTime": "2025-06-30T03:36:19.505145Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:06:21.5337077+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:06:21.5338111+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:06:21.5338804+05:30 [Information] No performance data found for step 'Initialize Database' (http)
2025-06-30T09:06:21.5341669+05:30 [Information] [ OK       ]
2025-06-30T09:06:21.5353199+05:30 [Information] [----------] 1 step from Startup (2032 ms total)
