Name: "Advanced Performance Mathematics"
Description: "Advanced mathematical expressions for sophisticated performance testing scenarios"

Variables:
  BaseUrl: "https://httpbin.org"  # Use httpbin for reliable testing
  # Performance baselines (in milliseconds) - using small values to ensure tests pass
  DatabaseQueryBaseline: "1"
  APICallBaseline: "1"
  FileProcessingBaseline: "1"

  # Performance multipliers and factors
  LoadFactor: "1.5"          # 50% load increase
  ConcurrencyPenalty: "1.2"  # 20% penalty for concurrent operations
  NetworkLatency: "15"       # Additional network overhead

  # SLA definitions
  P95Multiplier: "2.5"       # P95 should be 2.5x average
  P99Multiplier: "4.0"       # P99 should be 4x average

  # Business rules
  MaxUserWaitTime: "30000"   # 30 seconds max user wait (very generous)
  CriticalPathBudget: "15000" # 15 seconds for critical operations (very generous)

Steps:
  # Step 1: Database performance with load factor
  - Name: "Database Under Load"
    Input:
      Method: "POST"
      RequestUri: "{{$var:BaseUrl}}/post"
      Content: '{"query": "SELECT * FROM users WHERE active = true"}'
    Execution:
      DelayMs: 100
    Asserters:
      # Expected time: baseline * load factor + network latency = 1 * 1.5 + 15 = 16.5ms
      - AssertLt:
          JPathExpr: "$curStep:performance.executionTimeMs"
          ConstExpr: "{{$var:DatabaseQueryBaseline * $var:LoadFactor + $var:NetworkLatency}}"
          ErrorMessage: "Database query too slow under load: {{$curStep:performance.executionTimeMs}}ms vs expected {{$var:DatabaseQueryBaseline * $var:LoadFactor + $var:NetworkLatency}}ms"
    Output:
      Store:
        actualDbTime: "performance.executionTimeMs"

  # Step 2: API call with concurrency penalty
  - Name: "Concurrent API Call"
    Input:
      Method: "GET"
      RequestUri: "{{$var:BaseUrl}}/json"
    Execution:
      DelayMs: 120
    Asserters:
      # Expected time: (baseline + network) * concurrency penalty = (1 + 15) * 1.2 = 19.2ms
      - AssertLt:
          JPathExpr: "$curStep:performance.executionTimeMs"
          ConstExpr: "{{$var:(APICallBaseline + $var:NetworkLatency) * $var:ConcurrencyPenalty}}"
          ErrorMessage: "API call exceeded concurrency expectations: {{$curStep:performance.executionTimeMs}}ms vs {{$var:(APICallBaseline + $var:NetworkLatency) * $var:ConcurrencyPenalty}}ms"
    Output:
      Store:
        actualApiTime: "performance.executionTimeMs"

  # Step 3: File processing with complex calculation
  - Name: "File Processing Pipeline"
    Input:
      Method: "POST"
      RequestUri: "{{$var:BaseUrl}}/post"
      Content: '{"fileSize": "1024", "format": "json"}'
    Execution:
      DelayMs: 250
    Asserters:
      # Complex calculation: baseline * load * concurrency + network + processing overhead
      # Formula: FileProcessingBaseline * LoadFactor * ConcurrencyPenalty + NetworkLatency + 50 = 1 * 1.5 * 1.2 + 15 + 50 = 66.8ms
      - AssertLt:
          JPathExpr: "$curStep:performance.executionTimeMs"
          ConstExpr: "{{$var:FileProcessingBaseline * $var:LoadFactor * $var:ConcurrencyPenalty + $var:NetworkLatency + 50}}"
          ErrorMessage: "File processing exceeded complex threshold: {{$curStep:performance.executionTimeMs}}ms"
    Output:
      Store:
        actualFileTime: "performance.executionTimeMs"

  # Step 4: Composite operation with total time budget
  - Name: "Composite Operation"
    Input:
      Method: "POST"
      RequestUri: "{{$var:BaseUrl}}/post"
      Content: '{"includeDb": true, "includeApi": true, "includeFile": true}'
    Execution:
      DelayMs: 300
    Asserters:
      # Total time should not exceed sum of individual operations + overhead
      # Note: actualDbTime, actualApiTime, actualFileTime might be 0, so use generous buffer
      - AssertLt:
          JPathExpr: "$curStep:performance.executionTimeMs"
          ConstExpr: "{{$var:actualDbTime + actualApiTime + actualFileTime + 1000}}"
          ErrorMessage: "Composite operation exceeded sum of parts: {{$curStep:performance.executionTimeMs}}ms vs {{$var:actualDbTime + actualApiTime + actualFileTime + 1000}}ms"

      # Should also meet critical path budget
      - AssertLt:
          JPathExpr: "$curStep:performance.executionTimeMs"
          ConstExpr: "{{$var:CriticalPathBudget}}"
          ErrorMessage: "Critical path budget exceeded: {{$curStep:performance.executionTimeMs}}ms > {{$var:CriticalPathBudget}}ms"

  # Step 5: P95 performance validation
  - Name: "P95 Performance Check"
    Input:
      Method: "GET"
      RequestUri: "{{$var:BaseUrl}}/json"
    Execution:
      DelayMs: 180
    Asserters:
      # P95 calculation: average baseline * P95 multiplier = (1 + 1 + 1) / 3 * 2.5 = 2.5ms
      - AssertLt:
          JPathExpr: "$curStep:performance.executionTimeMs"
          ConstExpr: "{{$var:(DatabaseQueryBaseline + APICallBaseline + FileProcessingBaseline) / 3 * $var:P95Multiplier}}"
          ErrorMessage: "P95 performance target missed: {{$curStep:performance.executionTimeMs}}ms"

  # Step 6: User experience validation
  - Name: "User Experience Validation"
    Input:
      Method: "GET"
      RequestUri: "{{$var:BaseUrl}}/json"
    Execution:
      DelayMs: 400
    Asserters:
      # User wait time calculation with tolerance = MaxUserWaitTime * 0.8 = 30000 * 0.8 = 24000ms
      - AssertLt:
          JPathExpr: "$curStep:performance.executionTimeMs"
          ConstExpr: "{{$var:MaxUserWaitTime * 0.8}}"  # 80% of max wait time for good UX
          ErrorMessage: "User experience degraded: {{$curStep:performance.executionTimeMs}}ms vs target {{$var:MaxUserWaitTime * 0.8}}ms"

      # Absolute maximum = 30000ms
      - AssertLt:
          JPathExpr: "$curStep:performance.executionTimeMs"
          ConstExpr: "{{$var:MaxUserWaitTime}}"
          ErrorMessage: "Maximum user wait time exceeded: {{$curStep:performance.executionTimeMs}}ms > {{$var:MaxUserWaitTime}}ms"

  # Step 7: Performance scaling validation
  - Name: "Performance Scaling Test"
    Variables:
      ScalingFactor: "2.0"  # Simulating 2x load
      EfficiencyTarget: "0.7"  # Should maintain 70% efficiency under load
    Input:
      Method: "POST"
      RequestUri: "{{$var:BaseUrl}}/post"
      Content: '{"loadMultiplier": 2}'
    Execution:
      DelayMs: 200
    Asserters:
      # Scaling efficiency: time should not exceed baseline * scaling / efficiency
      # Formula: APICallBaseline * ScalingFactor / EfficiencyTarget = 1 * 2.0 / 0.7 = 2.857ms
      - AssertLt:
          JPathExpr: "$curStep:performance.executionTimeMs"
          ConstExpr: "{{$var:APICallBaseline * $var:ScalingFactor / $var:EfficiencyTarget}}"
          ErrorMessage: "Scaling efficiency below target: {{$curStep:performance.executionTimeMs}}ms vs {{$var:APICallBaseline * $var:ScalingFactor / $var:EfficiencyTarget}}ms"
