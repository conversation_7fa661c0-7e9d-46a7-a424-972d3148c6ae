2025-06-30T09:33:56.6907774+05:30 [Information] [----------] 1 step from Startup
2025-06-30T09:33:56.6932596+05:30 [Information] Set Value to Step 'd678b4c4-c50e-4fb2-bd18-959ca09a2d2d' name:
"Initialize Database"
2025-06-30T09:33:56.6934979+05:30 [Information] Set Value to Step 'd678b4c4-c50e-4fb2-bd18-959ca09a2d2d' description:
"Setup initial database state"
2025-06-30T09:33:56.6937265+05:30 [Information] [ RUN      ] Startup > Initialize Database
2025-06-30T09:33:56.6949682+05:30 [Information] Executing single instance of step 'Initialize Database'.
2025-06-30T09:33:56.6961065+05:30 [Information] Executing step 'Initialize Database' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "d678b4c4-c50e-4fb2-bd18-959ca09a2d2d",
        "tables": {},
        "variables": {},
        "name": "Initialize Database",
        "description": "Setup initial database state"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:33:56.6978326+05:30 [Information] Processing $var: path 'baseUrl'
2025-06-30T09:33:56.6992059+05:30 [Information] Checking if 'baseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:33:56.7007377+05:30 [Information] Resolving path 'baseUrl' using global variables.
2025-06-30T09:33:56.7009515+05:30 [Information] Resolved JPath '$var:baseUrl' to 'https://httpbin.org'
2025-06-30T09:33:56.7010383+05:30 [Information] Resolved template pattern '{{$var:baseUrl}}' to 'https://httpbin.org' in '{{$var:baseUrl}}/post'
2025-06-30T09:33:56.7012799+05:30 [Information] Resolved template '{{$var:baseUrl}}/post' to 'https://httpbin.org/post'
2025-06-30T09:33:56.7034097+05:30 [Information] Set Value to Step 'd678b4c4-c50e-4fb2-bd18-959ca09a2d2d' input:
{
  "Method": "POST",
  "RequestUri": "https://httpbin.org/post",
  "Headers": {
    "Content-Type": "application/json"
  },
  "Content": {
    "action": "initialize_db"
  }
}
2025-06-30T09:33:58.9789863+05:30 [Information] Set Value to Step 'd678b4c4-c50e-4fb2-bd18-959ca09a2d2d' output:
{
  "statusCode": 200,
  "reasonPhrase": "OK",
  "headers": [
    {
      "Key": "Date",
      "Value": [
        "Mon, 30 Jun 2025 04:04:04 GMT"
      ]
    },
    {
      "Key": "Connection",
      "Value": [
        "keep-alive"
      ]
    },
    {
      "Key": "Server",
      "Value": [
        "gunicorn/19.9.0"
      ]
    },
    {
      "Key": "Access-Control-Allow-Origin",
      "Value": [
        "*"
      ]
    },
    {
      "Key": "Access-Control-Allow-Credentials",
      "Value": [
        "true"
      ]
    }
  ],
  "content": {
    "args": {},
    "data": "{\r\n  \"action\": \"initialize_db\"\r\n}",
    "files": {},
    "form": {},
    "headers": {
      "Content-Length": "33",
      "Content-Type": "application/json; charset=utf-8",
      "Host": "httpbin.org",
      "X-Amzn-Trace-Id": "Root=1-68620cb4-304e56bb1450832b0cd9d8e8"
    },
    "json": {
      "action": "initialize_db"
    },
    "origin": "**************",
    "url": "https://httpbin.org/post"
  }
}
2025-06-30T09:33:58.9795246+05:30 [Information] Resolving path 'output.content.json.action' using step input/output.
2025-06-30T09:33:58.9797045+05:30 [Information] Resolved JPath 'output.content.json.action' to 'initialize_db'
2025-06-30T09:33:58.9807009+05:30 [Information] Set Value to Step 'd678b4c4-c50e-4fb2-bd18-959ca09a2d2d' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:03:56.6949431Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:33:58.9833326+05:30 [Information] Run Asserter : {"Expression1":{"JPath":"$curStep:output.content.json.action","ErrorMesage":null},"Expression2":{"Value":null,"ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:33:58.9836957+05:30 [Information] Resolving path 'output.content.json.action' using $curStep:
2025-06-30T09:33:58.9838195+05:30 [Information] Resolved JPath '$curStep:output.content.json.action' to 'initialize_db'
2025-06-30T09:33:58.9852256+05:30 [Information] Error in EqualExpression: The method or operation is not implemented.
2025-06-30T09:33:58.9861377+05:30 [Information] Set Value to Step 'd678b4c4-c50e-4fb2-bd18-959ca09a2d2d' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 2281,
  "startTime": "2025-06-30T04:03:56.6949431Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:33:58.9863144+05:30 [Information] Step 'Initialize Database' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "d678b4c4-c50e-4fb2-bd18-959ca09a2d2d",
        "tables": {},
        "variables": {},
        "name": "Initialize Database",
        "description": "Setup initial database state",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "action": "initialize_db"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:04:04 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"action\": \"initialize_db\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "33",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620cb4-304e56bb1450832b0cd9d8e8"
            },
            "json": {
              "action": "initialize_db"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 2281,
          "startTime": "2025-06-30T04:03:56.6949431Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:33:58.9864958+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:33:58.9866963+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:33:58.9868858+05:30 [Information] No performance data found for step 'Initialize Database' (http)
2025-06-30T09:33:58.9872056+05:30 [Information] [ OK       ]
2025-06-30T09:33:58.9882983+05:30 [Information] [----------] 1 step from Startup (2295 ms total)
