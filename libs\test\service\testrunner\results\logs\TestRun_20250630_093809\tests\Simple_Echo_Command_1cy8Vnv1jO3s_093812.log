2025-06-30T09:38:12.6562070+05:30 [Information] [----------] 1 step from Simple Echo Command
2025-06-30T09:38:12.6569694+05:30 [Information] Set Value to Step '1d08f26c-71ee-434c-aea0-019dcdb90b0d' name:
"Step echo"
2025-06-30T09:38:12.6571853+05:30 [Information] Set Value to Step '1d08f26c-71ee-434c-aea0-019dcdb90b0d' description:
"Echo Hello, World!"
2025-06-30T09:38:12.6572806+05:30 [Information] [ RUN      ] Simple Echo Command > Step echo
2025-06-30T09:38:12.6578013+05:30 [Information] Executing single instance of step 'Step echo'.
2025-06-30T09:38:12.6579972+05:30 [Information] Executing step 'Step echo' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "1d08f26c-71ee-434c-aea0-019dcdb90b0d",
        "tables": {},
        "variables": {},
        "name": "Step echo",
        "description": "Echo Hello, World!"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:38:12.6583445+05:30 [Information] Set Value to Step '1d08f26c-71ee-434c-aea0-019dcdb90b0d' input:
{
  "command": "echo",
  "arguments": [
    "Hello, World!"
  ],
  "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
  "printOutput": false
}
2025-06-30T09:38:12.6825756+05:30 [Information] Set Value to Step '1d08f26c-71ee-434c-aea0-019dcdb90b0d' output:
{
  "stdout": "Hello, World!",
  "stderr": "",
  "exitCode": 0
}
2025-06-30T09:38:12.6827631+05:30 [Information] Set Value to Step '1d08f26c-71ee-434c-aea0-019dcdb90b0d' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:08:12.6577975Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "cmd"
}
2025-06-30T09:38:12.6828763+05:30 [Information] Run Asserter : {"Expression1":{"Value":"Hello, World!","ErrorMesage":null},"Expression2":{"JPath":"$curStep:$.output.stdout","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:38:12.6830095+05:30 [Information] Resolving path '$.output.stdout' using $curStep:
2025-06-30T09:38:12.6830828+05:30 [Information] Resolved JPath '$curStep:$.output.stdout' to 'Hello, World!'
2025-06-30T09:38:12.6831678+05:30 [Information] Set Value to Step '1d08f26c-71ee-434c-aea0-019dcdb90b0d' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 24,
  "startTime": "2025-06-30T04:08:12.6577975Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "cmd"
}
2025-06-30T09:38:12.6832453+05:30 [Information] Step 'Step echo' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "1d08f26c-71ee-434c-aea0-019dcdb90b0d",
        "tables": {},
        "variables": {},
        "name": "Step echo",
        "description": "Echo Hello, World!",
        "input": {
          "command": "echo",
          "arguments": [
            "Hello, World!"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Hello, World!",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 24,
          "startTime": "2025-06-30T04:08:12.6577975Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:38:12.6833188+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:38:12.6833790+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:38:12.6834221+05:30 [Information] No performance data found for step 'Step echo' (cmd)
2025-06-30T09:38:12.6834720+05:30 [Information] [ OK       ]
2025-06-30T09:38:12.6838788+05:30 [Information] [----------] 1 step from Simple Echo Command (26 ms total)
