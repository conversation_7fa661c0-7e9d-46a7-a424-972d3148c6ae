2025-06-30T09:38:22.5426602+05:30 [Information] [----------] 5 steps from ProtoFieldSetters test
2025-06-30T09:38:22.5435047+05:30 [Information] Set Value to Step '0fddebd5-e084-481d-ab0a-be751378f5c8' name:
"Step 1: Connect to the MBusClient with admin credentials"
2025-06-30T09:38:22.5436363+05:30 [Information] Set Value to Step '0fddebd5-e084-481d-ab0a-be751378f5c8' description:
"Connect to the MBusClient with admin credentials"
2025-06-30T09:38:22.5437011+05:30 [Information] [ RUN      ] ProtoFieldSetters test > Step 1: Connect to the MBusClient with admin credentials
2025-06-30T09:38:22.5439585+05:30 [Information] Executing single instance of step 'Step 1: Connect to the MBusClient with admin credentials'.
2025-06-30T09:38:22.5473892+05:30 [Information] Executing step 'Step 1: Connect to the MBusClient with admin credentials' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "0fddebd5-e084-481d-ab0a-be751378f5c8",
        "tables": {},
        "variables": {},
        "name": "Step 1: Connect to the MBusClient with admin credentials",
        "description": "Connect to the MBusClient with admin credentials"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "userId": "surgeon2",
      "userIdentifier": {
        "value": "surgeon2",
        "assigner": 6
      }
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:38:22.5477076+05:30 [Information] Processing $var: path 'serverIp'
2025-06-30T09:38:22.5478149+05:30 [Information] Checking if 'serverIp' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:22.5478796+05:30 [Information] Resolving path 'serverIp' using global variables.
2025-06-30T09:38:22.5479255+05:30 [Information] Resolved JPath '$var:serverIp' to '127.0.0.1'
2025-06-30T09:38:22.5479638+05:30 [Information] Resolved template pattern '{{$var:serverIp}}' to '127.0.0.1' in '{{$var:serverIp}}'
2025-06-30T09:38:22.5480042+05:30 [Information] Resolved template '{{$var:serverIp}}' to '127.0.0.1'
2025-06-30T09:38:22.5480795+05:30 [Information] Processing $var: path 'serverPort'
2025-06-30T09:38:22.5481259+05:30 [Information] Checking if 'serverPort' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:22.5481676+05:30 [Information] Resolving path 'serverPort' using global variables.
2025-06-30T09:38:22.5482092+05:30 [Information] Resolved JPath '$var:serverPort' to '60606'
2025-06-30T09:38:22.5482460+05:30 [Information] Resolved template pattern '{{$var:serverPort}}' to '60606' in '{{$var:serverPort}}'
2025-06-30T09:38:22.5482822+05:30 [Information] Resolved template '{{$var:serverPort}}' to '60606'
2025-06-30T09:38:22.5485585+05:30 [Information] Set Value to Step '0fddebd5-e084-481d-ab0a-be751378f5c8' input:
{
  "ServerIp": "127.0.0.1",
  "ServerPort": 60606,
  "Username": "admin",
  "Password": "admin"
}
2025-06-30T09:38:24.5689838+05:30 [Information] Failed to execute step 'Step 1: Connect to the MBusClient with admin credentials': Failed to execute step : Cannot connect to 127.0.0.1:60606.
2025-06-30T09:38:24.5699801+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:38:24.5706819+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:38:24.5715217+05:30 [Information] No performance data found for step 'Step 1: Connect to the MBusClient with admin credentials' (mbus)
2025-06-30T09:38:24.5741977+05:30 [Error] Failed to execute step : Cannot connect to 127.0.0.1:60606.
2025-06-30T09:38:24.5745128+05:30 [Error] [ FAILED   ]
2025-06-30T09:38:24.5751960+05:30 [Information] [----------] 5 steps from ProtoFieldSetters test (2031 ms total)
