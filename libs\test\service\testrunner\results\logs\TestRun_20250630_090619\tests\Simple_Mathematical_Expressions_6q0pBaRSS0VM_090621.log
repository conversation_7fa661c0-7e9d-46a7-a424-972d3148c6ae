2025-06-30T09:06:21.5394831+05:30 [Information] [----------] 6 steps from Simple Mathematical Expressions Demo
2025-06-30T09:06:21.5410839+05:30 [Information] Set Value to Step '5e25a74c-2210-46ce-8289-94975cdb69a7' name:
"Fast Baseline Operation"
2025-06-30T09:06:21.5412521+05:30 [Information] [ RUN      ] Simple Mathematical Expressions Demo > Fast Baseline Operation
2025-06-30T09:06:21.5417558+05:30 [Information] Executing single instance of step 'Fast Baseline Operation'.
2025-06-30T09:06:21.5420513+05:30 [Information] Executing step 'Fast Baseline Operation' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "5e25a74c-2210-46ce-8289-94975cdb69a7",
        "tables": {},
        "variables": {},
        "name": "Fast Baseline Operation"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaselineThreshold": 100,
      "MaxRegressionPercent": 150,
      "BufferTime": 50,
      "FastThreshold": 5000,
      "SlowThreshold": 10000,
      "MathBaseline": 100
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:06:21.5436909+05:30 [Information] Set Value to Step '5e25a74c-2210-46ce-8289-94975cdb69a7' input:
{
  "command": "ping",
  "arguments": [
    "127.0.0.1",
    "-n",
    "1"
  ],
  "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
  "printOutput": false
}
2025-06-30T09:06:21.5439249+05:30 [Information] Delaying step execution for 50ms
2025-06-30T09:06:21.6452389+05:30 [Information] Set Value to Step '5e25a74c-2210-46ce-8289-94975cdb69a7' output:
{
  "stdout": "Pinging 127.0.0.1 with 32 bytes of data:\r\nReply from 127.0.0.1: bytes=32 time<1ms TTL=128\r\nPing statistics for 127.0.0.1:\r\n    Packets: Sent = 1, Received = 1, Lost = 0 (0% loss),\r\nApproximate round trip times in milli-seconds:\r\n    Minimum = 0ms, Maximum = 0ms, Average = 0ms",
  "stderr": "",
  "exitCode": 0
}
2025-06-30T09:06:21.6454786+05:30 [Information] Resolving path 'performance.executionTimeMs' using global context.
2025-06-30T09:06:21.6455912+05:30 [Information] Resolved JPath 'performance.executionTimeMs' to ''
2025-06-30T09:06:21.6456911+05:30 [Information] Set Value to Step '5e25a74c-2210-46ce-8289-94975cdb69a7' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T03:36:21.5417505Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "cmd"
}
2025-06-30T09:06:21.6464815+05:30 [Information] Run Asserter : {"Expression1":{"Value":"0","ErrorMesage":"Ping command failed"},"Expression2":{"JPath":"$curStep:$.output.exitCode","ErrorMesage":"Ping command failed"},"ErrorMesage":"Ping command failed"}
2025-06-30T09:06:21.6466181+05:30 [Information] Resolving path '$.output.exitCode' using $curStep:
2025-06-30T09:06:21.6467119+05:30 [Information] Resolved JPath '$curStep:$.output.exitCode' to '0'
2025-06-30T09:06:21.6477721+05:30 [Information] Run Asserter : {"_lessExpression":{"Expression1":{"Value":"0","ErrorMesage":"Performance data not available"},"Expression2":{"JPath":"$curStep:performance.executionTimeMs","ErrorMesage":"Performance data not available"},"ErrorMesage":null},"Expression1":{"Value":"0","ErrorMesage":"Performance data not available"},"Expression2":{"JPath":"$curStep:performance.executionTimeMs","ErrorMesage":"Performance data not available"},"ErrorMesage":"Performance data not available"}
2025-06-30T09:06:21.6479277+05:30 [Information] Resolving path 'performance.executionTimeMs' using $curStep:
2025-06-30T09:06:21.6480220+05:30 [Information] Resolved JPath '$curStep:performance.executionTimeMs' to '0'
2025-06-30T09:06:21.6483824+05:30 [Information] Set Value to Step '5e25a74c-2210-46ce-8289-94975cdb69a7' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 104,
  "startTime": "2025-06-30T03:36:21.5417505Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "cmd"
}
2025-06-30T09:06:21.6485393+05:30 [Information] Step 'Fast Baseline Operation' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "5e25a74c-2210-46ce-8289-94975cdb69a7",
        "tables": {},
        "variables": {},
        "name": "Fast Baseline Operation",
        "input": {
          "command": "ping",
          "arguments": [
            "127.0.0.1",
            "-n",
            "1"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Pinging 127.0.0.1 with 32 bytes of data:\r\nReply from 127.0.0.1: bytes=32 time<1ms TTL=128\r\nPing statistics for 127.0.0.1:\r\n    Packets: Sent = 1, Received = 1, Lost = 0 (0% loss),\r\nApproximate round trip times in milli-seconds:\r\n    Minimum = 0ms, Maximum = 0ms, Average = 0ms",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 104,
          "startTime": "2025-06-30T03:36:21.5417505Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaselineThreshold": 100,
      "MaxRegressionPercent": 150,
      "BufferTime": 50,
      "FastThreshold": 5000,
      "SlowThreshold": 10000,
      "MathBaseline": 100
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:06:21.6486143+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:06:21.6486773+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:06:21.6487995+05:30 [Information] No performance data found for step 'Fast Baseline Operation' (cmd)
2025-06-30T09:06:21.6488523+05:30 [Information] [ OK       ]
2025-06-30T09:06:21.6491526+05:30 [Information] Set Value to Step '56061c05-cf88-4c86-b5d7-8c018c0f24f4' name:
"Regression Threshold Test"
2025-06-30T09:06:21.6492782+05:30 [Information] [ RUN      ] Simple Mathematical Expressions Demo > Regression Threshold Test
2025-06-30T09:06:21.6495774+05:30 [Information] Executing single instance of step 'Regression Threshold Test'.
2025-06-30T09:06:21.6497026+05:30 [Information] Executing step 'Regression Threshold Test' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "5e25a74c-2210-46ce-8289-94975cdb69a7",
        "tables": {},
        "variables": {},
        "name": "Fast Baseline Operation",
        "input": {
          "command": "ping",
          "arguments": [
            "127.0.0.1",
            "-n",
            "1"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Pinging 127.0.0.1 with 32 bytes of data:\r\nReply from 127.0.0.1: bytes=32 time<1ms TTL=128\r\nPing statistics for 127.0.0.1:\r\n    Packets: Sent = 1, Received = 1, Lost = 0 (0% loss),\r\nApproximate round trip times in milli-seconds:\r\n    Minimum = 0ms, Maximum = 0ms, Average = 0ms",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 104,
          "startTime": "2025-06-30T03:36:21.5417505Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "56061c05-cf88-4c86-b5d7-8c018c0f24f4",
        "tables": {},
        "variables": {},
        "name": "Regression Threshold Test"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaselineThreshold": 100,
      "MaxRegressionPercent": 150,
      "BufferTime": 50,
      "FastThreshold": 5000,
      "SlowThreshold": 10000,
      "MathBaseline": 100
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:06:21.6498661+05:30 [Information] Set Value to Step '56061c05-cf88-4c86-b5d7-8c018c0f24f4' input:
{
  "command": "ping",
  "arguments": [
    "127.0.0.1",
    "-n",
    "2"
  ],
  "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
  "printOutput": false
}
2025-06-30T09:06:21.6499479+05:30 [Information] Delaying step execution for 100ms
2025-06-30T09:06:22.7887291+05:30 [Information] Set Value to Step '56061c05-cf88-4c86-b5d7-8c018c0f24f4' output:
{
  "stdout": "Pinging 127.0.0.1 with 32 bytes of data:\r\nReply from 127.0.0.1: bytes=32 time<1ms TTL=128\r\nReply from 127.0.0.1: bytes=32 time<1ms TTL=128\r\nPing statistics for 127.0.0.1:\r\n    Packets: Sent = 2, Received = 2, Lost = 0 (0% loss),\r\nApproximate round trip times in milli-seconds:\r\n    Minimum = 0ms, Maximum = 0ms, Average = 0ms",
  "stderr": "",
  "exitCode": 0
}
2025-06-30T09:06:22.7892405+05:30 [Information] Set Value to Step '56061c05-cf88-4c86-b5d7-8c018c0f24f4' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T03:36:21.6495746Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "cmd"
}
2025-06-30T09:06:22.7894661+05:30 [Information] Run Asserter : {"Expression1":{"Value":"0","ErrorMesage":"Ping command failed"},"Expression2":{"JPath":"$curStep:$.output.exitCode","ErrorMesage":"Ping command failed"},"ErrorMesage":"Ping command failed"}
2025-06-30T09:06:22.7896135+05:30 [Information] Resolving path '$.output.exitCode' using $curStep:
2025-06-30T09:06:22.7897428+05:30 [Information] Resolved JPath '$curStep:$.output.exitCode' to '0'
2025-06-30T09:06:22.7898691+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$var:MathBaseline * $var:MaxRegressionPercent / 100}}","ErrorMesage":"Performance regression detected: {{$curStep:performance.executionTimeMs}}ms vs allowed {{$var:MathBaseline * $var:MaxRegressionPercent / 100}}ms ({{$var:MathBaseline}}ms * {{$var:MaxRegressionPercent}}% / 100)"},"Expression2":{"JPath":"$curStep:performance.executionTimeMs","ErrorMesage":"Performance regression detected: {{$curStep:performance.executionTimeMs}}ms vs allowed {{$var:MathBaseline * $var:MaxRegressionPercent / 100}}ms ({{$var:MathBaseline}}ms * {{$var:MaxRegressionPercent}}% / 100)"},"ErrorMesage":"Performance regression detected: {{$curStep:performance.executionTimeMs}}ms vs allowed {{$var:MathBaseline * $var:MaxRegressionPercent / 100}}ms ({{$var:MathBaseline}}ms * {{$var:MaxRegressionPercent}}% / 100)"}
2025-06-30T09:06:22.7899909+05:30 [Information] Processing $var: path 'MathBaseline * $var:MaxRegressionPercent / 100'
2025-06-30T09:06:22.7900674+05:30 [Information] Checking if 'MathBaseline * $var:MaxRegressionPercent / 100' is mathematical expression: hasArithmetic=True, hasArray=False, hasProperty=False, result=True
2025-06-30T09:06:22.7901173+05:30 [Information] Detected mathematical expression in $var: 'MathBaseline * $var:MaxRegressionPercent / 100'
2025-06-30T09:06:22.7903661+05:30 [Information] Evaluating mathematical expression: 'MathBaseline * $var:MaxRegressionPercent / 100'
2025-06-30T09:06:22.7906063+05:30 [Information] Resolving variables in expression: 'MathBaseline * $var:MaxRegressionPercent / 100'
2025-06-30T09:06:22.7909918+05:30 [Information] Resolved variable 'MaxRegressionPercent' from global variables: 150
2025-06-30T09:06:22.7912395+05:30 [Information] Resolved variable 'MathBaseline' from global variables: 100
2025-06-30T09:06:22.7913395+05:30 [Information] Expression after variable resolution: '100 * 150 / 100'
2025-06-30T09:06:22.7914039+05:30 [Information] Expression after variable resolution: '100 * 150 / 100'
2025-06-30T09:06:22.7945574+05:30 [Information] Mathematical expression 'MathBaseline * $var:MaxRegressionPercent / 100' evaluated to: 150
2025-06-30T09:06:22.7947337+05:30 [Information] Mathematical expression result: '150'
2025-06-30T09:06:22.7948293+05:30 [Information] Resolved JPath '$var:MathBaseline * $var:MaxRegressionPercent / 100' to '150'
2025-06-30T09:06:22.7948868+05:30 [Information] Resolved template pattern '{{$var:MathBaseline * $var:MaxRegressionPercent / 100}}' to '150' in '{{$var:MathBaseline * $var:MaxRegressionPercent / 100}}'
2025-06-30T09:06:22.7951500+05:30 [Information] Resolved template '{{$var:MathBaseline * $var:MaxRegressionPercent / 100}}' to '150'
2025-06-30T09:06:22.7952669+05:30 [Information] Resolving path 'performance.executionTimeMs' using $curStep:
2025-06-30T09:06:22.7953430+05:30 [Information] Resolved JPath '$curStep:performance.executionTimeMs' to '0'
2025-06-30T09:06:22.7954466+05:30 [Error] Asserter error : {"Expression1":{"Value":"{{$var:MathBaseline * $var:MaxRegressionPercent / 100}}","ErrorMesage":"Performance regression detected: {{$curStep:performance.executionTimeMs}}ms vs allowed {{$var:MathBaseline * $var:MaxRegressionPercent / 100}}ms ({{$var:MathBaseline}}ms * {{$var:MaxRegressionPercent}}% / 100)"},"Expression2":{"JPath":"$curStep:performance.executionTimeMs","ErrorMesage":"Performance regression detected: {{$curStep:performance.executionTimeMs}}ms vs allowed {{$var:MathBaseline * $var:MaxRegressionPercent / 100}}ms ({{$var:MathBaseline}}ms * {{$var:MaxRegressionPercent}}% / 100)"},"ErrorMesage":"Performance regression detected: {{$curStep:performance.executionTimeMs}}ms vs allowed {{$var:MathBaseline * $var:MaxRegressionPercent / 100}}ms ({{$var:MathBaseline}}ms * {{$var:MaxRegressionPercent}}% / 100)"}
2025-06-30T09:06:22.7960424+05:30 [Error] Assert Failure : Less(150, 0)
2025-06-30T09:06:22.7963174+05:30 [Information] Failed to execute step 'Regression Threshold Test': Performance regression detected: {{$curStep:performance.executionTimeMs}}ms vs allowed {{$var:MathBaseline * $var:MaxRegressionPercent / 100}}ms ({{$var:MathBaseline}}ms * {{$var:MaxRegressionPercent}}% / 100)
2025-06-30T09:06:22.7964132+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:06:22.7964813+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:06:22.7965277+05:30 [Information] No performance data found for step 'Regression Threshold Test' (cmd)
2025-06-30T09:06:22.7967721+05:30 [Error] Performance regression detected: {{$curStep:performance.executionTimeMs}}ms vs allowed {{$var:MathBaseline * $var:MaxRegressionPercent / 100}}ms ({{$var:MathBaseline}}ms * {{$var:MaxRegressionPercent}}% / 100)
2025-06-30T09:06:22.7969686+05:30 [Error] [ FAILED   ]
2025-06-30T09:06:22.7973796+05:30 [Information] [----------] 6 steps from Simple Mathematical Expressions Demo (1257 ms total)
