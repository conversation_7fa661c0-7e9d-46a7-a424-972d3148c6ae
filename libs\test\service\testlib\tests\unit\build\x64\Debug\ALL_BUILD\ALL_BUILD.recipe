﻿<?xml version="1.0" encoding="utf-8"?>
<Project>
  <ProjectOutputs>
    <ProjectOutput>
      <FullPath>D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\build\x64\Debug\ZERO_CHECK</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\build\testlib\cocshmercur\mercuryswigcs\mercury\cocppbbclie\libsxplatfo\x64\Debug\xplatform</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\build\Debug\mercury_csharp_swig.dll</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\build\x64\Debug\ALL_BUILD</FullPath>
    </ProjectOutput>
  </ProjectOutputs>
  <ContentFiles />
  <SatelliteDlls />
  <NonRecipeFileRefs>
    <NonRecipeFileRef>
      <FullPath>D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\build\testlib\apicsharp\interop_api_csharp.csproj</FullPath>
    </NonRecipeFileRef>
    <NonRecipeFileRef>
      <FullPath>D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\build\testlib\cocshmercur\mercury_csharp.csproj</FullPath>
    </NonRecipeFileRef>
    <NonRecipeFileRef>
      <FullPath>D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\build\testlib\testlib.csproj</FullPath>
    </NonRecipeFileRef>
    <NonRecipeFileRef>
      <FullPath>D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\build\testlib_unit_tests.csproj</FullPath>
    </NonRecipeFileRef>
  </NonRecipeFileRefs>
</Project>