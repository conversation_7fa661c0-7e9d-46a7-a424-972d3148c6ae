﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{7D430EE9-FF7A-3670-86C5-1227BA36F89A}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.22621.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>mercury_csharp_swig</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\build\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">mercury_csharp_swig.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">mercury_csharp_swig</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\build\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">mercury_csharp_swig.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">mercury_csharp_swig</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
  </PropertyGroup>
  <PropertyGroup />
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\kibisoft\workarea\git\interop\libs\comm\cpp\mercury\swig\csharp;D:\kibisoft\workarea\git\interop\libs\comm\cpp\mercury\swig\csharp\..\..\..\..\..;D:\kibisoft\workarea\git\interop\libs;D:\kibisoft\workarea\git\interop\libs\comm\cpp\bbclient\..\..\..;D:\kibisoft\workarea\git\interop\libs\3rdparty\openssl-3.0.15\win\x64\release\include;D:\kibisoft\workarea\git\interop\libs\xplatform\include;D:\kibisoft\workarea\git\interop\libs\3rdparty\tiny-process-library\include;D:\kibisoft\workarea\git\interop\libs\comm\cpp\boarder\..;D:\kibisoft\workarea\git\interop\libs\comm\cpp\timer\..\..\..;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4127</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <UndefinePreprocessorDefinitions>GetCurrentTime</UndefinePreprocessorDefinitions>
      <UseFullPaths>false</UseFullPaths>
      <UseStandardPreprocessor>true</UseStandardPreprocessor>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;GIT_COMMIT_COUNT=3186;_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR;NOMINMAX;_CRT_SECURE_NO_WARNINGS;bongobongo;CMAKE_INTDIR="Debug";mercury_csharp_swig_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;GIT_COMMIT_COUNT=3186;_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR;NOMINMAX;_CRT_SECURE_NO_WARNINGS;bongobongo;CMAKE_INTDIR=\"Debug\";mercury_csharp_swig_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\kibisoft\workarea\git\interop\libs\comm\cpp\mercury\swig\csharp;D:\kibisoft\workarea\git\interop\libs\comm\cpp\mercury\swig\csharp\..\..\..\..\..;D:\kibisoft\workarea\git\interop\libs;D:\kibisoft\workarea\git\interop\libs\comm\cpp\bbclient\..\..\..;D:\kibisoft\workarea\git\interop\libs\3rdparty\openssl-3.0.15\win\x64\release\include;D:\kibisoft\workarea\git\interop\libs\xplatform\include;D:\kibisoft\workarea\git\interop\libs\3rdparty\tiny-process-library\include;D:\kibisoft\workarea\git\interop\libs\comm\cpp\boarder\..;D:\kibisoft\workarea\git\interop\libs\comm\cpp\timer\..\..\..;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\kibisoft\workarea\git\interop\libs\comm\cpp\mercury\swig\csharp;D:\kibisoft\workarea\git\interop\libs\comm\cpp\mercury\swig\csharp\..\..\..\..\..;D:\kibisoft\workarea\git\interop\libs;D:\kibisoft\workarea\git\interop\libs\comm\cpp\bbclient\..\..\..;D:\kibisoft\workarea\git\interop\libs\3rdparty\openssl-3.0.15\win\x64\release\include;D:\kibisoft\workarea\git\interop\libs\xplatform\include;D:\kibisoft\workarea\git\interop\libs\3rdparty\tiny-process-library\include;D:\kibisoft\workarea\git\interop\libs\comm\cpp\boarder\..;D:\kibisoft\workarea\git\interop\libs\comm\cpp\timer\..\..\..;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>mercury\Debug\mercury.lib;mercury\cocppbbclie\Debug\bbclient.lib;D:\kibisoft\workarea\git\interop\libs\3rdparty\openssl-3.0.15\win\x64\debug\lib\libssl_static.lib;D:\kibisoft\workarea\git\interop\libs\3rdparty\openssl-3.0.15\win\x64\debug\lib\libcrypto_static.lib;Ws2_32.lib;mercury\cocppbbclie\cocppboarde\Debug\boarder.lib;mercury\cocpptimer\Debug\timer.lib;mercury\cocppbbclie\libsxplatfo\li3rdtinypr\Debug\tiny_proc_lib.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /ignore:4099</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/kibisoft/workarea/git/interop/libs/test/service/testlib/tests/unit/build/Debug/mercury_csharp_swig.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/kibisoft/workarea/git/interop/libs/test/service/testlib/tests/unit/build/Debug/mercury_csharp_swig.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\kibisoft\workarea\git\interop\libs\comm\cpp\mercury\swig\csharp;D:\kibisoft\workarea\git\interop\libs\comm\cpp\mercury\swig\csharp\..\..\..\..\..;D:\kibisoft\workarea\git\interop\libs;D:\kibisoft\workarea\git\interop\libs\comm\cpp\bbclient\..\..\..;D:\kibisoft\workarea\git\interop\libs\3rdparty\openssl-3.0.15\win\x64\release\include;D:\kibisoft\workarea\git\interop\libs\xplatform\include;D:\kibisoft\workarea\git\interop\libs\3rdparty\tiny-process-library\include;D:\kibisoft\workarea\git\interop\libs\comm\cpp\boarder\..;D:\kibisoft\workarea\git\interop\libs\comm\cpp\timer\..\..\..;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DisableSpecificWarnings>4127</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <UndefinePreprocessorDefinitions>GetCurrentTime</UndefinePreprocessorDefinitions>
      <UseFullPaths>false</UseFullPaths>
      <UseStandardPreprocessor>true</UseStandardPreprocessor>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;GIT_COMMIT_COUNT=3186;_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR;NOMINMAX;_CRT_SECURE_NO_WARNINGS;bongobongo;CMAKE_INTDIR="Release";mercury_csharp_swig_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;GIT_COMMIT_COUNT=3186;_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR;NOMINMAX;_CRT_SECURE_NO_WARNINGS;bongobongo;CMAKE_INTDIR=\"Release\";mercury_csharp_swig_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\kibisoft\workarea\git\interop\libs\comm\cpp\mercury\swig\csharp;D:\kibisoft\workarea\git\interop\libs\comm\cpp\mercury\swig\csharp\..\..\..\..\..;D:\kibisoft\workarea\git\interop\libs;D:\kibisoft\workarea\git\interop\libs\comm\cpp\bbclient\..\..\..;D:\kibisoft\workarea\git\interop\libs\3rdparty\openssl-3.0.15\win\x64\release\include;D:\kibisoft\workarea\git\interop\libs\xplatform\include;D:\kibisoft\workarea\git\interop\libs\3rdparty\tiny-process-library\include;D:\kibisoft\workarea\git\interop\libs\comm\cpp\boarder\..;D:\kibisoft\workarea\git\interop\libs\comm\cpp\timer\..\..\..;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\kibisoft\workarea\git\interop\libs\comm\cpp\mercury\swig\csharp;D:\kibisoft\workarea\git\interop\libs\comm\cpp\mercury\swig\csharp\..\..\..\..\..;D:\kibisoft\workarea\git\interop\libs;D:\kibisoft\workarea\git\interop\libs\comm\cpp\bbclient\..\..\..;D:\kibisoft\workarea\git\interop\libs\3rdparty\openssl-3.0.15\win\x64\release\include;D:\kibisoft\workarea\git\interop\libs\xplatform\include;D:\kibisoft\workarea\git\interop\libs\3rdparty\tiny-process-library\include;D:\kibisoft\workarea\git\interop\libs\comm\cpp\boarder\..;D:\kibisoft\workarea\git\interop\libs\comm\cpp\timer\..\..\..;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>mercury\Release\mercury.lib;mercury\cocppbbclie\Release\bbclient.lib;D:\kibisoft\workarea\git\interop\libs\3rdparty\openssl-3.0.15\win\x64\release\lib\libssl_static.lib;D:\kibisoft\workarea\git\interop\libs\3rdparty\openssl-3.0.15\win\x64\release\lib\libcrypto_static.lib;Ws2_32.lib;mercury\cocppbbclie\cocppboarde\Release\boarder.lib;mercury\cocpptimer\Release\timer.lib;mercury\cocppbbclie\libsxplatfo\li3rdtinypr\Release\tiny_proc_lib.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /ignore:4099</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/kibisoft/workarea/git/interop/libs/test/service/testlib/tests/unit/build/Release/mercury_csharp_swig.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/kibisoft/workarea/git/interop/libs/test/service/testlib/tests/unit/build/Release/mercury_csharp_swig.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\kibisoft\workarea\git\interop\libs\comm\cpp\mercury\swig\csharp\mercury.i">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Swig compile mercury.i for csharp</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/kibisoft/workarea/git/interop/libs/test/service/testlib/tests/unit/build/testlib/cocshmercur/mercuryswigcs/CMakeFiles/mercury_csharp_swig.dir D:/kibisoft/workarea/git/interop/libs/test/service/testlib/tests/unit/build/testlib/cocshmercur/mercuryswigcs D:/kibisoft/workarea/git/interop/libs/test/service/testlib/tests/unit/build/testlib/cocshmercur/mercuryswigcs/CMakeFiles/mercury_csharp_swig.dir
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E env SWIG_LIB=D:/kibisoft/Instllers/tools/swigwin-4.2.1/Lib D:/kibisoft/Instllers/tools/swigwin-4.2.1/swig.exe -csharp -namespace Alcon.Interop.Comm.Mercury.Swig -outdir D:/kibisoft/workarea/git/interop/libs/test/service/testlib/tests/unit/build/testlib/cocshmercur/mercuryswigcs -c++ -dllimport mercury_csharp_swig -o D:/kibisoft/workarea/git/interop/libs/test/service/testlib/tests/unit/build/testlib/cocshmercur/mercuryswigcs/CMakeFiles/mercury_csharp_swig.dir/mercuryCSHARP_wrap.cxx D:/kibisoft/workarea/git/interop/libs/comm/cpp/mercury/swig/csharp/mercury.i
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\build\testlib\cocshmercur\mercuryswigcs\CMakeFiles\mercury_csharp_swig.dir\mercuryCSHARP_wrap.cxx;D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\build\testlib\cocshmercur\mercuryswigcs\SwigMercuryModule.cs;D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\build\testlib\cocshmercur\mercuryswigcs\SwigMercuryModulePINVOKE.cs</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Swig compile mercury.i for csharp</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/kibisoft/workarea/git/interop/libs/test/service/testlib/tests/unit/build/testlib/cocshmercur/mercuryswigcs/CMakeFiles/mercury_csharp_swig.dir D:/kibisoft/workarea/git/interop/libs/test/service/testlib/tests/unit/build/testlib/cocshmercur/mercuryswigcs D:/kibisoft/workarea/git/interop/libs/test/service/testlib/tests/unit/build/testlib/cocshmercur/mercuryswigcs/CMakeFiles/mercury_csharp_swig.dir
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E env SWIG_LIB=D:/kibisoft/Instllers/tools/swigwin-4.2.1/Lib D:/kibisoft/Instllers/tools/swigwin-4.2.1/swig.exe -csharp -namespace Alcon.Interop.Comm.Mercury.Swig -outdir D:/kibisoft/workarea/git/interop/libs/test/service/testlib/tests/unit/build/testlib/cocshmercur/mercuryswigcs -c++ -dllimport mercury_csharp_swig -o D:/kibisoft/workarea/git/interop/libs/test/service/testlib/tests/unit/build/testlib/cocshmercur/mercuryswigcs/CMakeFiles/mercury_csharp_swig.dir/mercuryCSHARP_wrap.cxx D:/kibisoft/workarea/git/interop/libs/comm/cpp/mercury/swig/csharp/mercury.i
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\build\testlib\cocshmercur\mercuryswigcs\CMakeFiles\mercury_csharp_swig.dir\mercuryCSHARP_wrap.cxx;D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\build\testlib\cocshmercur\mercuryswigcs\SwigMercuryModule.cs;D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\build\testlib\cocshmercur\mercuryswigcs\SwigMercuryModulePINVOKE.cs</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\kibisoft\workarea\git\interop\libs\comm\cpp\mercury\swig\csharp\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule D:/kibisoft/workarea/git/interop/libs/comm/cpp/mercury/swig/csharp/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/kibisoft/workarea/git/interop/libs/test/service/testlib/tests/unit -BD:/kibisoft/workarea/git/interop/libs/test/service/testlib/tests/unit/build --check-stamp-file D:/kibisoft/workarea/git/interop/libs/test/service/testlib/tests/unit/build/testlib/cocshmercur/mercuryswigcs/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Program Files\CMake\share\cmake-4.0\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CheckIncludeFileCXX.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CheckTypeSize.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindSWIG.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\UseSWIG.cmake;D:\kibisoft\workarea\git\interop\cmake\Globals.cmake;D:\kibisoft\workarea\git\interop\cmake\SetupSwig.cmake;D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\build\CMakeFiles\cmake.verify_globs;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\build\testlib\cocshmercur\mercuryswigcs\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule D:/kibisoft/workarea/git/interop/libs/comm/cpp/mercury/swig/csharp/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/kibisoft/workarea/git/interop/libs/test/service/testlib/tests/unit -BD:/kibisoft/workarea/git/interop/libs/test/service/testlib/tests/unit/build --check-stamp-file D:/kibisoft/workarea/git/interop/libs/test/service/testlib/tests/unit/build/testlib/cocshmercur/mercuryswigcs/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Program Files\CMake\share\cmake-4.0\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CheckIncludeFileCXX.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CheckTypeSize.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindSWIG.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\UseSWIG.cmake;D:\kibisoft\workarea\git\interop\cmake\Globals.cmake;D:\kibisoft\workarea\git\interop\cmake\SetupSwig.cmake;D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\build\CMakeFiles\cmake.verify_globs;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\build\testlib\cocshmercur\mercuryswigcs\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\build\testlib\cocshmercur\mercuryswigcs\CMakeFiles\mercury_csharp_swig.dir\mercuryCSHARP_wrap.cxx" />
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\build\testlib\cocshmercur\mercuryswigcs\SwigMercuryModule.cs" />
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\build\testlib\cocshmercur\mercuryswigcs\SwigMercuryModulePINVOKE.cs" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\build\ZERO_CHECK.vcxproj">
      <Project>{61469607-E4E8-314E-BE85-14884F74CA7F}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\build\testlib\cocshmercur\mercuryswigcs\mercury\cocppbbclie\bbclient.vcxproj">
      <Project>{3A01BC59-6A19-3D6A-8774-B1EE198C8334}</Project>
      <Name>bbclient</Name>
    </ProjectReference>
    <ProjectReference Include="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\build\testlib\cocshmercur\mercuryswigcs\mercury\cocppbbclie\cocppboarde\boarder.vcxproj">
      <Project>{7F1CCF46-8232-3DF0-8DB8-1AA7D869E66E}</Project>
      <Name>boarder</Name>
    </ProjectReference>
    <ProjectReference Include="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\build\testlib\cocshmercur\mercuryswigcs\mercury\mercury.vcxproj">
      <Project>{ED2D086C-787F-3B97-914F-662DD518B014}</Project>
      <Name>mercury</Name>
    </ProjectReference>
    <ProjectReference Include="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\build\testlib\cocshmercur\mercuryswigcs\mercury\cocpptimer\timer.vcxproj">
      <Project>{B26574C5-D2CF-3F5F-8AD7-EB6CCD840067}</Project>
      <Name>timer</Name>
    </ProjectReference>
    <ProjectReference Include="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\build\testlib\cocshmercur\mercuryswigcs\mercury\cocppbbclie\libsxplatfo\li3rdtinypr\tiny_proc_lib.vcxproj">
      <Project>{0C975849-2C64-3705-A7CB-5CD13CB273B2}</Project>
      <Name>tiny_proc_lib</Name>
    </ProjectReference>
    <ProjectReference Include="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\build\testlib\cocshmercur\mercuryswigcs\mercury\cocppbbclie\libsxplatfo\xplatform.vcxproj">
      <Project>{109650CC-FCF9-30DD-9050-56C1B7224A03}</Project>
      <Name>xplatform</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>