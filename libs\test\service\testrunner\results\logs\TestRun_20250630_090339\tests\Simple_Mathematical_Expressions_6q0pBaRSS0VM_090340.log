2025-06-30T09:03:40.5584696+05:30 [Information] [----------] 6 steps from Simple Mathematical Expressions Demo
2025-06-30T09:03:40.5599419+05:30 [Information] Set Value to Step 'ed4ee968-b8c6-4527-b8f3-9b83503e5393' name:
"Fast Baseline Operation"
2025-06-30T09:03:40.5601659+05:30 [Information] [ RUN      ] Simple Mathematical Expressions Demo > Fast Baseline Operation
2025-06-30T09:03:40.5606170+05:30 [Information] Executing single instance of step 'Fast Baseline Operation'.
2025-06-30T09:03:40.5607680+05:30 [Information] Executing step 'Fast Baseline Operation' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "ed4ee968-b8c6-4527-b8f3-9b83503e5393",
        "tables": {},
        "variables": {},
        "name": "Fast Baseline Operation"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaselineThreshold": 100,
      "MaxRegressionPercent": 150,
      "BufferTime": 50,
      "FastThreshold": 5000,
      "SlowThreshold": 10000
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:03:40.5619908+05:30 [Information] Set Value to Step 'ed4ee968-b8c6-4527-b8f3-9b83503e5393' input:
{
  "command": "ping",
  "arguments": [
    "127.0.0.1",
    "-n",
    "1"
  ],
  "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
  "printOutput": false
}
2025-06-30T09:03:40.5621982+05:30 [Information] Delaying step execution for 50ms
2025-06-30T09:03:40.6453264+05:30 [Information] Set Value to Step 'ed4ee968-b8c6-4527-b8f3-9b83503e5393' output:
{
  "stdout": "Pinging 127.0.0.1 with 32 bytes of data:\r\nReply from 127.0.0.1: bytes=32 time<1ms TTL=128\r\nPing statistics for 127.0.0.1:\r\n    Packets: Sent = 1, Received = 1, Lost = 0 (0% loss),\r\nApproximate round trip times in milli-seconds:\r\n    Minimum = 0ms, Maximum = 0ms, Average = 0ms",
  "stderr": "",
  "exitCode": 0
}
2025-06-30T09:03:40.6455593+05:30 [Information] Resolving path 'performance.executionTimeMs' using global context.
2025-06-30T09:03:40.6456666+05:30 [Information] Resolved JPath 'performance.executionTimeMs' to ''
2025-06-30T09:03:40.6457352+05:30 [Information] Resolving path '50' using global context.
2025-06-30T09:03:40.6457909+05:30 [Information] Resolved JPath '50' to ''
2025-06-30T09:03:40.6458785+05:30 [Information] Set Value to Step 'ed4ee968-b8c6-4527-b8f3-9b83503e5393' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T03:33:40.560613Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "cmd"
}
2025-06-30T09:03:40.6466155+05:30 [Information] Run Asserter : {"Expression1":{"Value":"0","ErrorMesage":"Ping command failed"},"Expression2":{"JPath":"$curStep:$.output.exitCode","ErrorMesage":"Ping command failed"},"ErrorMesage":"Ping command failed"}
2025-06-30T09:03:40.6467482+05:30 [Information] Resolving path '$.output.exitCode' using $curStep:
2025-06-30T09:03:40.6468406+05:30 [Information] Resolved JPath '$curStep:$.output.exitCode' to '0'
2025-06-30T09:03:40.6479208+05:30 [Information] Run Asserter : {"_lessExpression":{"Expression1":{"Value":"0","ErrorMesage":"Performance data not available"},"Expression2":{"JPath":"$curStep:performance.executionTimeMs","ErrorMesage":"Performance data not available"},"ErrorMesage":null},"Expression1":{"Value":"0","ErrorMesage":"Performance data not available"},"Expression2":{"JPath":"$curStep:performance.executionTimeMs","ErrorMesage":"Performance data not available"},"ErrorMesage":"Performance data not available"}
2025-06-30T09:03:40.6480791+05:30 [Information] Resolving path 'performance.executionTimeMs' using $curStep:
2025-06-30T09:03:40.6481695+05:30 [Information] Resolved JPath '$curStep:performance.executionTimeMs' to '0'
2025-06-30T09:03:40.6485149+05:30 [Information] Set Value to Step 'ed4ee968-b8c6-4527-b8f3-9b83503e5393' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 86,
  "startTime": "2025-06-30T03:33:40.560613Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "cmd"
}
2025-06-30T09:03:40.6486866+05:30 [Information] Step 'Fast Baseline Operation' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "ed4ee968-b8c6-4527-b8f3-9b83503e5393",
        "tables": {},
        "variables": {},
        "name": "Fast Baseline Operation",
        "input": {
          "command": "ping",
          "arguments": [
            "127.0.0.1",
            "-n",
            "1"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Pinging 127.0.0.1 with 32 bytes of data:\r\nReply from 127.0.0.1: bytes=32 time<1ms TTL=128\r\nPing statistics for 127.0.0.1:\r\n    Packets: Sent = 1, Received = 1, Lost = 0 (0% loss),\r\nApproximate round trip times in milli-seconds:\r\n    Minimum = 0ms, Maximum = 0ms, Average = 0ms",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 86,
          "startTime": "2025-06-30T03:33:40.560613Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaselineThreshold": 100,
      "MaxRegressionPercent": 150,
      "BufferTime": 50,
      "FastThreshold": 5000,
      "SlowThreshold": 10000
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:03:40.6487666+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:03:40.6488314+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:03:40.6488793+05:30 [Information] No performance data found for step 'Fast Baseline Operation' (cmd)
2025-06-30T09:03:40.6489216+05:30 [Information] [ OK       ]
2025-06-30T09:03:40.6492056+05:30 [Information] Set Value to Step '55644146-2447-4ae5-840b-17f8305c6ce4' name:
"Regression Threshold Test"
2025-06-30T09:03:40.6493372+05:30 [Information] [ RUN      ] Simple Mathematical Expressions Demo > Regression Threshold Test
2025-06-30T09:03:40.6495992+05:30 [Information] Executing single instance of step 'Regression Threshold Test'.
2025-06-30T09:03:40.6497305+05:30 [Information] Executing step 'Regression Threshold Test' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "ed4ee968-b8c6-4527-b8f3-9b83503e5393",
        "tables": {},
        "variables": {},
        "name": "Fast Baseline Operation",
        "input": {
          "command": "ping",
          "arguments": [
            "127.0.0.1",
            "-n",
            "1"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Pinging 127.0.0.1 with 32 bytes of data:\r\nReply from 127.0.0.1: bytes=32 time<1ms TTL=128\r\nPing statistics for 127.0.0.1:\r\n    Packets: Sent = 1, Received = 1, Lost = 0 (0% loss),\r\nApproximate round trip times in milli-seconds:\r\n    Minimum = 0ms, Maximum = 0ms, Average = 0ms",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 86,
          "startTime": "2025-06-30T03:33:40.560613Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "55644146-2447-4ae5-840b-17f8305c6ce4",
        "tables": {},
        "variables": {},
        "name": "Regression Threshold Test"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaselineThreshold": 100,
      "MaxRegressionPercent": 150,
      "BufferTime": 50,
      "FastThreshold": 5000,
      "SlowThreshold": 10000
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:03:40.6499844+05:30 [Information] Set Value to Step '55644146-2447-4ae5-840b-17f8305c6ce4' input:
{
  "command": "ping",
  "arguments": [
    "127.0.0.1",
    "-n",
    "2"
  ],
  "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
  "printOutput": false
}
2025-06-30T09:03:40.6500916+05:30 [Information] Delaying step execution for 100ms
2025-06-30T09:03:41.7949094+05:30 [Information] Set Value to Step '55644146-2447-4ae5-840b-17f8305c6ce4' output:
{
  "stdout": "Pinging 127.0.0.1 with 32 bytes of data:\r\nReply from 127.0.0.1: bytes=32 time<1ms TTL=128\r\nReply from 127.0.0.1: bytes=32 time<1ms TTL=128\r\nPing statistics for 127.0.0.1:\r\n    Packets: Sent = 2, Received = 2, Lost = 0 (0% loss),\r\nApproximate round trip times in milli-seconds:\r\n    Minimum = 0ms, Maximum = 0ms, Average = 0ms",
  "stderr": "",
  "exitCode": 0
}
2025-06-30T09:03:41.7960067+05:30 [Information] Set Value to Step '55644146-2447-4ae5-840b-17f8305c6ce4' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T03:33:40.6495966Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "cmd"
}
2025-06-30T09:03:41.7963511+05:30 [Information] Run Asserter : {"Expression1":{"Value":"0","ErrorMesage":"Ping command failed"},"Expression2":{"JPath":"$curStep:$.output.exitCode","ErrorMesage":"Ping command failed"},"ErrorMesage":"Ping command failed"}
2025-06-30T09:03:41.7965613+05:30 [Information] Resolving path '$.output.exitCode' using $curStep:
2025-06-30T09:03:41.7967040+05:30 [Information] Resolved JPath '$curStep:$.output.exitCode' to '0'
2025-06-30T09:03:41.7968490+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$var:mathBaseline * $var:MaxRegressionPercent / 100}}","ErrorMesage":"Performance regression detected: {{$curStep:performance.executionTimeMs}}ms vs allowed {{$var:mathBaseline * $var:MaxRegressionPercent / 100}}ms ({{$var:mathBaseline}}ms * {{$var:MaxRegressionPercent}}% / 100)"},"Expression2":{"JPath":"$curStep:performance.executionTimeMs","ErrorMesage":"Performance regression detected: {{$curStep:performance.executionTimeMs}}ms vs allowed {{$var:mathBaseline * $var:MaxRegressionPercent / 100}}ms ({{$var:mathBaseline}}ms * {{$var:MaxRegressionPercent}}% / 100)"},"ErrorMesage":"Performance regression detected: {{$curStep:performance.executionTimeMs}}ms vs allowed {{$var:mathBaseline * $var:MaxRegressionPercent / 100}}ms ({{$var:mathBaseline}}ms * {{$var:MaxRegressionPercent}}% / 100)"}
2025-06-30T09:03:41.7969975+05:30 [Information] Processing $var: path 'mathBaseline * $var:MaxRegressionPercent / 100'
2025-06-30T09:03:41.7971886+05:30 [Information] Checking if 'mathBaseline * $var:MaxRegressionPercent / 100' is mathematical expression: hasArithmetic=True, hasArray=False, hasProperty=False, result=True
2025-06-30T09:03:41.7973294+05:30 [Information] Detected mathematical expression in $var: 'mathBaseline * $var:MaxRegressionPercent / 100'
2025-06-30T09:03:41.7976834+05:30 [Information] Evaluating mathematical expression: 'mathBaseline * $var:MaxRegressionPercent / 100'
2025-06-30T09:03:41.7979871+05:30 [Information] Resolving variables in expression: 'mathBaseline * $var:MaxRegressionPercent / 100'
2025-06-30T09:03:41.7984689+05:30 [Information] Resolved variable 'MaxRegressionPercent' from global variables: 150
2025-06-30T09:03:41.7987515+05:30 [Information] Variable 'mathBaseline' not found, using fallback: mathBaseline
2025-06-30T09:03:41.7989147+05:30 [Information] Expression after variable resolution: 'mathBaseline * 150 / 100'
2025-06-30T09:03:41.7990238+05:30 [Information] Expression after variable resolution: 'mathBaseline * 150 / 100'
2025-06-30T09:03:41.7992655+05:30 [Information] Error evaluating mathematical expression 'mathBaseline * $var:MaxRegressionPercent / 100': Invalid characters in arithmetic expression: mathBaseline*150/100
2025-06-30T09:03:41.7994745+05:30 [Information] Mathematical expression result: 'mathBaseline * $var:MaxRegressionPercent / 100'
2025-06-30T09:03:41.7995538+05:30 [Information] Resolved JPath '$var:mathBaseline * $var:MaxRegressionPercent / 100' to 'mathBaseline * $var:MaxRegressionPercent / 100'
2025-06-30T09:03:41.7996150+05:30 [Information] Resolved template pattern '{{$var:mathBaseline * $var:MaxRegressionPercent / 100}}' to 'mathBaseline * $var:MaxRegressionPercent / 100' in '{{$var:mathBaseline * $var:MaxRegressionPercent / 100}}'
2025-06-30T09:03:41.7998476+05:30 [Information] Resolved template '{{$var:mathBaseline * $var:MaxRegressionPercent / 100}}' to 'mathBaseline * $var:MaxRegressionPercent / 100'
2025-06-30T09:03:41.7999931+05:30 [Information] Resolving path 'performance.executionTimeMs' using $curStep:
2025-06-30T09:03:41.8000742+05:30 [Information] Resolved JPath '$curStep:performance.executionTimeMs' to '0'
2025-06-30T09:03:41.8013620+05:30 [Error] Asserter error : {"Expression1":{"Value":"{{$var:mathBaseline * $var:MaxRegressionPercent / 100}}","ErrorMesage":"Performance regression detected: {{$curStep:performance.executionTimeMs}}ms vs allowed {{$var:mathBaseline * $var:MaxRegressionPercent / 100}}ms ({{$var:mathBaseline}}ms * {{$var:MaxRegressionPercent}}% / 100)"},"Expression2":{"JPath":"$curStep:performance.executionTimeMs","ErrorMesage":"Performance regression detected: {{$curStep:performance.executionTimeMs}}ms vs allowed {{$var:mathBaseline * $var:MaxRegressionPercent / 100}}ms ({{$var:mathBaseline}}ms * {{$var:MaxRegressionPercent}}% / 100)"},"ErrorMesage":"Performance regression detected: {{$curStep:performance.executionTimeMs}}ms vs allowed {{$var:mathBaseline * $var:MaxRegressionPercent / 100}}ms ({{$var:mathBaseline}}ms * {{$var:MaxRegressionPercent}}% / 100)"}
2025-06-30T09:03:41.8020754+05:30 [Error] Assert Failure : Less(mathBaseline * $var:MaxRegressionPercent / 100, 0)
2025-06-30T09:03:41.8023365+05:30 [Information] Failed to execute step 'Regression Threshold Test': Performance regression detected: {{$curStep:performance.executionTimeMs}}ms vs allowed {{$var:mathBaseline * $var:MaxRegressionPercent / 100}}ms ({{$var:mathBaseline}}ms * {{$var:MaxRegressionPercent}}% / 100)
2025-06-30T09:03:41.8024551+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:03:41.8025373+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:03:41.8025864+05:30 [Information] No performance data found for step 'Regression Threshold Test' (cmd)
2025-06-30T09:03:41.8027908+05:30 [Error] Performance regression detected: {{$curStep:performance.executionTimeMs}}ms vs allowed {{$var:mathBaseline * $var:MaxRegressionPercent / 100}}ms ({{$var:mathBaseline}}ms * {{$var:MaxRegressionPercent}}% / 100)
2025-06-30T09:03:41.8029757+05:30 [Error] [ FAILED   ]
2025-06-30T09:03:41.8033693+05:30 [Information] [----------] 6 steps from Simple Mathematical Expressions Demo (1244 ms total)
