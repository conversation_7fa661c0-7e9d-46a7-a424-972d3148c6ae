2025-06-30T09:25:55.8558045+05:30 [Information] Loading tests from D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc\examples with pattern *performance-testing-examples.yaml
2025-06-30T09:25:55.8691921+05:30 [Information] Loading test from D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc\examples\performance-testing-examples.yaml
2025-06-30T09:25:55.9551369+05:30 [Information] Global variables set to: 
2025-06-30T09:25:55.9563010+05:30 [Information] [==========] Running 1 test from Yaml files
2025-06-30T09:25:55.9574400+05:30 [Information] [------------------------------------- Test Execution -------------------------------------]
2025-06-30T09:25:55.9775306+05:30 [Information] [----------] 18 steps from Performance Testing Examples
2025-06-30T09:25:55.9803058+05:30 [Information] [ RUN      ] Performance Testing Examples > HTTP Response Time Test
2025-06-30T09:25:57.3618239+05:30 [Information] [ OK       ]
2025-06-30T09:25:57.3625354+05:30 [Information] [ RUN      ] Performance Testing Examples > Performance Baseline Establishment
2025-06-30T09:25:58.3967450+05:30 [Information] [ OK       ]
2025-06-30T09:25:58.3979469+05:30 [Information] [ RUN      ] Performance Testing Examples > Performance Regression Check
2025-06-30T09:25:59.2233218+05:30 [Information] [ OK       ]
2025-06-30T09:25:59.2241393+05:30 [Information] [ RUN      ] Performance Testing Examples > Load Testing with Controlled Request Pacing
2025-06-30T09:26:00.3014400+05:30 [Information] [ OK       ]
2025-06-30T09:26:00.3019808+05:30 [Information] [ RUN      ] Performance Testing Examples > Fast Endpoint Baseline
2025-06-30T09:26:01.3537193+05:30 [Information] [ OK       ]
2025-06-30T09:26:01.3542763+05:30 [Information] [ RUN      ] Performance Testing Examples > Complex Endpoint Comparison
2025-06-30T09:26:02.2262001+05:30 [Information] [ OK       ]
2025-06-30T09:26:02.2266815+05:30 [Information] [ RUN      ] Performance Testing Examples > Service Warm-up Performance Test
2025-06-30T09:26:02.9975226+05:30 [Information] [ OK       ]
2025-06-30T09:26:02.9979767+05:30 [Information] [ RUN      ] Performance Testing Examples > Performance Stability Test
2025-06-30T09:26:04.3080172+05:30 [Information] [ OK       ]
2025-06-30T09:26:04.3085640+05:30 [Information] [ RUN      ] Performance Testing Examples > Performance Recovery Test
2025-06-30T09:26:08.1280025+05:30 [Information] [ OK       ]
2025-06-30T09:26:08.1289695+05:30 [Information] [ RUN      ] Performance Testing Examples > Database Connection Simulation Performance Test
2025-06-30T09:26:10.2216577+05:30 [Information] [ OK       ]
2025-06-30T09:26:10.2223115+05:30 [Information] [ RUN      ] Performance Testing Examples > System Information Performance Test
2025-06-30T09:26:13.5500608+05:30 [Information] [ OK       ]
2025-06-30T09:26:13.5504869+05:30 [Information] [ RUN      ] Performance Testing Examples > Directory Listing Performance Test
2025-06-30T09:26:13.5743049+05:30 [Information] [ OK       ]
2025-06-30T09:26:13.5747240+05:30 [Information] [ RUN      ] Performance Testing Examples > Network Service Availability Performance Test
2025-06-30T09:26:15.1742607+05:30 [Information] [ OK       ]
2025-06-30T09:26:15.1749417+05:30 [Information] [ RUN      ] Performance Testing Examples > API Rate Limiting Compliance Test
2025-06-30T09:26:18.4529977+05:30 [Information] [ OK       ]
2025-06-30T09:26:18.4539474+05:30 [Information] [ RUN      ] Performance Testing Examples > Performance Baseline - Fast Operation
2025-06-30T09:26:18.4733482+05:30 [Information] [ OK       ]
2025-06-30T09:26:18.4738941+05:30 [Information] [ RUN      ] Performance Testing Examples > Performance Comparison - Slower Operation
2025-06-30T09:26:19.1500489+05:30 [Information] [ OK       ]
2025-06-30T09:26:19.1506350+05:30 [Information] [ RUN      ] Performance Testing Examples > Process Enumeration Performance Test
2025-06-30T09:26:19.4578745+05:30 [Information] [ OK       ]
2025-06-30T09:26:19.4610343+05:30 [Information] [ RUN      ] Performance Testing Examples > Mathematical Expressions Demo
2025-06-30T09:26:22.2612110+05:30 [Error] Assert Failure : Less(600, 0)
2025-06-30T09:26:22.2618498+05:30 [Error] Performance regression detected: {{$curStep:performance.executionTimeMs}}ms exceeds {{$var:BaselineResponseTime * $var:MaxRegressionPercent / 100}}ms ({{$var:BaselineResponseTime}}ms * {{$var:MaxRegressionPercent}}% / 100)
2025-06-30T09:26:22.2619720+05:30 [Error] [ FAILED   ]
2025-06-30T09:26:22.2629755+05:30 [Information] [----------] 18 steps from Performance Testing Examples (26284 ms total)
2025-06-30T09:26:22.2644519+05:30 [Information] [-------------------------------------  Test Results  -------------------------------------]
2025-06-30T09:26:22.2649474+05:30 [Information] [==========] 1 test from Yaml files ran. (26309 ms total)
2025-06-30T09:26:22.2662119+05:30 [Information] [  PASSED  ] 0 tests.
2025-06-30T09:26:22.2664488+05:30 [Error] [  FAILED  ] 1 test, listed below:
2025-06-30T09:26:22.2668140+05:30 [Error] [  FAILED  ] Performance Testing Examples
