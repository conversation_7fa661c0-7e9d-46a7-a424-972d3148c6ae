Name: "Advanced Performance Mathematics"
Description: "Advanced mathematical expressions for sophisticated performance testing scenarios"

Variables:
  BaseUrl: "https://api.example.com"
  # Performance baselines (in milliseconds)
  DatabaseQueryBaseline: "80"
  APICallBaseline: "120"
  FileProcessingBaseline: "200"
  
  # Performance multipliers and factors
  LoadFactor: "1.5"          # 50% load increase
  ConcurrencyPenalty: "1.2"  # 20% penalty for concurrent operations
  NetworkLatency: "15"       # Additional network overhead
  
  # SLA definitions
  P95Multiplier: "2.5"       # P95 should be 2.5x average
  P99Multiplier: "4.0"       # P99 should be 4x average
  
  # Business rules
  MaxUserWaitTime: "3000"    # 3 seconds max user wait
  CriticalPathBudget: "1500" # 1.5 seconds for critical operations

Steps:
  # Step 1: Database performance with load factor
  - Name: "Database Under Load"
    Input:
      Method: "POST"
      RequestUri: "{{$var:BaseUrl}}/database/query"
      Content: '{"query": "SELECT * FROM users WHERE active = true"}'
    Execution:
      DelayMs: 100
    Asserters:
      # Expected time: baseline * load factor + network latency
      - AssertLt:
          ConstExpr: "{{$var:DatabaseQueryBaseline * $var:LoadFactor + $var:NetworkLatency}}"
          JPathExpr: "$curStep:performance.executionTimeMs"
          ErrorMessage: "Database query too slow under load: {{$curStep:performance.executionTimeMs}}ms vs expected {{$var:DatabaseQueryBaseline * $var:LoadFactor + $var:NetworkLatency}}ms"
    Output:
      Store:
        actualDbTime: "performance.executionTimeMs"

  # Step 2: API call with concurrency penalty
  - Name: "Concurrent API Call"
    Input:
      Method: "GET"
      RequestUri: "{{$var:BaseUrl}}/api/user-profile"
    Execution:
      DelayMs: 120
    Asserters:
      # Expected time: (baseline + network) * concurrency penalty
      - AssertLt:
          ConstExpr: "{{$var:(APICallBaseline + $var:NetworkLatency) * $var:ConcurrencyPenalty}}"
          JPathExpr: "$curStep:performance.executionTimeMs"
          ErrorMessage: "API call exceeded concurrency expectations: {{$curStep:performance.executionTimeMs}}ms vs {{$var:(APICallBaseline + $var:NetworkLatency) * $var:ConcurrencyPenalty}}ms"
    Output:
      Store:
        actualApiTime: "performance.executionTimeMs"

  # Step 3: File processing with complex calculation
  - Name: "File Processing Pipeline"
    Input:
      Method: "POST"
      RequestUri: "{{$var:BaseUrl}}/files/process"
      Content: '{"fileSize": "1024", "format": "json"}'
    Execution:
      DelayMs: 250
    Asserters:
      # Complex calculation: baseline * load * concurrency + network + processing overhead
      - AssertLt:
          ConstExpr: "{{$var:FileProcessingBaseline * $var:LoadFactor * $var:ConcurrencyPenalty + $var:NetworkLatency + 50}}"
          JPathExpr: "$curStep:performance.executionTimeMs"
          ErrorMessage: "File processing exceeded complex threshold: {{$curStep:performance.executionTimeMs}}ms"
    Output:
      Store:
        actualFileTime: "performance.executionTimeMs"

  # Step 4: Composite operation with total time budget
  - Name: "Composite Operation"
    Input:
      Method: "POST"
      RequestUri: "{{$var:BaseUrl}}/composite/operation"
      Content: '{"includeDb": true, "includeApi": true, "includeFile": true}'
    Execution:
      DelayMs: 300
    Asserters:
      # Total time should not exceed sum of individual operations + overhead
      - AssertLt:
          ConstExpr: "{{$var:actualDbTime + actualApiTime + actualFileTime + 100}}"
          JPathExpr: "$curStep:performance.executionTimeMs"
          ErrorMessage: "Composite operation exceeded sum of parts: {{$curStep:performance.executionTimeMs}}ms vs {{$var:actualDbTime + actualApiTime + actualFileTime + 100}}ms"
      
      # Should also meet critical path budget
      - AssertLt:
          ConstExpr: "{{$var:CriticalPathBudget}}"
          JPathExpr: "$curStep:performance.executionTimeMs"
          ErrorMessage: "Critical path budget exceeded: {{$curStep:performance.executionTimeMs}}ms > {{$var:CriticalPathBudget}}ms"

  # Step 5: P95 performance validation
  - Name: "P95 Performance Check"
    Input:
      Method: "GET"
      RequestUri: "{{$var:BaseUrl}}/performance/p95-test"
    Execution:
      DelayMs: 180
    Asserters:
      # P95 calculation: average baseline * P95 multiplier
      - AssertLt:
          ConstExpr: "{{$var:(DatabaseQueryBaseline + APICallBaseline + FileProcessingBaseline) / 3 * $var:P95Multiplier}}"
          JPathExpr: "$curStep:performance.executionTimeMs"
          ErrorMessage: "P95 performance target missed: {{$curStep:performance.executionTimeMs}}ms"

  # Step 6: User experience validation
  - Name: "User Experience Validation"
    Input:
      Method: "GET"
      RequestUri: "{{$var:BaseUrl}}/user/dashboard"
    Execution:
      DelayMs: 400
    Asserters:
      # User wait time calculation with tolerance
      - AssertLt:
          ConstExpr: "{{$var:MaxUserWaitTime * 0.8}}"  # 80% of max wait time for good UX
          JPathExpr: "$curStep:performance.executionTimeMs"
          ErrorMessage: "User experience degraded: {{$curStep:performance.executionTimeMs}}ms vs target {{$var:MaxUserWaitTime * 0.8}}ms"
      
      # Absolute maximum
      - AssertLt:
          ConstExpr: "{{$var:MaxUserWaitTime}}"
          JPathExpr: "$curStep:performance.executionTimeMs"
          ErrorMessage: "Maximum user wait time exceeded: {{$curStep:performance.executionTimeMs}}ms > {{$var:MaxUserWaitTime}}ms"

  # Step 7: Performance scaling validation
  - Name: "Performance Scaling Test"
    Variables:
      ScalingFactor: "2.0"  # Simulating 2x load
      EfficiencyTarget: "0.7"  # Should maintain 70% efficiency under load
    Input:
      Method: "POST"
      RequestUri: "{{$var:BaseUrl}}/scaling/test"
      Content: '{"loadMultiplier": 2}'
    Execution:
      DelayMs: 200
    Asserters:
      # Scaling efficiency: time should not exceed baseline * scaling / efficiency
      - AssertLt:
          ConstExpr: "{{$var:APICallBaseline * $var:ScalingFactor / $var:EfficiencyTarget}}"
          JPathExpr: "$curStep:performance.executionTimeMs"
          ErrorMessage: "Scaling efficiency below target: {{$curStep:performance.executionTimeMs}}ms vs {{$var:APICallBaseline * $var:ScalingFactor / $var:EfficiencyTarget}}ms"
