2025-06-30T08:59:22.3101987+05:30 [Information] [----------] 1 step from Teardown
2025-06-30T08:59:22.3108372+05:30 [Information] Set Value to Step '6659fa3a-8fa3-40eb-a0dc-07f080801f1b' name:
"Clean up Database"
2025-06-30T08:59:22.3111126+05:30 [Information] Set Value to Step '6659fa3a-8fa3-40eb-a0dc-07f080801f1b' description:
"Tear down the database state"
2025-06-30T08:59:22.3111897+05:30 [Information] [ RUN      ] Teardown > Clean up Database
2025-06-30T08:59:22.3115131+05:30 [Information] Executing single instance of step 'Clean up Database'.
2025-06-30T08:59:22.3116677+05:30 [Information] Executing step 'Clean up Database' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "6659fa3a-8fa3-40eb-a0dc-07f080801f1b",
        "tables": {},
        "variables": {},
        "name": "Clean up Database",
        "description": "Tear down the database state"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T08:59:22.3151555+05:30 [Information] Processing $var: path 'baseUrl'
2025-06-30T08:59:22.3152798+05:30 [Information] Checking if 'baseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T08:59:22.3153581+05:30 [Information] Resolving path 'baseUrl' using global variables.
2025-06-30T08:59:22.3154407+05:30 [Information] Resolved JPath '$var:baseUrl' to 'https://httpbin.org'
2025-06-30T08:59:22.3154883+05:30 [Information] Resolved template pattern '{{$var:baseUrl}}' to 'https://httpbin.org' in '{{$var:baseUrl}}/post'
2025-06-30T08:59:22.3155358+05:30 [Information] Resolved template '{{$var:baseUrl}}/post' to 'https://httpbin.org/post'
2025-06-30T08:59:22.3157122+05:30 [Information] Set Value to Step '6659fa3a-8fa3-40eb-a0dc-07f080801f1b' input:
{
  "Method": "POST",
  "RequestUri": "https://httpbin.org/post",
  "Headers": {
    "Content-Type": "application/json"
  },
  "Content": {
    "action": "cleanup_db"
  }
}
2025-06-30T08:59:23.0949668+05:30 [Information] Set Value to Step '6659fa3a-8fa3-40eb-a0dc-07f080801f1b' output:
{
  "statusCode": 200,
  "reasonPhrase": "OK",
  "headers": [
    {
      "Key": "Date",
      "Value": [
        "Mon, 30 Jun 2025 03:29:28 GMT"
      ]
    },
    {
      "Key": "Connection",
      "Value": [
        "keep-alive"
      ]
    },
    {
      "Key": "Server",
      "Value": [
        "gunicorn/19.9.0"
      ]
    },
    {
      "Key": "Access-Control-Allow-Origin",
      "Value": [
        "*"
      ]
    },
    {
      "Key": "Access-Control-Allow-Credentials",
      "Value": [
        "true"
      ]
    }
  ],
  "content": {
    "args": {},
    "data": "{\r\n  \"action\": \"cleanup_db\"\r\n}",
    "files": {},
    "form": {},
    "headers": {
      "Content-Length": "30",
      "Content-Type": "application/json; charset=utf-8",
      "Host": "httpbin.org",
      "X-Amzn-Trace-Id": "Root=1-68620498-61ed0eba346b20640abd9482"
    },
    "json": {
      "action": "cleanup_db"
    },
    "origin": "**************",
    "url": "https://httpbin.org/post"
  }
}
2025-06-30T08:59:23.0960960+05:30 [Information] Set Value to Step '6659fa3a-8fa3-40eb-a0dc-07f080801f1b' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T03:29:22.3115096Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T08:59:23.0969334+05:30 [Information] Set Value to Step '6659fa3a-8fa3-40eb-a0dc-07f080801f1b' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 780,
  "startTime": "2025-06-30T03:29:22.3115096Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T08:59:23.0975635+05:30 [Information] Step 'Clean up Database' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "6659fa3a-8fa3-40eb-a0dc-07f080801f1b",
        "tables": {},
        "variables": {},
        "name": "Clean up Database",
        "description": "Tear down the database state",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "action": "cleanup_db"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 03:29:28 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"action\": \"cleanup_db\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "30",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620498-61ed0eba346b20640abd9482"
            },
            "json": {
              "action": "cleanup_db"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 780,
          "startTime": "2025-06-30T03:29:22.3115096Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T08:59:23.0978729+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T08:59:23.0981232+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T08:59:23.0983217+05:30 [Information] No performance data found for step 'Clean up Database' (http)
2025-06-30T08:59:23.0985060+05:30 [Information] [ OK       ]
2025-06-30T08:59:23.0995317+05:30 [Information] [----------] 1 step from Teardown (788 ms total)
