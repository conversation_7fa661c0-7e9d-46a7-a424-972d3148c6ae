2025-06-30T09:37:37.1351118+05:30 [Information] [----------] 2 steps from Itterative Test and Step
2025-06-30T09:37:37.1355768+05:30 [Information] Default-mapped column 'User' to variable 'User' with value '{
  "Username": "<PERSON>",
  "Email": "<EMAIL>"
}'
2025-06-30T09:37:37.1356524+05:30 [Information] Default-mapped column 'OrgName' to variable 'OrgName' with value 'Org1'
2025-06-30T09:37:37.1357049+05:30 [Information] Processing $var: path 'User.Username'
2025-06-30T09:37:37.1357489+05:30 [Information] Checking if 'User.Username' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=True, result=False
2025-06-30T09:37:37.1357987+05:30 [Information] Resolving path 'User.Username' using global variables.
2025-06-30T09:37:37.1359560+05:30 [Information] Resolved JPath '$var:User.Username' to 'Tom'
2025-06-30T09:37:37.1360450+05:30 [Information] Resolved template pattern '{{$var:User.Username}}' to 'Tom' in '{{$var:User.Username}}'
2025-06-30T09:37:37.1360963+05:30 [Information] Resolved template '{{$var:User.Username}}' to 'Tom'
2025-06-30T09:37:37.1361654+05:30 [Information] Mapped column '{{$var:User.Username}}' to variable 'Usernamelocal' with value 'Tom'
2025-06-30T09:37:37.1362145+05:30 [Information] Processing $var: path 'User.Email'
2025-06-30T09:37:37.1362595+05:30 [Information] Checking if 'User.Email' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=True, result=False
2025-06-30T09:37:37.1362970+05:30 [Information] Resolving path 'User.Email' using global variables.
2025-06-30T09:37:37.1363353+05:30 [Information] Resolved JPath '$var:User.Email' to '<EMAIL>'
2025-06-30T09:37:37.1363709+05:30 [Information] Resolved template pattern '{{$var:User.Email}}' to '<EMAIL>' in '{{$var:User.Email}}'
2025-06-30T09:37:37.1364076+05:30 [Information] Resolved template '{{$var:User.Email}}' to '<EMAIL>'
2025-06-30T09:37:37.1364665+05:30 [Information] Mapped column '{{$var:User.Email}}' to variable 'Emaillocal' with value '<EMAIL>'
2025-06-30T09:37:37.1365102+05:30 [Information] Processing $var: path 'Usernamelocal'
2025-06-30T09:37:37.1365483+05:30 [Information] Checking if 'Usernamelocal' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:37.1365846+05:30 [Information] Resolving path 'Usernamelocal' using global variables.
2025-06-30T09:37:37.1366215+05:30 [Information] Resolved JPath '$var:Usernamelocal' to 'Tom'
2025-06-30T09:37:37.1366569+05:30 [Information] Resolved template pattern '{{$var:Usernamelocal}}' to 'Tom' in 'Running Test for {{$var:Usernamelocal}}'
2025-06-30T09:37:37.1366942+05:30 [Information] Resolved template 'Running Test for {{$var:Usernamelocal}}' to 'Running Test for Tom'
2025-06-30T09:37:37.1368509+05:30 [Information] Test Iteration 1/2: Running Test for Tom
2025-06-30T09:37:37.1369658+05:30 [Information] Processing $var: path 'Usernamelocal'
2025-06-30T09:37:37.1370330+05:30 [Information] Checking if 'Usernamelocal' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:37.1370786+05:30 [Information] Resolving path 'Usernamelocal' using global variables.
2025-06-30T09:37:37.1371198+05:30 [Information] Resolved JPath '$var:Usernamelocal' to 'Tom'
2025-06-30T09:37:37.1371566+05:30 [Information] Resolved template pattern '{{$var:Usernamelocal}}' to 'Tom' in 'Step to get token for {{$var:Usernamelocal}}'
2025-06-30T09:37:37.1371942+05:30 [Information] Resolved template 'Step to get token for {{$var:Usernamelocal}}' to 'Step to get token for Tom'
2025-06-30T09:37:37.1372372+05:30 [Information] Processing $var: path 'Usernamelocal'
2025-06-30T09:37:37.1372739+05:30 [Information] Checking if 'Usernamelocal' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:37.1373106+05:30 [Information] Resolving path 'Usernamelocal' using global variables.
2025-06-30T09:37:37.1373470+05:30 [Information] Resolved JPath '$var:Usernamelocal' to 'Tom'
2025-06-30T09:37:37.1373950+05:30 [Information] Resolved template pattern '{{$var:Usernamelocal}}' to 'Tom' in 'Step to get token for {{$var:Usernamelocal}}'
2025-06-30T09:37:37.1374402+05:30 [Information] Resolved template 'Step to get token for {{$var:Usernamelocal}}' to 'Step to get token for Tom'
2025-06-30T09:37:37.1374822+05:30 [Information] Set Value to Step 'e0307eff-1cb7-4523-a9d8-d0eb9677f935' name:
"Step to get token for Tom"
2025-06-30T09:37:37.1375211+05:30 [Information] Set Value to Step 'e0307eff-1cb7-4523-a9d8-d0eb9677f935' description:
"Step to get token for Tom"
2025-06-30T09:37:37.1375573+05:30 [Information] [ RUN      ] Itterative Test and Step > Step to get token for Tom
2025-06-30T09:37:37.1377545+05:30 [Information] Executing single instance of step 'Step to get token for {{$var:Usernamelocal}}'.
2025-06-30T09:37:37.1378135+05:30 [Information] Executing step 'Step to get token for Tom' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "e0307eff-1cb7-4523-a9d8-d0eb9677f935",
        "tables": {},
        "variables": {},
        "name": "Step to get token for Tom",
        "description": "Step to get token for Tom"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaseUrl": "https://httpbin.org",
      "User": {
        "Username": "Tom",
        "Email": "<EMAIL>"
      },
      "OrgName": "Org1",
      "Usernamelocal": "Tom",
      "Emaillocal": "<EMAIL>"
    },
    "tables": {
      "UserTable": [
        {
          "User": {
            "Username": "Tom",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org1"
        },
        {
          "User": {
            "Username": "John",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org2"
        }
      ],
      "UserRolesTable": [
        {
          "Name": "Admin"
        },
        {
          "Name": "User"
        }
      ]
    },
    "context": {
      "repeatForTable": "UserTable",
      "row": {
        "User": {
          "Username": "Tom",
          "Email": "<EMAIL>"
        },
        "OrgName": "Org1"
      },
      "rowIndex": 0
    }
  }
}
2025-06-30T09:37:37.1378594+05:30 [Information] Processing $var: path 'BaseUrl'
2025-06-30T09:37:37.1378989+05:30 [Information] Checking if 'BaseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:37.1379380+05:30 [Information] Resolving path 'BaseUrl' using global variables.
2025-06-30T09:37:37.1379791+05:30 [Information] Resolved JPath '$var:BaseUrl' to 'https://httpbin.org'
2025-06-30T09:37:37.1380145+05:30 [Information] Resolved template pattern '{{$var:BaseUrl}}' to 'https://httpbin.org' in '{{$var:BaseUrl}}/post'
2025-06-30T09:37:37.1380530+05:30 [Information] Resolved template '{{$var:BaseUrl}}/post' to 'https://httpbin.org/post'
2025-06-30T09:37:37.1381106+05:30 [Information] Resolving path 'row.User.Username' using test context.
2025-06-30T09:37:37.1381512+05:30 [Information] Resolved JPath '$test:row.User.Username' to 'Tom'
2025-06-30T09:37:37.1381872+05:30 [Information] Resolved template pattern '{{$test:row.User.Username}}' to 'Tom' in '{
  "username": "{{$test:row.User.Username}}",
  "email": "{{$var:Emaillocal}}",
  "org": "{{$var:OrgName}}"
}'
2025-06-30T09:37:37.1382246+05:30 [Information] Processing $var: path 'Emaillocal'
2025-06-30T09:37:37.1382607+05:30 [Information] Checking if 'Emaillocal' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:37.1382995+05:30 [Information] Resolving path 'Emaillocal' using global variables.
2025-06-30T09:37:37.1383361+05:30 [Information] Resolved JPath '$var:Emaillocal' to '<EMAIL>'
2025-06-30T09:37:37.1383711+05:30 [Information] Resolved template pattern '{{$var:Emaillocal}}' to '<EMAIL>' in '{
  "username": "{{$test:row.User.Username}}",
  "email": "{{$var:Emaillocal}}",
  "org": "{{$var:OrgName}}"
}'
2025-06-30T09:37:37.1384227+05:30 [Information] Processing $var: path 'OrgName'
2025-06-30T09:37:37.1384633+05:30 [Information] Checking if 'OrgName' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:37.1385007+05:30 [Information] Resolving path 'OrgName' using global variables.
2025-06-30T09:37:37.1385372+05:30 [Information] Resolved JPath '$var:OrgName' to 'Org1'
2025-06-30T09:37:37.1385726+05:30 [Information] Resolved template pattern '{{$var:OrgName}}' to 'Org1' in '{
  "username": "{{$test:row.User.Username}}",
  "email": "{{$var:Emaillocal}}",
  "org": "{{$var:OrgName}}"
}'
2025-06-30T09:37:37.1386100+05:30 [Information] Resolved template '{
  "username": "{{$test:row.User.Username}}",
  "email": "{{$var:Emaillocal}}",
  "org": "{{$var:OrgName}}"
}' to '{
  "username": "Tom",
  "email": "<EMAIL>",
  "org": "Org1"
}'
2025-06-30T09:37:37.1386745+05:30 [Information] Set Value to Step 'e0307eff-1cb7-4523-a9d8-d0eb9677f935' input:
{
  "Method": "POST",
  "RequestUri": "https://httpbin.org/post",
  "Headers": {
    "Content-Type": "application/json"
  },
  "Content": {
    "username": "Tom",
    "email": "<EMAIL>",
    "org": "Org1"
  }
}
2025-06-30T09:37:37.8956700+05:30 [Information] Set Value to Step 'e0307eff-1cb7-4523-a9d8-d0eb9677f935' output:
{
  "statusCode": 200,
  "reasonPhrase": "OK",
  "headers": [
    {
      "Key": "Date",
      "Value": [
        "Mon, 30 Jun 2025 04:07:43 GMT"
      ]
    },
    {
      "Key": "Connection",
      "Value": [
        "keep-alive"
      ]
    },
    {
      "Key": "Server",
      "Value": [
        "gunicorn/19.9.0"
      ]
    },
    {
      "Key": "Access-Control-Allow-Origin",
      "Value": [
        "*"
      ]
    },
    {
      "Key": "Access-Control-Allow-Credentials",
      "Value": [
        "true"
      ]
    }
  ],
  "content": {
    "args": {},
    "data": "{\r\n  \"username\": \"Tom\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"org\": \"Org1\"\r\n}",
    "files": {},
    "form": {},
    "headers": {
      "Content-Length": "74",
      "Content-Type": "application/json; charset=utf-8",
      "Host": "httpbin.org",
      "X-Amzn-Trace-Id": "Root=1-68620d8f-38e8db126912ea4c7c533e95"
    },
    "json": {
      "email": "<EMAIL>",
      "org": "Org1",
      "username": "Tom"
    },
    "origin": "**************",
    "url": "https://httpbin.org/post"
  }
}
2025-06-30T09:37:37.8959859+05:30 [Information] Resolving path 'output.content.json.username' using step input/output.
2025-06-30T09:37:37.8960911+05:30 [Information] Resolved JPath 'output.content.json.username' to 'Tom'
2025-06-30T09:37:37.8962486+05:30 [Information] Set Value to Step 'e0307eff-1cb7-4523-a9d8-d0eb9677f935' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:07:37.1377533Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:37:37.8963588+05:30 [Information] Run Asserter : {"Expression1":{"Value":"200","ErrorMesage":null},"Expression2":{"JPath":"output.statusCode","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:37:37.8964339+05:30 [Information] Resolving path 'output.statusCode' using step input/output.
2025-06-30T09:37:37.8964831+05:30 [Information] Resolved JPath 'output.statusCode' to '200'
2025-06-30T09:37:37.8965357+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$test:row.User.Username}}","ErrorMesage":null},"Expression2":{"JPath":"output.content.json.username","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:37:37.8965924+05:30 [Information] Resolving path 'row.User.Username' using test context.
2025-06-30T09:37:37.8966340+05:30 [Information] Resolved JPath '$test:row.User.Username' to 'Tom'
2025-06-30T09:37:37.8966913+05:30 [Information] Resolved template pattern '{{$test:row.User.Username}}' to 'Tom' in '{{$test:row.User.Username}}'
2025-06-30T09:37:37.8967403+05:30 [Information] Resolved template '{{$test:row.User.Username}}' to 'Tom'
2025-06-30T09:37:37.8968318+05:30 [Information] Resolving path 'output.content.json.username' using step input/output.
2025-06-30T09:37:37.8968832+05:30 [Information] Resolved JPath 'output.content.json.username' to 'Tom'
2025-06-30T09:37:37.8969310+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$test:row.User.Email}}","ErrorMesage":null},"Expression2":{"JPath":"output.content.json.email","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:37:37.8969738+05:30 [Information] Resolving path 'row.User.Email' using test context.
2025-06-30T09:37:37.8970139+05:30 [Information] Resolved JPath '$test:row.User.Email' to '<EMAIL>'
2025-06-30T09:37:37.8970522+05:30 [Information] Resolved template pattern '{{$test:row.User.Email}}' to '<EMAIL>' in '{{$test:row.User.Email}}'
2025-06-30T09:37:37.8970891+05:30 [Information] Resolved template '{{$test:row.User.Email}}' to '<EMAIL>'
2025-06-30T09:37:37.8971495+05:30 [Information] Resolving path 'output.content.json.email' using step input/output.
2025-06-30T09:37:37.8971918+05:30 [Information] Resolved JPath 'output.content.json.email' to '<EMAIL>'
2025-06-30T09:37:37.8972364+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$test:row.OrgName}}","ErrorMesage":null},"Expression2":{"JPath":"output.content.json.org","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:37:37.8972770+05:30 [Information] Resolving path 'row.OrgName' using test context.
2025-06-30T09:37:37.8973156+05:30 [Information] Resolved JPath '$test:row.OrgName' to 'Org1'
2025-06-30T09:37:37.8973521+05:30 [Information] Resolved template pattern '{{$test:row.OrgName}}' to 'Org1' in '{{$test:row.OrgName}}'
2025-06-30T09:37:37.8973887+05:30 [Information] Resolved template '{{$test:row.OrgName}}' to 'Org1'
2025-06-30T09:37:37.8974428+05:30 [Information] Resolving path 'output.content.json.org' using step input/output.
2025-06-30T09:37:37.8974851+05:30 [Information] Resolved JPath 'output.content.json.org' to 'Org1'
2025-06-30T09:37:37.8975299+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$test:row.NewUsername}}","ErrorMesage":null},"Expression2":{"JPath":"output.content.json.username","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:37:37.8975721+05:30 [Information] Resolving path 'row.NewUsername' using test context.
2025-06-30T09:37:37.8976158+05:30 [Information] Resolved JPath '$test:row.NewUsername' to 'Tom'
2025-06-30T09:37:37.8976558+05:30 [Information] Resolved template pattern '{{$test:row.NewUsername}}' to 'Tom' in '{{$test:row.NewUsername}}'
2025-06-30T09:37:37.8977032+05:30 [Information] Resolved template '{{$test:row.NewUsername}}' to 'Tom'
2025-06-30T09:37:37.8977588+05:30 [Information] Resolving path 'output.content.json.username' using step input/output.
2025-06-30T09:37:37.8978581+05:30 [Information] Resolved JPath 'output.content.json.username' to 'Tom'
2025-06-30T09:37:37.8979722+05:30 [Information] Set Value to Step 'e0307eff-1cb7-4523-a9d8-d0eb9677f935' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 759,
  "startTime": "2025-06-30T04:07:37.1377533Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:37:37.8980473+05:30 [Information] Step 'Step to get token for Tom' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "e0307eff-1cb7-4523-a9d8-d0eb9677f935",
        "tables": {},
        "variables": {},
        "name": "Step to get token for Tom",
        "description": "Step to get token for Tom",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "Tom",
            "email": "<EMAIL>",
            "org": "Org1"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:07:43 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"Tom\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"org\": \"Org1\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "74",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620d8f-38e8db126912ea4c7c533e95"
            },
            "json": {
              "email": "<EMAIL>",
              "org": "Org1",
              "username": "Tom"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 759,
          "startTime": "2025-06-30T04:07:37.1377533Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaseUrl": "https://httpbin.org",
      "User": {
        "Username": "Tom",
        "Email": "<EMAIL>"
      },
      "OrgName": "Org1",
      "Usernamelocal": "Tom",
      "Emaillocal": "<EMAIL>"
    },
    "tables": {
      "UserTable": [
        {
          "User": {
            "Username": "Tom",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org1",
          "NewUsername": "Tom"
        },
        {
          "User": {
            "Username": "John",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org2"
        }
      ],
      "UserRolesTable": [
        {
          "Name": "Admin"
        },
        {
          "Name": "User"
        }
      ]
    },
    "context": {
      "repeatForTable": "UserTable",
      "row": {
        "User": {
          "Username": "Tom",
          "Email": "<EMAIL>"
        },
        "OrgName": "Org1",
        "NewUsername": "Tom"
      },
      "rowIndex": 0
    }
  }
}
2025-06-30T09:37:37.8981358+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:37:37.8981914+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:37:37.8982310+05:30 [Information] No performance data found for step 'Step to get token for {{$var:Usernamelocal}}' (http)
2025-06-30T09:37:37.8982704+05:30 [Information] [ OK       ]
2025-06-30T09:37:37.8984936+05:30 [Information] Processing $var: path 'Usernamelocal'
2025-06-30T09:37:37.8985643+05:30 [Information] Checking if 'Usernamelocal' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:37.8986183+05:30 [Information] Resolving path 'Usernamelocal' using global variables.
2025-06-30T09:37:37.8986587+05:30 [Information] Resolved JPath '$var:Usernamelocal' to 'Tom'
2025-06-30T09:37:37.8986952+05:30 [Information] Resolved template pattern '{{$var:Usernamelocal}}' to 'Tom' in 'Itterative Step for user {{$var:Usernamelocal}}'
2025-06-30T09:37:37.8987957+05:30 [Information] Resolved template 'Itterative Step for user {{$var:Usernamelocal}}' to 'Itterative Step for user Tom'
2025-06-30T09:37:37.8989022+05:30 [Information] Set Value to Step '59bbd061-39e3-4683-8ec9-94cbaa5976a7' name:
"Itterative Step for user Tom"
2025-06-30T09:37:37.8989679+05:30 [Information] Set Value to Step '59bbd061-39e3-4683-8ec9-94cbaa5976a7' description:
"Itterative Step"
2025-06-30T09:37:37.8990122+05:30 [Information] [ RUN      ] Itterative Test and Step > Itterative Step for user Tom
2025-06-30T09:37:37.8992930+05:30 [Information] Executing step 'Itterative Step for user {{$var:Usernamelocal}}' for row 1/2 in table 'UserRolesTable'.
2025-06-30T09:37:37.8993848+05:30 [Information] Default-mapped column 'Name' to variable 'Name' with value 'Admin'
2025-06-30T09:37:37.8994404+05:30 [Information] Resolved path 'row.Name' in step-specific context.
2025-06-30T09:37:37.8994867+05:30 [Information] Resolved JPath '$step:row.Name' to 'Admin'
2025-06-30T09:37:37.8995253+05:30 [Information] Resolved template pattern '{{$step:row.Name}}' to 'Admin' in '{{$step:row.Name}}'
2025-06-30T09:37:37.8995622+05:30 [Information] Resolved template '{{$step:row.Name}}' to 'Admin'
2025-06-30T09:37:37.8996408+05:30 [Information] Mapped column '{{$step:row.Name}}' to variable 'Role' with value 'Admin'
2025-06-30T09:37:37.8996913+05:30 [Information] Processing $var: path 'Usernamelocal'
2025-06-30T09:37:37.8997304+05:30 [Information] Checking if 'Usernamelocal' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:37.8997696+05:30 [Information] Resolving path 'Usernamelocal' using global variables.
2025-06-30T09:37:37.8998073+05:30 [Information] Resolved JPath '$var:Usernamelocal' to 'Tom'
2025-06-30T09:37:37.8998445+05:30 [Information] Resolved template pattern '{{$var:Usernamelocal}}' to 'Tom' in 'Running for {{$var:Usernamelocal}} and role {{$step:row.Name}}'
2025-06-30T09:37:37.8998860+05:30 [Information] Resolved path 'row.Name' in step-specific context.
2025-06-30T09:37:37.8999229+05:30 [Information] Resolved JPath '$step:row.Name' to 'Admin'
2025-06-30T09:37:37.8999582+05:30 [Information] Resolved template pattern '{{$step:row.Name}}' to 'Admin' in 'Running for {{$var:Usernamelocal}} and role {{$step:row.Name}}'
2025-06-30T09:37:37.8999956+05:30 [Information] Resolved template 'Running for {{$var:Usernamelocal}} and role {{$step:row.Name}}' to 'Running for Tom and role Admin'
2025-06-30T09:37:37.9001904+05:30 [Information] Step Iteration 1/2: Running for Tom and role Admin
2025-06-30T09:37:37.9004157+05:30 [Information] Executing step 'Running for Tom and role Admin' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "e0307eff-1cb7-4523-a9d8-d0eb9677f935",
        "tables": {},
        "variables": {},
        "name": "Step to get token for Tom",
        "description": "Step to get token for Tom",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "Tom",
            "email": "<EMAIL>",
            "org": "Org1"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:07:43 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"Tom\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"org\": \"Org1\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "74",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620d8f-38e8db126912ea4c7c533e95"
            },
            "json": {
              "email": "<EMAIL>",
              "org": "Org1",
              "username": "Tom"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 759,
          "startTime": "2025-06-30T04:07:37.1377533Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      },
      {
        "id": "59bbd061-39e3-4683-8ec9-94cbaa5976a7",
        "tables": {},
        "variables": {
          "Name": "Admin",
          "Role": "Admin"
        },
        "name": "Itterative Step for user Tom",
        "description": "Itterative Step",
        "context": {
          "repeatForTable": "UserRolesTable",
          "row": {
            "Name": "Admin"
          },
          "rowIndex": 0
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaseUrl": "https://httpbin.org",
      "User": {
        "Username": "Tom",
        "Email": "<EMAIL>"
      },
      "OrgName": "Org1",
      "Usernamelocal": "Tom",
      "Emaillocal": "<EMAIL>"
    },
    "tables": {
      "UserTable": [
        {
          "User": {
            "Username": "Tom",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org1",
          "NewUsername": "Tom"
        },
        {
          "User": {
            "Username": "John",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org2"
        }
      ],
      "UserRolesTable": [
        {
          "Name": "Admin"
        },
        {
          "Name": "User"
        }
      ]
    },
    "context": {
      "repeatForTable": "UserTable",
      "row": {
        "User": {
          "Username": "Tom",
          "Email": "<EMAIL>"
        },
        "OrgName": "Org1",
        "NewUsername": "Tom"
      },
      "rowIndex": 0
    }
  }
}
2025-06-30T09:37:37.9004984+05:30 [Information] Processing $var: path 'BaseUrl'
2025-06-30T09:37:37.9005472+05:30 [Information] Checking if 'BaseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:37.9005895+05:30 [Information] Resolving path 'BaseUrl' using global variables.
2025-06-30T09:37:37.9006270+05:30 [Information] Resolved JPath '$var:BaseUrl' to 'https://httpbin.org'
2025-06-30T09:37:37.9006674+05:30 [Information] Resolved template pattern '{{$var:BaseUrl}}' to 'https://httpbin.org' in '{{$var:BaseUrl}}/post'
2025-06-30T09:37:37.9007055+05:30 [Information] Resolved template '{{$var:BaseUrl}}/post' to 'https://httpbin.org/post'
2025-06-30T09:37:37.9007887+05:30 [Information] Resolving path 'row.NewUsername' using test context.
2025-06-30T09:37:37.9009140+05:30 [Information] Resolved JPath '$test:row.NewUsername' to 'Tom'
2025-06-30T09:37:37.9009798+05:30 [Information] Resolved template pattern '{{$test:row.NewUsername}}' to 'Tom' in '{
  "username": "{{$test:row.NewUsername}}",
  "role": "{{$var:Role}}"
}'
2025-06-30T09:37:37.9010431+05:30 [Information] Processing $var: path 'Role'
2025-06-30T09:37:37.9010902+05:30 [Information] Checking if 'Role' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:37.9011286+05:30 [Information] Resolved path 'Role' in step-specific variables.
2025-06-30T09:37:37.9011655+05:30 [Information] Resolved JPath '$var:Role' to 'Admin'
2025-06-30T09:37:37.9012079+05:30 [Information] Resolved template pattern '{{$var:Role}}' to 'Admin' in '{
  "username": "{{$test:row.NewUsername}}",
  "role": "{{$var:Role}}"
}'
2025-06-30T09:37:37.9012465+05:30 [Information] Resolved template '{
  "username": "{{$test:row.NewUsername}}",
  "role": "{{$var:Role}}"
}' to '{
  "username": "Tom",
  "role": "Admin"
}'
2025-06-30T09:37:37.9013245+05:30 [Information] Set Value to Step '59bbd061-39e3-4683-8ec9-94cbaa5976a7' input:
{
  "Method": "POST",
  "RequestUri": "https://httpbin.org/post",
  "Headers": {
    "Content-Type": "application/json"
  },
  "Content": {
    "username": "Tom",
    "role": "Admin"
  }
}
2025-06-30T09:37:38.6740346+05:30 [Information] Set Value to Step '59bbd061-39e3-4683-8ec9-94cbaa5976a7' output:
{
  "statusCode": 200,
  "reasonPhrase": "OK",
  "headers": [
    {
      "Key": "Date",
      "Value": [
        "Mon, 30 Jun 2025 04:07:44 GMT"
      ]
    },
    {
      "Key": "Connection",
      "Value": [
        "keep-alive"
      ]
    },
    {
      "Key": "Server",
      "Value": [
        "gunicorn/19.9.0"
      ]
    },
    {
      "Key": "Access-Control-Allow-Origin",
      "Value": [
        "*"
      ]
    },
    {
      "Key": "Access-Control-Allow-Credentials",
      "Value": [
        "true"
      ]
    }
  ],
  "content": {
    "args": {},
    "data": "{\r\n  \"username\": \"Tom\",\r\n  \"role\": \"Admin\"\r\n}",
    "files": {},
    "form": {},
    "headers": {
      "Content-Length": "45",
      "Content-Type": "application/json; charset=utf-8",
      "Host": "httpbin.org",
      "X-Amzn-Trace-Id": "Root=1-68620d90-52f1185a210a235e7dd24f70"
    },
    "json": {
      "role": "Admin",
      "username": "Tom"
    },
    "origin": "**************",
    "url": "https://httpbin.org/post"
  }
}
2025-06-30T09:37:38.6743220+05:30 [Information] Set Value to Step '59bbd061-39e3-4683-8ec9-94cbaa5976a7' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:07:37.8992651Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:37:38.6744395+05:30 [Information] Run Asserter : {"Expression1":{"Value":"200","ErrorMesage":null},"Expression2":{"JPath":"output.statusCode","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:37:38.6744969+05:30 [Information] Resolving path 'output.statusCode' using step input/output.
2025-06-30T09:37:38.6745578+05:30 [Information] Resolved JPath 'output.statusCode' to '200'
2025-06-30T09:37:38.6746258+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$test:row.NewUsername}}","ErrorMesage":null},"Expression2":{"JPath":"output.content.json.username","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:37:38.6746699+05:30 [Information] Resolving path 'row.NewUsername' using test context.
2025-06-30T09:37:38.6747328+05:30 [Information] Resolved JPath '$test:row.NewUsername' to 'Tom'
2025-06-30T09:37:38.6747672+05:30 [Information] Resolved template pattern '{{$test:row.NewUsername}}' to 'Tom' in '{{$test:row.NewUsername}}'
2025-06-30T09:37:38.6748054+05:30 [Information] Resolved template '{{$test:row.NewUsername}}' to 'Tom'
2025-06-30T09:37:38.6748825+05:30 [Information] Resolving path 'output.content.json.username' using step input/output.
2025-06-30T09:37:38.6749559+05:30 [Information] Resolved JPath 'output.content.json.username' to 'Tom'
2025-06-30T09:37:38.6750086+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$step:row.Name}}","ErrorMesage":null},"Expression2":{"JPath":"output.content.json.role","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:37:38.6750664+05:30 [Information] Resolved path 'row.Name' in step-specific context.
2025-06-30T09:37:38.6751194+05:30 [Information] Resolved JPath '$step:row.Name' to 'Admin'
2025-06-30T09:37:38.6752052+05:30 [Information] Resolved template pattern '{{$step:row.Name}}' to 'Admin' in '{{$step:row.Name}}'
2025-06-30T09:37:38.6752603+05:30 [Information] Resolved template '{{$step:row.Name}}' to 'Admin'
2025-06-30T09:37:38.6753267+05:30 [Information] Resolving path 'output.content.json.role' using step input/output.
2025-06-30T09:37:38.6753734+05:30 [Information] Resolved JPath 'output.content.json.role' to 'Admin'
2025-06-30T09:37:38.6754335+05:30 [Information] Set Value to Step '59bbd061-39e3-4683-8ec9-94cbaa5976a7' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 774,
  "startTime": "2025-06-30T04:07:37.8992651Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:37:38.6754968+05:30 [Information] Step 'Running for Tom and role Admin' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "e0307eff-1cb7-4523-a9d8-d0eb9677f935",
        "tables": {},
        "variables": {},
        "name": "Step to get token for Tom",
        "description": "Step to get token for Tom",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "Tom",
            "email": "<EMAIL>",
            "org": "Org1"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:07:43 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"Tom\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"org\": \"Org1\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "74",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620d8f-38e8db126912ea4c7c533e95"
            },
            "json": {
              "email": "<EMAIL>",
              "org": "Org1",
              "username": "Tom"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 759,
          "startTime": "2025-06-30T04:07:37.1377533Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      },
      {
        "id": "59bbd061-39e3-4683-8ec9-94cbaa5976a7",
        "tables": {},
        "variables": {
          "Name": "Admin",
          "Role": "Admin"
        },
        "name": "Itterative Step for user Tom",
        "description": "Itterative Step",
        "context": {
          "repeatForTable": "UserRolesTable",
          "row": {
            "Name": "Admin"
          },
          "rowIndex": 0
        },
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "Tom",
            "role": "Admin"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:07:44 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"Tom\",\r\n  \"role\": \"Admin\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "45",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620d90-52f1185a210a235e7dd24f70"
            },
            "json": {
              "role": "Admin",
              "username": "Tom"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 774,
          "startTime": "2025-06-30T04:07:37.8992651Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaseUrl": "https://httpbin.org",
      "User": {
        "Username": "Tom",
        "Email": "<EMAIL>"
      },
      "OrgName": "Org1",
      "Usernamelocal": "Tom",
      "Emaillocal": "<EMAIL>"
    },
    "tables": {
      "UserTable": [
        {
          "User": {
            "Username": "Tom",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org1",
          "NewUsername": "Tom"
        },
        {
          "User": {
            "Username": "John",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org2"
        }
      ],
      "UserRolesTable": [
        {
          "Name": "Admin"
        },
        {
          "Name": "User"
        }
      ]
    },
    "context": {
      "repeatForTable": "UserTable",
      "row": {
        "User": {
          "Username": "Tom",
          "Email": "<EMAIL>"
        },
        "OrgName": "Org1",
        "NewUsername": "Tom"
      },
      "rowIndex": 0
    }
  }
}
2025-06-30T09:37:38.6755594+05:30 [Information] Executing step 'Itterative Step for user {{$var:Usernamelocal}}' for row 2/2 in table 'UserRolesTable'.
2025-06-30T09:37:38.6756281+05:30 [Information] Default-mapped column 'Name' to variable 'Name' with value 'User'
2025-06-30T09:37:38.6756759+05:30 [Information] Resolved path 'row.Name' in step-specific context.
2025-06-30T09:37:38.6757168+05:30 [Information] Resolved JPath '$step:row.Name' to 'User'
2025-06-30T09:37:38.6757516+05:30 [Information] Resolved template pattern '{{$step:row.Name}}' to 'User' in '{{$step:row.Name}}'
2025-06-30T09:37:38.6757895+05:30 [Information] Resolved template '{{$step:row.Name}}' to 'User'
2025-06-30T09:37:38.6758472+05:30 [Information] Mapped column '{{$step:row.Name}}' to variable 'Role' with value 'User'
2025-06-30T09:37:38.6758953+05:30 [Information] Processing $var: path 'Usernamelocal'
2025-06-30T09:37:38.6759807+05:30 [Information] Checking if 'Usernamelocal' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:38.6764893+05:30 [Information] Resolving path 'Usernamelocal' using global variables.
2025-06-30T09:37:38.6765991+05:30 [Information] Resolved JPath '$var:Usernamelocal' to 'Tom'
2025-06-30T09:37:38.6766498+05:30 [Information] Resolved template pattern '{{$var:Usernamelocal}}' to 'Tom' in 'Running for {{$var:Usernamelocal}} and role {{$step:row.Name}}'
2025-06-30T09:37:38.6767013+05:30 [Information] Resolved path 'row.Name' in step-specific context.
2025-06-30T09:37:38.6767513+05:30 [Information] Resolved JPath '$step:row.Name' to 'User'
2025-06-30T09:37:38.6767937+05:30 [Information] Resolved template pattern '{{$step:row.Name}}' to 'User' in 'Running for {{$var:Usernamelocal}} and role {{$step:row.Name}}'
2025-06-30T09:37:38.6768313+05:30 [Information] Resolved template 'Running for {{$var:Usernamelocal}} and role {{$step:row.Name}}' to 'Running for Tom and role User'
2025-06-30T09:37:38.6772353+05:30 [Information] Step Iteration 2/2: Running for Tom and role User
2025-06-30T09:37:38.6774341+05:30 [Information] Executing step 'Running for Tom and role User' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "e0307eff-1cb7-4523-a9d8-d0eb9677f935",
        "tables": {},
        "variables": {},
        "name": "Step to get token for Tom",
        "description": "Step to get token for Tom",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "Tom",
            "email": "<EMAIL>",
            "org": "Org1"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:07:43 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"Tom\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"org\": \"Org1\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "74",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620d8f-38e8db126912ea4c7c533e95"
            },
            "json": {
              "email": "<EMAIL>",
              "org": "Org1",
              "username": "Tom"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 759,
          "startTime": "2025-06-30T04:07:37.1377533Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      },
      {
        "id": "59bbd061-39e3-4683-8ec9-94cbaa5976a7",
        "tables": {},
        "variables": {
          "Name": "User",
          "Role": "User"
        },
        "name": "Itterative Step for user Tom",
        "description": "Itterative Step",
        "context": {
          "repeatForTable": "UserRolesTable",
          "row": {
            "Name": "User"
          },
          "rowIndex": 1
        },
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "Tom",
            "role": "Admin"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:07:44 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"Tom\",\r\n  \"role\": \"Admin\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "45",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620d90-52f1185a210a235e7dd24f70"
            },
            "json": {
              "role": "Admin",
              "username": "Tom"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 774,
          "startTime": "2025-06-30T04:07:37.8992651Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaseUrl": "https://httpbin.org",
      "User": {
        "Username": "Tom",
        "Email": "<EMAIL>"
      },
      "OrgName": "Org1",
      "Usernamelocal": "Tom",
      "Emaillocal": "<EMAIL>"
    },
    "tables": {
      "UserTable": [
        {
          "User": {
            "Username": "Tom",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org1",
          "NewUsername": "Tom"
        },
        {
          "User": {
            "Username": "John",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org2"
        }
      ],
      "UserRolesTable": [
        {
          "Name": "Admin"
        },
        {
          "Name": "User"
        }
      ]
    },
    "context": {
      "repeatForTable": "UserTable",
      "row": {
        "User": {
          "Username": "Tom",
          "Email": "<EMAIL>"
        },
        "OrgName": "Org1",
        "NewUsername": "Tom"
      },
      "rowIndex": 0
    }
  }
}
2025-06-30T09:37:38.6775125+05:30 [Information] Processing $var: path 'BaseUrl'
2025-06-30T09:37:38.6775592+05:30 [Information] Checking if 'BaseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:38.6775985+05:30 [Information] Resolving path 'BaseUrl' using global variables.
2025-06-30T09:37:38.6776358+05:30 [Information] Resolved JPath '$var:BaseUrl' to 'https://httpbin.org'
2025-06-30T09:37:38.6776706+05:30 [Information] Resolved template pattern '{{$var:BaseUrl}}' to 'https://httpbin.org' in '{{$var:BaseUrl}}/post'
2025-06-30T09:37:38.6777069+05:30 [Information] Resolved template '{{$var:BaseUrl}}/post' to 'https://httpbin.org/post'
2025-06-30T09:37:38.6777707+05:30 [Information] Resolving path 'row.NewUsername' using test context.
2025-06-30T09:37:38.6778232+05:30 [Information] Resolved JPath '$test:row.NewUsername' to 'Tom'
2025-06-30T09:37:38.6778643+05:30 [Information] Resolved template pattern '{{$test:row.NewUsername}}' to 'Tom' in '{
  "username": "{{$test:row.NewUsername}}",
  "role": "{{$var:Role}}"
}'
2025-06-30T09:37:38.6779038+05:30 [Information] Processing $var: path 'Role'
2025-06-30T09:37:38.6779391+05:30 [Information] Checking if 'Role' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:38.6779751+05:30 [Information] Resolved path 'Role' in step-specific variables.
2025-06-30T09:37:38.6780122+05:30 [Information] Resolved JPath '$var:Role' to 'User'
2025-06-30T09:37:38.6780465+05:30 [Information] Resolved template pattern '{{$var:Role}}' to 'User' in '{
  "username": "{{$test:row.NewUsername}}",
  "role": "{{$var:Role}}"
}'
2025-06-30T09:37:38.6780817+05:30 [Information] Resolved template '{
  "username": "{{$test:row.NewUsername}}",
  "role": "{{$var:Role}}"
}' to '{
  "username": "Tom",
  "role": "User"
}'
2025-06-30T09:37:38.6781506+05:30 [Information] Set Value to Step '59bbd061-39e3-4683-8ec9-94cbaa5976a7' input:
{
  "Method": "POST",
  "RequestUri": "https://httpbin.org/post",
  "Headers": {
    "Content-Type": "application/json"
  },
  "Content": {
    "username": "Tom",
    "role": "User"
  }
}
2025-06-30T09:37:38.9441437+05:30 [Information] Set Value to Step '59bbd061-39e3-4683-8ec9-94cbaa5976a7' output:
{
  "statusCode": 200,
  "reasonPhrase": "OK",
  "headers": [
    {
      "Key": "Date",
      "Value": [
        "Mon, 30 Jun 2025 04:07:44 GMT"
      ]
    },
    {
      "Key": "Connection",
      "Value": [
        "keep-alive"
      ]
    },
    {
      "Key": "Server",
      "Value": [
        "gunicorn/19.9.0"
      ]
    },
    {
      "Key": "Access-Control-Allow-Origin",
      "Value": [
        "*"
      ]
    },
    {
      "Key": "Access-Control-Allow-Credentials",
      "Value": [
        "true"
      ]
    }
  ],
  "content": {
    "args": {},
    "data": "{\r\n  \"username\": \"Tom\",\r\n  \"role\": \"User\"\r\n}",
    "files": {},
    "form": {},
    "headers": {
      "Content-Length": "44",
      "Content-Type": "application/json; charset=utf-8",
      "Host": "httpbin.org",
      "X-Amzn-Trace-Id": "Root=1-68620d90-553375761cc6cb1264351781"
    },
    "json": {
      "role": "User",
      "username": "Tom"
    },
    "origin": "**************",
    "url": "https://httpbin.org/post"
  }
}
2025-06-30T09:37:38.9446918+05:30 [Information] Set Value to Step '59bbd061-39e3-4683-8ec9-94cbaa5976a7' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:07:37.8992651Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:37:38.9448483+05:30 [Information] Run Asserter : {"Expression1":{"Value":"200","ErrorMesage":null},"Expression2":{"JPath":"output.statusCode","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:37:38.9449627+05:30 [Information] Resolving path 'output.statusCode' using step input/output.
2025-06-30T09:37:38.9450737+05:30 [Information] Resolved JPath 'output.statusCode' to '200'
2025-06-30T09:37:38.9451789+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$test:row.NewUsername}}","ErrorMesage":null},"Expression2":{"JPath":"output.content.json.username","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:37:38.9452587+05:30 [Information] Resolving path 'row.NewUsername' using test context.
2025-06-30T09:37:38.9453587+05:30 [Information] Resolved JPath '$test:row.NewUsername' to 'Tom'
2025-06-30T09:37:38.9454398+05:30 [Information] Resolved template pattern '{{$test:row.NewUsername}}' to 'Tom' in '{{$test:row.NewUsername}}'
2025-06-30T09:37:38.9455690+05:30 [Information] Resolved template '{{$test:row.NewUsername}}' to 'Tom'
2025-06-30T09:37:38.9459369+05:30 [Information] Resolving path 'output.content.json.username' using step input/output.
2025-06-30T09:37:38.9461402+05:30 [Information] Resolved JPath 'output.content.json.username' to 'Tom'
2025-06-30T09:37:38.9462687+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$step:row.Name}}","ErrorMesage":null},"Expression2":{"JPath":"output.content.json.role","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:37:38.9463595+05:30 [Information] Resolved path 'row.Name' in step-specific context.
2025-06-30T09:37:38.9464379+05:30 [Information] Resolved JPath '$step:row.Name' to 'User'
2025-06-30T09:37:38.9464981+05:30 [Information] Resolved template pattern '{{$step:row.Name}}' to 'User' in '{{$step:row.Name}}'
2025-06-30T09:37:38.9465644+05:30 [Information] Resolved template '{{$step:row.Name}}' to 'User'
2025-06-30T09:37:38.9466499+05:30 [Information] Resolving path 'output.content.json.role' using step input/output.
2025-06-30T09:37:38.9467240+05:30 [Information] Resolved JPath 'output.content.json.role' to 'User'
2025-06-30T09:37:38.9468053+05:30 [Information] Set Value to Step '59bbd061-39e3-4683-8ec9-94cbaa5976a7' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 268,
  "startTime": "2025-06-30T04:07:37.8992651Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:37:38.9469122+05:30 [Information] Step 'Running for Tom and role User' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "e0307eff-1cb7-4523-a9d8-d0eb9677f935",
        "tables": {},
        "variables": {},
        "name": "Step to get token for Tom",
        "description": "Step to get token for Tom",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "Tom",
            "email": "<EMAIL>",
            "org": "Org1"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:07:43 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"Tom\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"org\": \"Org1\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "74",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620d8f-38e8db126912ea4c7c533e95"
            },
            "json": {
              "email": "<EMAIL>",
              "org": "Org1",
              "username": "Tom"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 759,
          "startTime": "2025-06-30T04:07:37.1377533Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      },
      {
        "id": "59bbd061-39e3-4683-8ec9-94cbaa5976a7",
        "tables": {},
        "variables": {
          "Name": "User",
          "Role": "User"
        },
        "name": "Itterative Step for user Tom",
        "description": "Itterative Step",
        "context": {
          "repeatForTable": "UserRolesTable",
          "row": {
            "Name": "User"
          },
          "rowIndex": 1
        },
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "Tom",
            "role": "User"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:07:44 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"Tom\",\r\n  \"role\": \"User\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "44",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620d90-553375761cc6cb1264351781"
            },
            "json": {
              "role": "User",
              "username": "Tom"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 268,
          "startTime": "2025-06-30T04:07:37.8992651Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaseUrl": "https://httpbin.org",
      "User": {
        "Username": "Tom",
        "Email": "<EMAIL>"
      },
      "OrgName": "Org1",
      "Usernamelocal": "Tom",
      "Emaillocal": "<EMAIL>"
    },
    "tables": {
      "UserTable": [
        {
          "User": {
            "Username": "Tom",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org1",
          "NewUsername": "Tom"
        },
        {
          "User": {
            "Username": "John",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org2"
        }
      ],
      "UserRolesTable": [
        {
          "Name": "Admin"
        },
        {
          "Name": "User"
        }
      ]
    },
    "context": {
      "repeatForTable": "UserTable",
      "row": {
        "User": {
          "Username": "Tom",
          "Email": "<EMAIL>"
        },
        "OrgName": "Org1",
        "NewUsername": "Tom"
      },
      "rowIndex": 0
    }
  }
}
2025-06-30T09:37:38.9469895+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:37:38.9470457+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:37:38.9471060+05:30 [Information] No performance data found for step 'Itterative Step for user {{$var:Usernamelocal}}' (http)
2025-06-30T09:37:38.9471533+05:30 [Information] [ OK       ]
2025-06-30T09:37:38.9475855+05:30 [Information] Default-mapped column 'User' to variable 'User' with value '{
  "Username": "John",
  "Email": "<EMAIL>"
}'
2025-06-30T09:37:38.9477246+05:30 [Information] Default-mapped column 'OrgName' to variable 'OrgName' with value 'Org2'
2025-06-30T09:37:38.9478520+05:30 [Information] Processing $var: path 'User.Username'
2025-06-30T09:37:38.9479154+05:30 [Information] Checking if 'User.Username' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=True, result=False
2025-06-30T09:37:38.9479637+05:30 [Information] Resolving path 'User.Username' using global variables.
2025-06-30T09:37:38.9480103+05:30 [Information] Resolved JPath '$var:User.Username' to 'John'
2025-06-30T09:37:38.9480504+05:30 [Information] Resolved template pattern '{{$var:User.Username}}' to 'John' in '{{$var:User.Username}}'
2025-06-30T09:37:38.9480917+05:30 [Information] Resolved template '{{$var:User.Username}}' to 'John'
2025-06-30T09:37:38.9481628+05:30 [Information] Mapped column '{{$var:User.Username}}' to variable 'Usernamelocal' with value 'John'
2025-06-30T09:37:38.9482117+05:30 [Information] Processing $var: path 'User.Email'
2025-06-30T09:37:38.9482532+05:30 [Information] Checking if 'User.Email' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=True, result=False
2025-06-30T09:37:38.9482947+05:30 [Information] Resolving path 'User.Email' using global variables.
2025-06-30T09:37:38.9483299+05:30 [Information] Resolved JPath '$var:User.Email' to '<EMAIL>'
2025-06-30T09:37:38.9483643+05:30 [Information] Resolved template pattern '{{$var:User.Email}}' to '<EMAIL>' in '{{$var:User.Email}}'
2025-06-30T09:37:38.9483995+05:30 [Information] Resolved template '{{$var:User.Email}}' to '<EMAIL>'
2025-06-30T09:37:38.9484534+05:30 [Information] Mapped column '{{$var:User.Email}}' to variable 'Emaillocal' with value '<EMAIL>'
2025-06-30T09:37:38.9484950+05:30 [Information] Processing $var: path 'Usernamelocal'
2025-06-30T09:37:38.9485319+05:30 [Information] Checking if 'Usernamelocal' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:38.9485699+05:30 [Information] Resolving path 'Usernamelocal' using global variables.
2025-06-30T09:37:38.9486053+05:30 [Information] Resolved JPath '$var:Usernamelocal' to 'John'
2025-06-30T09:37:38.9486431+05:30 [Information] Resolved template pattern '{{$var:Usernamelocal}}' to 'John' in 'Running Test for {{$var:Usernamelocal}}'
2025-06-30T09:37:38.9486812+05:30 [Information] Resolved template 'Running Test for {{$var:Usernamelocal}}' to 'Running Test for John'
2025-06-30T09:37:38.9488492+05:30 [Information] Test Iteration 2/2: Running Test for John
2025-06-30T09:37:38.9489661+05:30 [Information] Processing $var: path 'Usernamelocal'
2025-06-30T09:37:38.9490033+05:30 [Information] Checking if 'Usernamelocal' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:38.9490442+05:30 [Information] Resolving path 'Usernamelocal' using global variables.
2025-06-30T09:37:38.9490815+05:30 [Information] Resolved JPath '$var:Usernamelocal' to 'John'
2025-06-30T09:37:38.9491169+05:30 [Information] Resolved template pattern '{{$var:Usernamelocal}}' to 'John' in 'Step to get token for {{$var:Usernamelocal}}'
2025-06-30T09:37:38.9491521+05:30 [Information] Resolved template 'Step to get token for {{$var:Usernamelocal}}' to 'Step to get token for John'
2025-06-30T09:37:38.9491965+05:30 [Information] Set Value to Step '75c37223-3a73-4220-a4af-522e32464734' name:
"Step to get token for John"
2025-06-30T09:37:38.9492338+05:30 [Information] Set Value to Step '75c37223-3a73-4220-a4af-522e32464734' description:
"Step to get token for Tom"
2025-06-30T09:37:38.9492701+05:30 [Information] [ RUN      ] Itterative Test and Step > Step to get token for John
2025-06-30T09:37:38.9494786+05:30 [Information] Executing single instance of step 'Step to get token for {{$var:Usernamelocal}}'.
2025-06-30T09:37:38.9495703+05:30 [Information] Executing step 'Step to get token for John' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "e0307eff-1cb7-4523-a9d8-d0eb9677f935",
        "tables": {},
        "variables": {},
        "name": "Step to get token for Tom",
        "description": "Step to get token for Tom",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "Tom",
            "email": "<EMAIL>",
            "org": "Org1"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:07:43 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"Tom\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"org\": \"Org1\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "74",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620d8f-38e8db126912ea4c7c533e95"
            },
            "json": {
              "email": "<EMAIL>",
              "org": "Org1",
              "username": "Tom"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 759,
          "startTime": "2025-06-30T04:07:37.1377533Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      },
      {
        "id": "59bbd061-39e3-4683-8ec9-94cbaa5976a7",
        "tables": {},
        "variables": {
          "Name": "User",
          "Role": "User"
        },
        "name": "Itterative Step for user Tom",
        "description": "Itterative Step",
        "context": {
          "repeatForTable": "UserRolesTable",
          "row": {
            "Name": "User"
          },
          "rowIndex": 1
        },
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "Tom",
            "role": "User"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:07:44 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"Tom\",\r\n  \"role\": \"User\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "44",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620d90-553375761cc6cb1264351781"
            },
            "json": {
              "role": "User",
              "username": "Tom"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 268,
          "startTime": "2025-06-30T04:07:37.8992651Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      },
      {
        "id": "75c37223-3a73-4220-a4af-522e32464734",
        "tables": {},
        "variables": {},
        "name": "Step to get token for John",
        "description": "Step to get token for Tom"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaseUrl": "https://httpbin.org",
      "User": {
        "Username": "John",
        "Email": "<EMAIL>"
      },
      "OrgName": "Org2",
      "Usernamelocal": "John",
      "Emaillocal": "<EMAIL>"
    },
    "tables": {
      "UserTable": [
        {
          "User": {
            "Username": "Tom",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org1",
          "NewUsername": "Tom"
        },
        {
          "User": {
            "Username": "John",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org2"
        }
      ],
      "UserRolesTable": [
        {
          "Name": "Admin"
        },
        {
          "Name": "User"
        }
      ]
    },
    "context": {
      "repeatForTable": "UserTable",
      "row": {
        "User": {
          "Username": "John",
          "Email": "<EMAIL>"
        },
        "OrgName": "Org2"
      },
      "rowIndex": 1
    }
  }
}
2025-06-30T09:37:38.9496383+05:30 [Information] Processing $var: path 'BaseUrl'
2025-06-30T09:37:38.9496820+05:30 [Information] Checking if 'BaseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:38.9497210+05:30 [Information] Resolving path 'BaseUrl' using global variables.
2025-06-30T09:37:38.9497570+05:30 [Information] Resolved JPath '$var:BaseUrl' to 'https://httpbin.org'
2025-06-30T09:37:38.9497944+05:30 [Information] Resolved template pattern '{{$var:BaseUrl}}' to 'https://httpbin.org' in '{{$var:BaseUrl}}/post'
2025-06-30T09:37:38.9498304+05:30 [Information] Resolved template '{{$var:BaseUrl}}/post' to 'https://httpbin.org/post'
2025-06-30T09:37:38.9498866+05:30 [Information] Resolving path 'row.User.Username' using test context.
2025-06-30T09:37:38.9499273+05:30 [Information] Resolved JPath '$test:row.User.Username' to 'John'
2025-06-30T09:37:38.9499624+05:30 [Information] Resolved template pattern '{{$test:row.User.Username}}' to 'John' in '{
  "username": "{{$test:row.User.Username}}",
  "email": "{{$var:Emaillocal}}",
  "org": "{{$var:OrgName}}"
}'
2025-06-30T09:37:38.9499977+05:30 [Information] Processing $var: path 'Emaillocal'
2025-06-30T09:37:38.9500372+05:30 [Information] Checking if 'Emaillocal' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:38.9500734+05:30 [Information] Resolving path 'Emaillocal' using global variables.
2025-06-30T09:37:38.9501084+05:30 [Information] Resolved JPath '$var:Emaillocal' to '<EMAIL>'
2025-06-30T09:37:38.9501423+05:30 [Information] Resolved template pattern '{{$var:Emaillocal}}' to '<EMAIL>' in '{
  "username": "{{$test:row.User.Username}}",
  "email": "{{$var:Emaillocal}}",
  "org": "{{$var:OrgName}}"
}'
2025-06-30T09:37:38.9501890+05:30 [Information] Processing $var: path 'OrgName'
2025-06-30T09:37:38.9502274+05:30 [Information] Checking if 'OrgName' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:38.9502623+05:30 [Information] Resolving path 'OrgName' using global variables.
2025-06-30T09:37:38.9502998+05:30 [Information] Resolved JPath '$var:OrgName' to 'Org2'
2025-06-30T09:37:38.9503342+05:30 [Information] Resolved template pattern '{{$var:OrgName}}' to 'Org2' in '{
  "username": "{{$test:row.User.Username}}",
  "email": "{{$var:Emaillocal}}",
  "org": "{{$var:OrgName}}"
}'
2025-06-30T09:37:38.9503692+05:30 [Information] Resolved template '{
  "username": "{{$test:row.User.Username}}",
  "email": "{{$var:Emaillocal}}",
  "org": "{{$var:OrgName}}"
}' to '{
  "username": "John",
  "email": "<EMAIL>",
  "org": "Org2"
}'
2025-06-30T09:37:38.9504308+05:30 [Information] Set Value to Step '75c37223-3a73-4220-a4af-522e32464734' input:
{
  "Method": "POST",
  "RequestUri": "https://httpbin.org/post",
  "Headers": {
    "Content-Type": "application/json"
  },
  "Content": {
    "username": "John",
    "email": "<EMAIL>",
    "org": "Org2"
  }
}
2025-06-30T09:37:39.2031473+05:30 [Information] Set Value to Step '75c37223-3a73-4220-a4af-522e32464734' output:
{
  "statusCode": 200,
  "reasonPhrase": "OK",
  "headers": [
    {
      "Key": "Date",
      "Value": [
        "Mon, 30 Jun 2025 04:07:44 GMT"
      ]
    },
    {
      "Key": "Connection",
      "Value": [
        "keep-alive"
      ]
    },
    {
      "Key": "Server",
      "Value": [
        "gunicorn/19.9.0"
      ]
    },
    {
      "Key": "Access-Control-Allow-Origin",
      "Value": [
        "*"
      ]
    },
    {
      "Key": "Access-Control-Allow-Credentials",
      "Value": [
        "true"
      ]
    }
  ],
  "content": {
    "args": {},
    "data": "{\r\n  \"username\": \"John\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"org\": \"Org2\"\r\n}",
    "files": {},
    "form": {},
    "headers": {
      "Content-Length": "76",
      "Content-Type": "application/json; charset=utf-8",
      "Host": "httpbin.org",
      "X-Amzn-Trace-Id": "Root=1-68620d90-3d3af40047437f505ba6cae1"
    },
    "json": {
      "email": "<EMAIL>",
      "org": "Org2",
      "username": "John"
    },
    "origin": "**************",
    "url": "https://httpbin.org/post"
  }
}
2025-06-30T09:37:39.2044226+05:30 [Information] Resolving path 'output.content.json.username' using step input/output.
2025-06-30T09:37:39.2050256+05:30 [Information] Resolved JPath 'output.content.json.username' to 'John'
2025-06-30T09:37:39.2056623+05:30 [Information] Set Value to Step '75c37223-3a73-4220-a4af-522e32464734' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:07:38.949477Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:37:39.2061765+05:30 [Information] Run Asserter : {"Expression1":{"Value":"200","ErrorMesage":null},"Expression2":{"JPath":"output.statusCode","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:37:39.2065242+05:30 [Information] Resolving path 'output.statusCode' using step input/output.
2025-06-30T09:37:39.2065675+05:30 [Information] Resolved JPath 'output.statusCode' to '200'
2025-06-30T09:37:39.2066216+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$test:row.User.Username}}","ErrorMesage":null},"Expression2":{"JPath":"output.content.json.username","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:37:39.2066618+05:30 [Information] Resolving path 'row.User.Username' using test context.
2025-06-30T09:37:39.2067130+05:30 [Information] Resolved JPath '$test:row.User.Username' to 'John'
2025-06-30T09:37:39.2067613+05:30 [Information] Resolved template pattern '{{$test:row.User.Username}}' to 'John' in '{{$test:row.User.Username}}'
2025-06-30T09:37:39.2068148+05:30 [Information] Resolved template '{{$test:row.User.Username}}' to 'John'
2025-06-30T09:37:39.2069022+05:30 [Information] Resolving path 'output.content.json.username' using step input/output.
2025-06-30T09:37:39.2069419+05:30 [Information] Resolved JPath 'output.content.json.username' to 'John'
2025-06-30T09:37:39.2069954+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$test:row.User.Email}}","ErrorMesage":null},"Expression2":{"JPath":"output.content.json.email","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:37:39.2070379+05:30 [Information] Resolving path 'row.User.Email' using test context.
2025-06-30T09:37:39.2070828+05:30 [Information] Resolved JPath '$test:row.User.Email' to '<EMAIL>'
2025-06-30T09:37:39.2071218+05:30 [Information] Resolved template pattern '{{$test:row.User.Email}}' to '<EMAIL>' in '{{$test:row.User.Email}}'
2025-06-30T09:37:39.2071670+05:30 [Information] Resolved template '{{$test:row.User.Email}}' to '<EMAIL>'
2025-06-30T09:37:39.2072159+05:30 [Information] Resolving path 'output.content.json.email' using step input/output.
2025-06-30T09:37:39.2072572+05:30 [Information] Resolved JPath 'output.content.json.email' to '<EMAIL>'
2025-06-30T09:37:39.2072981+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$test:row.OrgName}}","ErrorMesage":null},"Expression2":{"JPath":"output.content.json.org","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:37:39.2073384+05:30 [Information] Resolving path 'row.OrgName' using test context.
2025-06-30T09:37:39.2073743+05:30 [Information] Resolved JPath '$test:row.OrgName' to 'Org2'
2025-06-30T09:37:39.2074115+05:30 [Information] Resolved template pattern '{{$test:row.OrgName}}' to 'Org2' in '{{$test:row.OrgName}}'
2025-06-30T09:37:39.2074582+05:30 [Information] Resolved template '{{$test:row.OrgName}}' to 'Org2'
2025-06-30T09:37:39.2075246+05:30 [Information] Resolving path 'output.content.json.org' using step input/output.
2025-06-30T09:37:39.2075672+05:30 [Information] Resolved JPath 'output.content.json.org' to 'Org2'
2025-06-30T09:37:39.2076075+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$test:row.NewUsername}}","ErrorMesage":null},"Expression2":{"JPath":"output.content.json.username","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:37:39.2076492+05:30 [Information] Resolving path 'row.NewUsername' using test context.
2025-06-30T09:37:39.2076845+05:30 [Information] Resolved JPath '$test:row.NewUsername' to 'John'
2025-06-30T09:37:39.2077209+05:30 [Information] Resolved template pattern '{{$test:row.NewUsername}}' to 'John' in '{{$test:row.NewUsername}}'
2025-06-30T09:37:39.2077635+05:30 [Information] Resolved template '{{$test:row.NewUsername}}' to 'John'
2025-06-30T09:37:39.2078118+05:30 [Information] Resolving path 'output.content.json.username' using step input/output.
2025-06-30T09:37:39.2078524+05:30 [Information] Resolved JPath 'output.content.json.username' to 'John'
2025-06-30T09:37:39.2079100+05:30 [Information] Set Value to Step '75c37223-3a73-4220-a4af-522e32464734' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 257,
  "startTime": "2025-06-30T04:07:38.949477Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:37:39.2079970+05:30 [Information] Step 'Step to get token for John' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "e0307eff-1cb7-4523-a9d8-d0eb9677f935",
        "tables": {},
        "variables": {},
        "name": "Step to get token for Tom",
        "description": "Step to get token for Tom",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "Tom",
            "email": "<EMAIL>",
            "org": "Org1"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:07:43 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"Tom\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"org\": \"Org1\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "74",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620d8f-38e8db126912ea4c7c533e95"
            },
            "json": {
              "email": "<EMAIL>",
              "org": "Org1",
              "username": "Tom"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 759,
          "startTime": "2025-06-30T04:07:37.1377533Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      },
      {
        "id": "59bbd061-39e3-4683-8ec9-94cbaa5976a7",
        "tables": {},
        "variables": {
          "Name": "User",
          "Role": "User"
        },
        "name": "Itterative Step for user Tom",
        "description": "Itterative Step",
        "context": {
          "repeatForTable": "UserRolesTable",
          "row": {
            "Name": "User"
          },
          "rowIndex": 1
        },
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "Tom",
            "role": "User"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:07:44 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"Tom\",\r\n  \"role\": \"User\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "44",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620d90-553375761cc6cb1264351781"
            },
            "json": {
              "role": "User",
              "username": "Tom"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 268,
          "startTime": "2025-06-30T04:07:37.8992651Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      },
      {
        "id": "75c37223-3a73-4220-a4af-522e32464734",
        "tables": {},
        "variables": {},
        "name": "Step to get token for John",
        "description": "Step to get token for Tom",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "John",
            "email": "<EMAIL>",
            "org": "Org2"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:07:44 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"John\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"org\": \"Org2\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "76",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620d90-3d3af40047437f505ba6cae1"
            },
            "json": {
              "email": "<EMAIL>",
              "org": "Org2",
              "username": "John"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 257,
          "startTime": "2025-06-30T04:07:38.949477Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaseUrl": "https://httpbin.org",
      "User": {
        "Username": "John",
        "Email": "<EMAIL>"
      },
      "OrgName": "Org2",
      "Usernamelocal": "John",
      "Emaillocal": "<EMAIL>"
    },
    "tables": {
      "UserTable": [
        {
          "User": {
            "Username": "Tom",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org1",
          "NewUsername": "Tom"
        },
        {
          "User": {
            "Username": "John",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org2",
          "NewUsername": "John"
        }
      ],
      "UserRolesTable": [
        {
          "Name": "Admin"
        },
        {
          "Name": "User"
        }
      ]
    },
    "context": {
      "repeatForTable": "UserTable",
      "row": {
        "User": {
          "Username": "John",
          "Email": "<EMAIL>"
        },
        "OrgName": "Org2",
        "NewUsername": "John"
      },
      "rowIndex": 1
    }
  }
}
2025-06-30T09:37:39.2081804+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:37:39.2082730+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:37:39.2083219+05:30 [Information] No performance data found for step 'Step to get token for {{$var:Usernamelocal}}' (http)
2025-06-30T09:37:39.2083612+05:30 [Information] [ OK       ]
2025-06-30T09:37:39.2086662+05:30 [Information] Processing $var: path 'Usernamelocal'
2025-06-30T09:37:39.2087750+05:30 [Information] Checking if 'Usernamelocal' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:39.2088287+05:30 [Information] Resolving path 'Usernamelocal' using global variables.
2025-06-30T09:37:39.2088857+05:30 [Information] Resolved JPath '$var:Usernamelocal' to 'John'
2025-06-30T09:37:39.2089279+05:30 [Information] Resolved template pattern '{{$var:Usernamelocal}}' to 'John' in 'Itterative Step for user {{$var:Usernamelocal}}'
2025-06-30T09:37:39.2091276+05:30 [Information] Resolved template 'Itterative Step for user {{$var:Usernamelocal}}' to 'Itterative Step for user John'
2025-06-30T09:37:39.2092678+05:30 [Information] Set Value to Step '7cf626c0-566e-4336-a63f-98488dce1149' name:
"Itterative Step for user John"
2025-06-30T09:37:39.2093376+05:30 [Information] Set Value to Step '7cf626c0-566e-4336-a63f-98488dce1149' description:
"Itterative Step"
2025-06-30T09:37:39.2093827+05:30 [Information] [ RUN      ] Itterative Test and Step > Itterative Step for user John
2025-06-30T09:37:39.2096400+05:30 [Information] Executing step 'Itterative Step for user {{$var:Usernamelocal}}' for row 1/2 in table 'UserRolesTable'.
2025-06-30T09:37:39.2097969+05:30 [Information] Default-mapped column 'Name' to variable 'Name' with value 'Admin'
2025-06-30T09:37:39.2098664+05:30 [Information] Resolved path 'row.Name' in step-specific context.
2025-06-30T09:37:39.2099099+05:30 [Information] Resolved JPath '$step:row.Name' to 'Admin'
2025-06-30T09:37:39.2099459+05:30 [Information] Resolved template pattern '{{$step:row.Name}}' to 'Admin' in '{{$step:row.Name}}'
2025-06-30T09:37:39.2099827+05:30 [Information] Resolved template '{{$step:row.Name}}' to 'Admin'
2025-06-30T09:37:39.2100510+05:30 [Information] Mapped column '{{$step:row.Name}}' to variable 'Role' with value 'Admin'
2025-06-30T09:37:39.2100928+05:30 [Information] Processing $var: path 'Usernamelocal'
2025-06-30T09:37:39.2101311+05:30 [Information] Checking if 'Usernamelocal' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:39.2101673+05:30 [Information] Resolving path 'Usernamelocal' using global variables.
2025-06-30T09:37:39.2102031+05:30 [Information] Resolved JPath '$var:Usernamelocal' to 'John'
2025-06-30T09:37:39.2102375+05:30 [Information] Resolved template pattern '{{$var:Usernamelocal}}' to 'John' in 'Running for {{$var:Usernamelocal}} and role {{$step:row.Name}}'
2025-06-30T09:37:39.2102746+05:30 [Information] Resolved path 'row.Name' in step-specific context.
2025-06-30T09:37:39.2103103+05:30 [Information] Resolved JPath '$step:row.Name' to 'Admin'
2025-06-30T09:37:39.2103443+05:30 [Information] Resolved template pattern '{{$step:row.Name}}' to 'Admin' in 'Running for {{$var:Usernamelocal}} and role {{$step:row.Name}}'
2025-06-30T09:37:39.2103886+05:30 [Information] Resolved template 'Running for {{$var:Usernamelocal}} and role {{$step:row.Name}}' to 'Running for John and role Admin'
2025-06-30T09:37:39.2106126+05:30 [Information] Step Iteration 1/2: Running for John and role Admin
2025-06-30T09:37:39.2108339+05:30 [Information] Executing step 'Running for John and role Admin' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "e0307eff-1cb7-4523-a9d8-d0eb9677f935",
        "tables": {},
        "variables": {},
        "name": "Step to get token for Tom",
        "description": "Step to get token for Tom",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "Tom",
            "email": "<EMAIL>",
            "org": "Org1"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:07:43 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"Tom\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"org\": \"Org1\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "74",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620d8f-38e8db126912ea4c7c533e95"
            },
            "json": {
              "email": "<EMAIL>",
              "org": "Org1",
              "username": "Tom"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 759,
          "startTime": "2025-06-30T04:07:37.1377533Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      },
      {
        "id": "59bbd061-39e3-4683-8ec9-94cbaa5976a7",
        "tables": {},
        "variables": {
          "Name": "User",
          "Role": "User"
        },
        "name": "Itterative Step for user Tom",
        "description": "Itterative Step",
        "context": {
          "repeatForTable": "UserRolesTable",
          "row": {
            "Name": "User"
          },
          "rowIndex": 1
        },
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "Tom",
            "role": "User"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:07:44 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"Tom\",\r\n  \"role\": \"User\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "44",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620d90-553375761cc6cb1264351781"
            },
            "json": {
              "role": "User",
              "username": "Tom"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 268,
          "startTime": "2025-06-30T04:07:37.8992651Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      },
      {
        "id": "75c37223-3a73-4220-a4af-522e32464734",
        "tables": {},
        "variables": {},
        "name": "Step to get token for John",
        "description": "Step to get token for Tom",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "John",
            "email": "<EMAIL>",
            "org": "Org2"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:07:44 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"John\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"org\": \"Org2\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "76",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620d90-3d3af40047437f505ba6cae1"
            },
            "json": {
              "email": "<EMAIL>",
              "org": "Org2",
              "username": "John"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 257,
          "startTime": "2025-06-30T04:07:38.949477Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      },
      {
        "id": "7cf626c0-566e-4336-a63f-98488dce1149",
        "tables": {},
        "variables": {
          "Name": "Admin",
          "Role": "Admin"
        },
        "name": "Itterative Step for user John",
        "description": "Itterative Step",
        "context": {
          "repeatForTable": "UserRolesTable",
          "row": {
            "Name": "Admin"
          },
          "rowIndex": 0
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaseUrl": "https://httpbin.org",
      "User": {
        "Username": "John",
        "Email": "<EMAIL>"
      },
      "OrgName": "Org2",
      "Usernamelocal": "John",
      "Emaillocal": "<EMAIL>"
    },
    "tables": {
      "UserTable": [
        {
          "User": {
            "Username": "Tom",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org1",
          "NewUsername": "Tom"
        },
        {
          "User": {
            "Username": "John",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org2",
          "NewUsername": "John"
        }
      ],
      "UserRolesTable": [
        {
          "Name": "Admin"
        },
        {
          "Name": "User"
        }
      ]
    },
    "context": {
      "repeatForTable": "UserTable",
      "row": {
        "User": {
          "Username": "John",
          "Email": "<EMAIL>"
        },
        "OrgName": "Org2",
        "NewUsername": "John"
      },
      "rowIndex": 1
    }
  }
}
2025-06-30T09:37:39.2109277+05:30 [Information] Processing $var: path 'BaseUrl'
2025-06-30T09:37:39.2109727+05:30 [Information] Checking if 'BaseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:39.2110125+05:30 [Information] Resolving path 'BaseUrl' using global variables.
2025-06-30T09:37:39.2110495+05:30 [Information] Resolved JPath '$var:BaseUrl' to 'https://httpbin.org'
2025-06-30T09:37:39.2110842+05:30 [Information] Resolved template pattern '{{$var:BaseUrl}}' to 'https://httpbin.org' in '{{$var:BaseUrl}}/post'
2025-06-30T09:37:39.2111198+05:30 [Information] Resolved template '{{$var:BaseUrl}}/post' to 'https://httpbin.org/post'
2025-06-30T09:37:39.2111751+05:30 [Information] Resolving path 'row.NewUsername' using test context.
2025-06-30T09:37:39.2112133+05:30 [Information] Resolved JPath '$test:row.NewUsername' to 'John'
2025-06-30T09:37:39.2112478+05:30 [Information] Resolved template pattern '{{$test:row.NewUsername}}' to 'John' in '{
  "username": "{{$test:row.NewUsername}}",
  "role": "{{$var:Role}}"
}'
2025-06-30T09:37:39.2112837+05:30 [Information] Processing $var: path 'Role'
2025-06-30T09:37:39.2113181+05:30 [Information] Checking if 'Role' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:39.2113526+05:30 [Information] Resolved path 'Role' in step-specific variables.
2025-06-30T09:37:39.2113868+05:30 [Information] Resolved JPath '$var:Role' to 'Admin'
2025-06-30T09:37:39.2114212+05:30 [Information] Resolved template pattern '{{$var:Role}}' to 'Admin' in '{
  "username": "{{$test:row.NewUsername}}",
  "role": "{{$var:Role}}"
}'
2025-06-30T09:37:39.2114556+05:30 [Information] Resolved template '{
  "username": "{{$test:row.NewUsername}}",
  "role": "{{$var:Role}}"
}' to '{
  "username": "John",
  "role": "Admin"
}'
2025-06-30T09:37:39.2115155+05:30 [Information] Set Value to Step '7cf626c0-566e-4336-a63f-98488dce1149' input:
{
  "Method": "POST",
  "RequestUri": "https://httpbin.org/post",
  "Headers": {
    "Content-Type": "application/json"
  },
  "Content": {
    "username": "John",
    "role": "Admin"
  }
}
2025-06-30T09:37:39.4795801+05:30 [Information] Set Value to Step '7cf626c0-566e-4336-a63f-98488dce1149' output:
{
  "statusCode": 200,
  "reasonPhrase": "OK",
  "headers": [
    {
      "Key": "Date",
      "Value": [
        "Mon, 30 Jun 2025 04:07:44 GMT"
      ]
    },
    {
      "Key": "Connection",
      "Value": [
        "keep-alive"
      ]
    },
    {
      "Key": "Server",
      "Value": [
        "gunicorn/19.9.0"
      ]
    },
    {
      "Key": "Access-Control-Allow-Origin",
      "Value": [
        "*"
      ]
    },
    {
      "Key": "Access-Control-Allow-Credentials",
      "Value": [
        "true"
      ]
    }
  ],
  "content": {
    "args": {},
    "data": "{\r\n  \"username\": \"John\",\r\n  \"role\": \"Admin\"\r\n}",
    "files": {},
    "form": {},
    "headers": {
      "Content-Length": "46",
      "Content-Type": "application/json; charset=utf-8",
      "Host": "httpbin.org",
      "X-Amzn-Trace-Id": "Root=1-68620d90-2115cc6738f3f5ff7057fc9f"
    },
    "json": {
      "role": "Admin",
      "username": "John"
    },
    "origin": "**************",
    "url": "https://httpbin.org/post"
  }
}
2025-06-30T09:37:39.4797822+05:30 [Information] Set Value to Step '7cf626c0-566e-4336-a63f-98488dce1149' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:07:39.2096069Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:37:39.4798953+05:30 [Information] Run Asserter : {"Expression1":{"Value":"200","ErrorMesage":null},"Expression2":{"JPath":"output.statusCode","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:37:39.4799693+05:30 [Information] Resolving path 'output.statusCode' using step input/output.
2025-06-30T09:37:39.4800220+05:30 [Information] Resolved JPath 'output.statusCode' to '200'
2025-06-30T09:37:39.4800719+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$test:row.NewUsername}}","ErrorMesage":null},"Expression2":{"JPath":"output.content.json.username","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:37:39.4801243+05:30 [Information] Resolving path 'row.NewUsername' using test context.
2025-06-30T09:37:39.4801635+05:30 [Information] Resolved JPath '$test:row.NewUsername' to 'John'
2025-06-30T09:37:39.4801996+05:30 [Information] Resolved template pattern '{{$test:row.NewUsername}}' to 'John' in '{{$test:row.NewUsername}}'
2025-06-30T09:37:39.4802381+05:30 [Information] Resolved template '{{$test:row.NewUsername}}' to 'John'
2025-06-30T09:37:39.4803243+05:30 [Information] Resolving path 'output.content.json.username' using step input/output.
2025-06-30T09:37:39.4803712+05:30 [Information] Resolved JPath 'output.content.json.username' to 'John'
2025-06-30T09:37:39.4804229+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$step:row.Name}}","ErrorMesage":null},"Expression2":{"JPath":"output.content.json.role","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:37:39.4805900+05:30 [Information] Resolved path 'row.Name' in step-specific context.
2025-06-30T09:37:39.4806884+05:30 [Information] Resolved JPath '$step:row.Name' to 'Admin'
2025-06-30T09:37:39.4807360+05:30 [Information] Resolved template pattern '{{$step:row.Name}}' to 'Admin' in '{{$step:row.Name}}'
2025-06-30T09:37:39.4807859+05:30 [Information] Resolved template '{{$step:row.Name}}' to 'Admin'
2025-06-30T09:37:39.4808543+05:30 [Information] Resolving path 'output.content.json.role' using step input/output.
2025-06-30T09:37:39.4808999+05:30 [Information] Resolved JPath 'output.content.json.role' to 'Admin'
2025-06-30T09:37:39.4809612+05:30 [Information] Set Value to Step '7cf626c0-566e-4336-a63f-98488dce1149' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 269,
  "startTime": "2025-06-30T04:07:39.2096069Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:37:39.4810500+05:30 [Information] Step 'Running for John and role Admin' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "e0307eff-1cb7-4523-a9d8-d0eb9677f935",
        "tables": {},
        "variables": {},
        "name": "Step to get token for Tom",
        "description": "Step to get token for Tom",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "Tom",
            "email": "<EMAIL>",
            "org": "Org1"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:07:43 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"Tom\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"org\": \"Org1\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "74",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620d8f-38e8db126912ea4c7c533e95"
            },
            "json": {
              "email": "<EMAIL>",
              "org": "Org1",
              "username": "Tom"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 759,
          "startTime": "2025-06-30T04:07:37.1377533Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      },
      {
        "id": "59bbd061-39e3-4683-8ec9-94cbaa5976a7",
        "tables": {},
        "variables": {
          "Name": "User",
          "Role": "User"
        },
        "name": "Itterative Step for user Tom",
        "description": "Itterative Step",
        "context": {
          "repeatForTable": "UserRolesTable",
          "row": {
            "Name": "User"
          },
          "rowIndex": 1
        },
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "Tom",
            "role": "User"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:07:44 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"Tom\",\r\n  \"role\": \"User\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "44",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620d90-553375761cc6cb1264351781"
            },
            "json": {
              "role": "User",
              "username": "Tom"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 268,
          "startTime": "2025-06-30T04:07:37.8992651Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      },
      {
        "id": "75c37223-3a73-4220-a4af-522e32464734",
        "tables": {},
        "variables": {},
        "name": "Step to get token for John",
        "description": "Step to get token for Tom",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "John",
            "email": "<EMAIL>",
            "org": "Org2"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:07:44 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"John\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"org\": \"Org2\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "76",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620d90-3d3af40047437f505ba6cae1"
            },
            "json": {
              "email": "<EMAIL>",
              "org": "Org2",
              "username": "John"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 257,
          "startTime": "2025-06-30T04:07:38.949477Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      },
      {
        "id": "7cf626c0-566e-4336-a63f-98488dce1149",
        "tables": {},
        "variables": {
          "Name": "Admin",
          "Role": "Admin"
        },
        "name": "Itterative Step for user John",
        "description": "Itterative Step",
        "context": {
          "repeatForTable": "UserRolesTable",
          "row": {
            "Name": "Admin"
          },
          "rowIndex": 0
        },
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "John",
            "role": "Admin"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:07:44 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"John\",\r\n  \"role\": \"Admin\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "46",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620d90-2115cc6738f3f5ff7057fc9f"
            },
            "json": {
              "role": "Admin",
              "username": "John"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 269,
          "startTime": "2025-06-30T04:07:39.2096069Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaseUrl": "https://httpbin.org",
      "User": {
        "Username": "John",
        "Email": "<EMAIL>"
      },
      "OrgName": "Org2",
      "Usernamelocal": "John",
      "Emaillocal": "<EMAIL>"
    },
    "tables": {
      "UserTable": [
        {
          "User": {
            "Username": "Tom",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org1",
          "NewUsername": "Tom"
        },
        {
          "User": {
            "Username": "John",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org2",
          "NewUsername": "John"
        }
      ],
      "UserRolesTable": [
        {
          "Name": "Admin"
        },
        {
          "Name": "User"
        }
      ]
    },
    "context": {
      "repeatForTable": "UserTable",
      "row": {
        "User": {
          "Username": "John",
          "Email": "<EMAIL>"
        },
        "OrgName": "Org2",
        "NewUsername": "John"
      },
      "rowIndex": 1
    }
  }
}
2025-06-30T09:37:39.4811331+05:30 [Information] Executing step 'Itterative Step for user {{$var:Usernamelocal}}' for row 2/2 in table 'UserRolesTable'.
2025-06-30T09:37:39.4811917+05:30 [Information] Default-mapped column 'Name' to variable 'Name' with value 'User'
2025-06-30T09:37:39.4812461+05:30 [Information] Resolved path 'row.Name' in step-specific context.
2025-06-30T09:37:39.4812847+05:30 [Information] Resolved JPath '$step:row.Name' to 'User'
2025-06-30T09:37:39.4813449+05:30 [Information] Resolved template pattern '{{$step:row.Name}}' to 'User' in '{{$step:row.Name}}'
2025-06-30T09:37:39.4814291+05:30 [Information] Resolved template '{{$step:row.Name}}' to 'User'
2025-06-30T09:37:39.4815097+05:30 [Information] Mapped column '{{$step:row.Name}}' to variable 'Role' with value 'User'
2025-06-30T09:37:39.4815614+05:30 [Information] Processing $var: path 'Usernamelocal'
2025-06-30T09:37:39.4816063+05:30 [Information] Checking if 'Usernamelocal' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:39.4816638+05:30 [Information] Resolving path 'Usernamelocal' using global variables.
2025-06-30T09:37:39.4817074+05:30 [Information] Resolved JPath '$var:Usernamelocal' to 'John'
2025-06-30T09:37:39.4817496+05:30 [Information] Resolved template pattern '{{$var:Usernamelocal}}' to 'John' in 'Running for {{$var:Usernamelocal}} and role {{$step:row.Name}}'
2025-06-30T09:37:39.4818031+05:30 [Information] Resolved path 'row.Name' in step-specific context.
2025-06-30T09:37:39.4818442+05:30 [Information] Resolved JPath '$step:row.Name' to 'User'
2025-06-30T09:37:39.4818830+05:30 [Information] Resolved template pattern '{{$step:row.Name}}' to 'User' in 'Running for {{$var:Usernamelocal}} and role {{$step:row.Name}}'
2025-06-30T09:37:39.4819243+05:30 [Information] Resolved template 'Running for {{$var:Usernamelocal}} and role {{$step:row.Name}}' to 'Running for John and role User'
2025-06-30T09:37:39.4821305+05:30 [Information] Step Iteration 2/2: Running for John and role User
2025-06-30T09:37:39.4822799+05:30 [Information] Executing step 'Running for John and role User' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "e0307eff-1cb7-4523-a9d8-d0eb9677f935",
        "tables": {},
        "variables": {},
        "name": "Step to get token for Tom",
        "description": "Step to get token for Tom",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "Tom",
            "email": "<EMAIL>",
            "org": "Org1"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:07:43 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"Tom\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"org\": \"Org1\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "74",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620d8f-38e8db126912ea4c7c533e95"
            },
            "json": {
              "email": "<EMAIL>",
              "org": "Org1",
              "username": "Tom"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 759,
          "startTime": "2025-06-30T04:07:37.1377533Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      },
      {
        "id": "59bbd061-39e3-4683-8ec9-94cbaa5976a7",
        "tables": {},
        "variables": {
          "Name": "User",
          "Role": "User"
        },
        "name": "Itterative Step for user Tom",
        "description": "Itterative Step",
        "context": {
          "repeatForTable": "UserRolesTable",
          "row": {
            "Name": "User"
          },
          "rowIndex": 1
        },
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "Tom",
            "role": "User"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:07:44 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"Tom\",\r\n  \"role\": \"User\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "44",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620d90-553375761cc6cb1264351781"
            },
            "json": {
              "role": "User",
              "username": "Tom"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 268,
          "startTime": "2025-06-30T04:07:37.8992651Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      },
      {
        "id": "75c37223-3a73-4220-a4af-522e32464734",
        "tables": {},
        "variables": {},
        "name": "Step to get token for John",
        "description": "Step to get token for Tom",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "John",
            "email": "<EMAIL>",
            "org": "Org2"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:07:44 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"John\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"org\": \"Org2\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "76",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620d90-3d3af40047437f505ba6cae1"
            },
            "json": {
              "email": "<EMAIL>",
              "org": "Org2",
              "username": "John"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 257,
          "startTime": "2025-06-30T04:07:38.949477Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      },
      {
        "id": "7cf626c0-566e-4336-a63f-98488dce1149",
        "tables": {},
        "variables": {
          "Name": "User",
          "Role": "User"
        },
        "name": "Itterative Step for user John",
        "description": "Itterative Step",
        "context": {
          "repeatForTable": "UserRolesTable",
          "row": {
            "Name": "User"
          },
          "rowIndex": 1
        },
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "John",
            "role": "Admin"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:07:44 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"John\",\r\n  \"role\": \"Admin\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "46",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620d90-2115cc6738f3f5ff7057fc9f"
            },
            "json": {
              "role": "Admin",
              "username": "John"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 269,
          "startTime": "2025-06-30T04:07:39.2096069Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaseUrl": "https://httpbin.org",
      "User": {
        "Username": "John",
        "Email": "<EMAIL>"
      },
      "OrgName": "Org2",
      "Usernamelocal": "John",
      "Emaillocal": "<EMAIL>"
    },
    "tables": {
      "UserTable": [
        {
          "User": {
            "Username": "Tom",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org1",
          "NewUsername": "Tom"
        },
        {
          "User": {
            "Username": "John",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org2",
          "NewUsername": "John"
        }
      ],
      "UserRolesTable": [
        {
          "Name": "Admin"
        },
        {
          "Name": "User"
        }
      ]
    },
    "context": {
      "repeatForTable": "UserTable",
      "row": {
        "User": {
          "Username": "John",
          "Email": "<EMAIL>"
        },
        "OrgName": "Org2",
        "NewUsername": "John"
      },
      "rowIndex": 1
    }
  }
}
2025-06-30T09:37:39.4823660+05:30 [Information] Processing $var: path 'BaseUrl'
2025-06-30T09:37:39.4824105+05:30 [Information] Checking if 'BaseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:39.4824496+05:30 [Information] Resolving path 'BaseUrl' using global variables.
2025-06-30T09:37:39.4824838+05:30 [Information] Resolved JPath '$var:BaseUrl' to 'https://httpbin.org'
2025-06-30T09:37:39.4825204+05:30 [Information] Resolved template pattern '{{$var:BaseUrl}}' to 'https://httpbin.org' in '{{$var:BaseUrl}}/post'
2025-06-30T09:37:39.4825606+05:30 [Information] Resolved template '{{$var:BaseUrl}}/post' to 'https://httpbin.org/post'
2025-06-30T09:37:39.4826196+05:30 [Information] Resolving path 'row.NewUsername' using test context.
2025-06-30T09:37:39.4826630+05:30 [Information] Resolved JPath '$test:row.NewUsername' to 'John'
2025-06-30T09:37:39.4827005+05:30 [Information] Resolved template pattern '{{$test:row.NewUsername}}' to 'John' in '{
  "username": "{{$test:row.NewUsername}}",
  "role": "{{$var:Role}}"
}'
2025-06-30T09:37:39.4827475+05:30 [Information] Processing $var: path 'Role'
2025-06-30T09:37:39.4827972+05:30 [Information] Checking if 'Role' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:39.4828653+05:30 [Information] Resolved path 'Role' in step-specific variables.
2025-06-30T09:37:39.4829172+05:30 [Information] Resolved JPath '$var:Role' to 'User'
2025-06-30T09:37:39.4829659+05:30 [Information] Resolved template pattern '{{$var:Role}}' to 'User' in '{
  "username": "{{$test:row.NewUsername}}",
  "role": "{{$var:Role}}"
}'
2025-06-30T09:37:39.4830200+05:30 [Information] Resolved template '{
  "username": "{{$test:row.NewUsername}}",
  "role": "{{$var:Role}}"
}' to '{
  "username": "John",
  "role": "User"
}'
2025-06-30T09:37:39.4831055+05:30 [Information] Set Value to Step '7cf626c0-566e-4336-a63f-98488dce1149' input:
{
  "Method": "POST",
  "RequestUri": "https://httpbin.org/post",
  "Headers": {
    "Content-Type": "application/json"
  },
  "Content": {
    "username": "John",
    "role": "User"
  }
}
2025-06-30T09:37:40.1400508+05:30 [Information] Set Value to Step '7cf626c0-566e-4336-a63f-98488dce1149' output:
{
  "statusCode": 200,
  "reasonPhrase": "OK",
  "headers": [
    {
      "Key": "Date",
      "Value": [
        "Mon, 30 Jun 2025 04:07:45 GMT"
      ]
    },
    {
      "Key": "Connection",
      "Value": [
        "keep-alive"
      ]
    },
    {
      "Key": "Server",
      "Value": [
        "gunicorn/19.9.0"
      ]
    },
    {
      "Key": "Access-Control-Allow-Origin",
      "Value": [
        "*"
      ]
    },
    {
      "Key": "Access-Control-Allow-Credentials",
      "Value": [
        "true"
      ]
    }
  ],
  "content": {
    "args": {},
    "data": "{\r\n  \"username\": \"John\",\r\n  \"role\": \"User\"\r\n}",
    "files": {},
    "form": {},
    "headers": {
      "Content-Length": "45",
      "Content-Type": "application/json; charset=utf-8",
      "Host": "httpbin.org",
      "X-Amzn-Trace-Id": "Root=1-68620d91-1ef7c2fe45f0fae65f631624"
    },
    "json": {
      "role": "User",
      "username": "John"
    },
    "origin": "**************",
    "url": "https://httpbin.org/post"
  }
}
2025-06-30T09:37:40.1408289+05:30 [Information] Set Value to Step '7cf626c0-566e-4336-a63f-98488dce1149' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:07:39.2096069Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:37:40.1412386+05:30 [Information] Run Asserter : {"Expression1":{"Value":"200","ErrorMesage":null},"Expression2":{"JPath":"output.statusCode","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:37:40.1413849+05:30 [Information] Resolving path 'output.statusCode' using step input/output.
2025-06-30T09:37:40.1414385+05:30 [Information] Resolved JPath 'output.statusCode' to '200'
2025-06-30T09:37:40.1414836+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$test:row.NewUsername}}","ErrorMesage":null},"Expression2":{"JPath":"output.content.json.username","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:37:40.1415742+05:30 [Information] Resolving path 'row.NewUsername' using test context.
2025-06-30T09:37:40.1416681+05:30 [Information] Resolved JPath '$test:row.NewUsername' to 'John'
2025-06-30T09:37:40.1417135+05:30 [Information] Resolved template pattern '{{$test:row.NewUsername}}' to 'John' in '{{$test:row.NewUsername}}'
2025-06-30T09:37:40.1417877+05:30 [Information] Resolved template '{{$test:row.NewUsername}}' to 'John'
2025-06-30T09:37:40.1418907+05:30 [Information] Resolving path 'output.content.json.username' using step input/output.
2025-06-30T09:37:40.1419363+05:30 [Information] Resolved JPath 'output.content.json.username' to 'John'
2025-06-30T09:37:40.1420031+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$step:row.Name}}","ErrorMesage":null},"Expression2":{"JPath":"output.content.json.role","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:37:40.1420485+05:30 [Information] Resolved path 'row.Name' in step-specific context.
2025-06-30T09:37:40.1420943+05:30 [Information] Resolved JPath '$step:row.Name' to 'User'
2025-06-30T09:37:40.1421301+05:30 [Information] Resolved template pattern '{{$step:row.Name}}' to 'User' in '{{$step:row.Name}}'
2025-06-30T09:37:40.1422494+05:30 [Information] Resolved template '{{$step:row.Name}}' to 'User'
2025-06-30T09:37:40.1423219+05:30 [Information] Resolving path 'output.content.json.role' using step input/output.
2025-06-30T09:37:40.1423724+05:30 [Information] Resolved JPath 'output.content.json.role' to 'User'
2025-06-30T09:37:40.1424216+05:30 [Information] Set Value to Step '7cf626c0-566e-4336-a63f-98488dce1149' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 659,
  "startTime": "2025-06-30T04:07:39.2096069Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:37:40.1425008+05:30 [Information] Step 'Running for John and role User' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "e0307eff-1cb7-4523-a9d8-d0eb9677f935",
        "tables": {},
        "variables": {},
        "name": "Step to get token for Tom",
        "description": "Step to get token for Tom",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "Tom",
            "email": "<EMAIL>",
            "org": "Org1"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:07:43 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"Tom\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"org\": \"Org1\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "74",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620d8f-38e8db126912ea4c7c533e95"
            },
            "json": {
              "email": "<EMAIL>",
              "org": "Org1",
              "username": "Tom"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 759,
          "startTime": "2025-06-30T04:07:37.1377533Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      },
      {
        "id": "59bbd061-39e3-4683-8ec9-94cbaa5976a7",
        "tables": {},
        "variables": {
          "Name": "User",
          "Role": "User"
        },
        "name": "Itterative Step for user Tom",
        "description": "Itterative Step",
        "context": {
          "repeatForTable": "UserRolesTable",
          "row": {
            "Name": "User"
          },
          "rowIndex": 1
        },
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "Tom",
            "role": "User"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:07:44 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"Tom\",\r\n  \"role\": \"User\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "44",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620d90-553375761cc6cb1264351781"
            },
            "json": {
              "role": "User",
              "username": "Tom"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 268,
          "startTime": "2025-06-30T04:07:37.8992651Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      },
      {
        "id": "75c37223-3a73-4220-a4af-522e32464734",
        "tables": {},
        "variables": {},
        "name": "Step to get token for John",
        "description": "Step to get token for Tom",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "John",
            "email": "<EMAIL>",
            "org": "Org2"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:07:44 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"John\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"org\": \"Org2\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "76",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620d90-3d3af40047437f505ba6cae1"
            },
            "json": {
              "email": "<EMAIL>",
              "org": "Org2",
              "username": "John"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 257,
          "startTime": "2025-06-30T04:07:38.949477Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      },
      {
        "id": "7cf626c0-566e-4336-a63f-98488dce1149",
        "tables": {},
        "variables": {
          "Name": "User",
          "Role": "User"
        },
        "name": "Itterative Step for user John",
        "description": "Itterative Step",
        "context": {
          "repeatForTable": "UserRolesTable",
          "row": {
            "Name": "User"
          },
          "rowIndex": 1
        },
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "John",
            "role": "User"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:07:45 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"John\",\r\n  \"role\": \"User\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "45",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620d91-1ef7c2fe45f0fae65f631624"
            },
            "json": {
              "role": "User",
              "username": "John"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 659,
          "startTime": "2025-06-30T04:07:39.2096069Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaseUrl": "https://httpbin.org",
      "User": {
        "Username": "John",
        "Email": "<EMAIL>"
      },
      "OrgName": "Org2",
      "Usernamelocal": "John",
      "Emaillocal": "<EMAIL>"
    },
    "tables": {
      "UserTable": [
        {
          "User": {
            "Username": "Tom",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org1",
          "NewUsername": "Tom"
        },
        {
          "User": {
            "Username": "John",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org2",
          "NewUsername": "John"
        }
      ],
      "UserRolesTable": [
        {
          "Name": "Admin"
        },
        {
          "Name": "User"
        }
      ]
    },
    "context": {
      "repeatForTable": "UserTable",
      "row": {
        "User": {
          "Username": "John",
          "Email": "<EMAIL>"
        },
        "OrgName": "Org2",
        "NewUsername": "John"
      },
      "rowIndex": 1
    }
  }
}
2025-06-30T09:37:40.1425610+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:37:40.1426114+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:37:40.1426511+05:30 [Information] No performance data found for step 'Itterative Step for user {{$var:Usernamelocal}}' (http)
2025-06-30T09:37:40.1426993+05:30 [Information] [ OK       ]
2025-06-30T09:37:40.1430306+05:30 [Information] [----------] 2 steps from Itterative Test and Step (3007 ms total)
