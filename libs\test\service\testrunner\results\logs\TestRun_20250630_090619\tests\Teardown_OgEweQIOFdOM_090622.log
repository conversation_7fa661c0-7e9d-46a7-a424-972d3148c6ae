2025-06-30T09:06:22.7989662+05:30 [Information] [----------] 1 step from Teardown
2025-06-30T09:06:22.7994678+05:30 [Information] Set Value to Step 'c2ad3631-c53c-408d-889d-344b5ad2bc5c' name:
"Clean up Database"
2025-06-30T09:06:22.7995929+05:30 [Information] Set Value to Step 'c2ad3631-c53c-408d-889d-344b5ad2bc5c' description:
"Tear down the database state"
2025-06-30T09:06:22.7997013+05:30 [Information] [ RUN      ] Teardown > Clean up Database
2025-06-30T09:06:22.7999961+05:30 [Information] Executing single instance of step 'Clean up Database'.
2025-06-30T09:06:22.8001102+05:30 [Information] Executing step 'Clean up Database' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "c2ad3631-c53c-408d-889d-344b5ad2bc5c",
        "tables": {},
        "variables": {},
        "name": "Clean up Database",
        "description": "Tear down the database state"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:06:22.8003179+05:30 [Information] Processing $var: path 'baseUrl'
2025-06-30T09:06:22.8004060+05:30 [Information] Checking if 'baseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:06:22.8004687+05:30 [Information] Resolving path 'baseUrl' using global variables.
2025-06-30T09:06:22.8005172+05:30 [Information] Resolved JPath '$var:baseUrl' to 'https://httpbin.org'
2025-06-30T09:06:22.8005586+05:30 [Information] Resolved template pattern '{{$var:baseUrl}}' to 'https://httpbin.org' in '{{$var:baseUrl}}/post'
2025-06-30T09:06:22.8006075+05:30 [Information] Resolved template '{{$var:baseUrl}}/post' to 'https://httpbin.org/post'
2025-06-30T09:06:22.8007710+05:30 [Information] Set Value to Step 'c2ad3631-c53c-408d-889d-344b5ad2bc5c' input:
{
  "Method": "POST",
  "RequestUri": "https://httpbin.org/post",
  "Headers": {
    "Content-Type": "application/json"
  },
  "Content": {
    "action": "cleanup_db"
  }
}
2025-06-30T09:06:23.6141580+05:30 [Information] Set Value to Step 'c2ad3631-c53c-408d-889d-344b5ad2bc5c' output:
{
  "statusCode": 200,
  "reasonPhrase": "OK",
  "headers": [
    {
      "Key": "Date",
      "Value": [
        "Mon, 30 Jun 2025 03:36:28 GMT"
      ]
    },
    {
      "Key": "Connection",
      "Value": [
        "keep-alive"
      ]
    },
    {
      "Key": "Server",
      "Value": [
        "gunicorn/19.9.0"
      ]
    },
    {
      "Key": "Access-Control-Allow-Origin",
      "Value": [
        "*"
      ]
    },
    {
      "Key": "Access-Control-Allow-Credentials",
      "Value": [
        "true"
      ]
    }
  ],
  "content": {
    "args": {},
    "data": "{\r\n  \"action\": \"cleanup_db\"\r\n}",
    "files": {},
    "form": {},
    "headers": {
      "Content-Length": "30",
      "Content-Type": "application/json; charset=utf-8",
      "Host": "httpbin.org",
      "X-Amzn-Trace-Id": "Root=1-6862063c-299f872d0087cfa865c12169"
    },
    "json": {
      "action": "cleanup_db"
    },
    "origin": "**************",
    "url": "https://httpbin.org/post"
  }
}
2025-06-30T09:06:23.6145386+05:30 [Information] Set Value to Step 'c2ad3631-c53c-408d-889d-344b5ad2bc5c' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T03:36:22.7999925Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:06:23.6147595+05:30 [Information] Set Value to Step 'c2ad3631-c53c-408d-889d-344b5ad2bc5c' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 813,
  "startTime": "2025-06-30T03:36:22.7999925Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:06:23.6149473+05:30 [Information] Step 'Clean up Database' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "c2ad3631-c53c-408d-889d-344b5ad2bc5c",
        "tables": {},
        "variables": {},
        "name": "Clean up Database",
        "description": "Tear down the database state",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "action": "cleanup_db"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 03:36:28 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"action\": \"cleanup_db\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "30",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-6862063c-299f872d0087cfa865c12169"
            },
            "json": {
              "action": "cleanup_db"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 813,
          "startTime": "2025-06-30T03:36:22.7999925Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:06:23.6150465+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:06:23.6151401+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:06:23.6152353+05:30 [Information] No performance data found for step 'Clean up Database' (http)
2025-06-30T09:06:23.6153032+05:30 [Information] [ OK       ]
2025-06-30T09:06:23.6156130+05:30 [Information] [----------] 1 step from Teardown (816 ms total)
