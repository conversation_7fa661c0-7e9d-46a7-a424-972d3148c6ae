/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using Newtonsoft.Json.Linq;
using NUnit.Framework;
using TestLib.Expressions;

namespace TestLib.Tests;

[TestFixture]
public class NotEqualExpressionTests
{
    private TestContext _testContext;

    [SetUp]
    public void Setup()
    {
        _testContext = new TestContext();
        _testContext.Variables.Upsert("intValue", "42");
        _testContext.Variables.Upsert("floatValue", "3.14");
        _testContext.Variables.Upsert("boolValue", "true");
        _testContext.Variables.Upsert("jsonValue", "{\"intValue\":1, \"boolValue\" : true}");
        _testContext.Variables.Upsert("invalidJson", "not-a-json");
    }

    [Test]
    public void Evaluate_DifferentValues_ReturnsTrue()
    {
        var expression = new NotEqualExpression(new ConstantExpression(42), new ConstantExpression(24));
        var result = expression.Evaluate(_testContext);

        Assert.IsTrue((bool)result);
    }

    [Test]
    public void Evaluate_SameValues_ReturnsFalse()
    {
        var expression = new NotEqualExpression(new ConstantExpression(42), new ConstantExpression(42));
        var result = expression.Evaluate(_testContext);

        Assert.IsFalse((bool)result);
    }

    [Test]
    public void Evaluate_NullValues_ReturnsFalse()
    {
        var expression = new NotEqualExpression(new ConstantExpression(null), new ConstantExpression(null));
        var result = expression.Evaluate(_testContext);

        Assert.IsFalse((bool)result);
    }

    [Test]
    public void Evaluate_NullAndNonNull_ReturnsTrue()
    {
        var expression = new NotEqualExpression(new ConstantExpression(null), new ConstantExpression(42));
        var result = expression.Evaluate(_testContext);

        Assert.IsTrue((bool)result);
    }

    [Test]
    public void Evaluate_ConvertedIntInequality_ReturnsTrue()
    {
        var expression = new NotEqualExpression(new ConstantExpression("42"), new ConstantExpression(24));
        var result = expression.Evaluate(_testContext);

        Assert.IsTrue((bool)result);
    }

    [Test]
    public void Evaluate_ConvertedIntEquality_ReturnsFalse()
    {
        var expression = new NotEqualExpression(new ConstantExpression("42"), new ConstantExpression(42));
        var result = expression.Evaluate(_testContext);

        Assert.IsFalse((bool)result);
    }

    [Test]
    public void Evaluate_ConvertedFloatInequality_ReturnsTrue()
    {
        var expression = new NotEqualExpression(new ConstantExpression("3.14"), new ConstantExpression(2.71f));
        var result = expression.Evaluate(_testContext);

        Assert.IsTrue((bool)result);
    }

    [Test]
    public void Evaluate_ConvertedBoolInequality_ReturnsTrue()
    {
        var expression = new NotEqualExpression(new ConstantExpression("true"), new ConstantExpression(false));
        var result = expression.Evaluate(_testContext);

        Assert.IsTrue((bool)result);
    }

    [Test]
    public void Evaluate_RawStringInequality_ReturnsTrue()
    {
        var expression = new NotEqualExpression(new ConstantExpression("Hello"), new ConstantExpression("World"));
        var result = expression.Evaluate(_testContext);

        Assert.IsTrue((bool)result);
    }

    [Test]
    public void Evaluate_RawStringEquality_ReturnsFalse()
    {
        var expression = new NotEqualExpression(new ConstantExpression("Hello"), new ConstantExpression("Hello"));
        var result = expression.Evaluate(_testContext);

        Assert.IsFalse((bool)result);
    }

    [Test]
    public void Evaluate_JsonObjectInequality_ReturnsTrue()
    {
        var json1 = JObject.Parse("{\"key1\": \"value1\"}");
        var json2 = JObject.Parse("{\"key2\": \"value2\"}");

        var expression = new NotEqualExpression(new ConstantExpression(json1), new ConstantExpression(json2));
        var result = expression.Evaluate(_testContext);

        Assert.IsTrue((bool)result);
    }

    [Test]
    public void Evaluate_JsonObjectEquality_ReturnsFalse()
    {
        var json1 = JObject.Parse("{\"key1\": \"value1\"}");
        var json2 = JObject.Parse("{\"key1\": \"value1\"}");

        var expression = new NotEqualExpression(new ConstantExpression(json1), new ConstantExpression(json2));
        var result = expression.Evaluate(_testContext);

        Assert.IsFalse((bool)result);
    }

    [Test]
    public void Evaluate_IntFromJsonPathToConstant_ReturnsTrue()
    {
        var expression = new NotEqualExpression(new ConstantExpression(24), new JsonPathExpression("$var:intValue"));
        var result = expression.Evaluate(_testContext);

        Assert.IsTrue((bool)result);
    }

    [Test]
    public void Evaluate_BoolFromJsonPathToConstant_ReturnsTrue()
    {
        var expression = new NotEqualExpression(new ConstantExpression(false), new JsonPathExpression("$var:jsonValue.boolValue"));
        var result = expression.Evaluate(_testContext);

        Assert.IsTrue((bool)result);
    }

    [Test]
    public void GetErrorMessage_ReturnsExpectedFormat()
    {
        var expression = new NotEqualExpression(new ConstantExpression("Hello"), new ConstantExpression("Hello"));
        expression.Evaluate(_testContext);

        var errorMessage = expression.GetErrorMessage();

        StringAssert.Contains("Not-equal assertion failed", errorMessage);
        StringAssert.Contains("Expression1 should not be equal to Expression2", errorMessage);
    }

    [Test]
    public void GetErrorMessage_WithCustomMessage_ReturnsCustomMessage()
    {
        var customMessage = "Custom not equal error message";
        var expression = new NotEqualExpression(new ConstantExpression("Hello"), new ConstantExpression("Hello"), customMessage);
        expression.Evaluate(_testContext);

        var errorMessage = expression.GetErrorMessage();

        Assert.AreEqual(customMessage, errorMessage);
    }

    [Test]
    public void ToString_ReturnsExpectedFormat()
    {
        var expression = new NotEqualExpression(new ConstantExpression("Hello"), new ConstantExpression("World"));
        expression.Evaluate(_testContext);

        var toStringResult = expression.ToString();

        StringAssert.Contains("NotEqual", toStringResult);
        StringAssert.Contains("Hello", toStringResult);
        StringAssert.Contains("World", toStringResult);
    }
}
