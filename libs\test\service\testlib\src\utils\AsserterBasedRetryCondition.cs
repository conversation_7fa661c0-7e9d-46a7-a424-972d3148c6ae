/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using TestLib.Assertions;

namespace TestLib;
public class AsserterBasedRetryCondition(List<Asserter> asserters) : IRetryCondition
{
    public bool IsConditionMet(JToken response)
    {
        if (asserters == null || asserters.Count == 0)
        {
            // No asserters, so no condition to check
            return true;
        }

        // Create a temporary TestContext and ExecResult
        var tempContext = new TestContext();
        var tempResult = new TestStep.ExecutionResult(Guid.NewGuid().ToString());

        // Insert the response into the tempContext as needed so asserters can find it
        tempContext.AddStepOutput(tempResult.Id, response);

        // Run all asserters
        foreach (var asserter in asserters)
        {
            try
            {
                asserter.Test(tempContext, tempResult);
            }
            catch
            {
                // If asserter throws, condition not met
                return false;
            }
            if (tempResult.AssertionFailures.Count > 0)
            {
                // Asser<PERSON> failed
                return false;
            }
        }

        // If we reach here, all asserters passed
        return true;
    }
}
