2025-06-30T09:33:56.6358366+05:30 [Information] Global variables set to: baseUrl: https://httpbin.org, serverIp: 127.0.0.1, serverPort: 60606
2025-06-30T09:33:56.6516016+05:30 [Information] Loading tests from D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc with pattern *math-expressions-simple.yaml
2025-06-30T09:33:56.6557001+05:30 [Information] Loading test from D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc\math-expressions-simple.yaml
2025-06-30T09:33:56.6680233+05:30 [Information] Global variables set to: 
2025-06-30T09:33:56.6690732+05:30 [Information] [==========] Running 1 test from Yaml files
2025-06-30T09:33:56.6705403+05:30 [Information] [-------------------------------------    Startup     -------------------------------------]
2025-06-30T09:33:56.6910337+05:30 [Information] [----------] 1 step from Startup
2025-06-30T09:33:56.6938234+05:30 [Information] [ RUN      ] Startup > Initialize Database
2025-06-30T09:33:58.9873095+05:30 [Information] [ OK       ]
2025-06-30T09:33:58.9884545+05:30 [Information] [----------] 1 step from Startup (2295 ms total)
2025-06-30T09:33:58.9908079+05:30 [Information] Global variables set to: curDir: D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc, baseUrl: https://httpbin.org, serverIp: 127.0.0.1, serverPort: 60606, dbInitStatus: initialize_db
2025-06-30T09:33:58.9913964+05:30 [Information] [------------------------------------- Test Execution -------------------------------------]
2025-06-30T09:33:58.9927952+05:30 [Information] [----------] 6 steps from Simple Mathematical Expressions Demo - Passing Tests
2025-06-30T09:33:58.9942526+05:30 [Information] [ RUN      ] Simple Mathematical Expressions Demo - Passing Tests > Fast Baseline Operation
2025-06-30T09:33:59.0847914+05:30 [Information] [ OK       ]
2025-06-30T09:33:59.0852006+05:30 [Information] [ RUN      ] Simple Mathematical Expressions Demo - Passing Tests > Regression Threshold Test
2025-06-30T09:33:59.3192677+05:30 [Information] [ OK       ]
2025-06-30T09:33:59.3196824+05:30 [Information] [ RUN      ] Simple Mathematical Expressions Demo - Passing Tests > Complex Expression Test
