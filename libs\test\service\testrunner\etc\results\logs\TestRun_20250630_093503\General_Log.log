2025-06-30T09:35:03.1724412+05:30 [Information] Loading tests from D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc\examples with pattern *performance-testing-examples.yaml
2025-06-30T09:35:03.1852946+05:30 [Information] Loading test from D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc\examples\performance-testing-examples.yaml
2025-06-30T09:35:03.2689549+05:30 [Information] Global variables set to: 
2025-06-30T09:35:03.2700570+05:30 [Information] [==========] Running 1 test from Yaml files
2025-06-30T09:35:03.2712569+05:30 [Information] [------------------------------------- Test Execution -------------------------------------]
2025-06-30T09:35:03.2918874+05:30 [Information] [----------] 19 steps from Performance Testing Examples
2025-06-30T09:35:03.2949411+05:30 [Information] [ RUN      ] Performance Testing Examples > HTTP Response Time Test
2025-06-30T09:35:04.6499637+05:30 [Information] [ OK       ]
2025-06-30T09:35:04.6506226+05:30 [Information] [ RUN      ] Performance Testing Examples > Performance Baseline Establishment
2025-06-30T09:35:05.6600274+05:30 [Information] [ OK       ]
2025-06-30T09:35:05.6607281+05:30 [Information] [ RUN      ] Performance Testing Examples > Performance Regression Check
2025-06-30T09:35:06.7221928+05:30 [Information] [ OK       ]
2025-06-30T09:35:06.7233930+05:30 [Information] [ RUN      ] Performance Testing Examples > Load Testing with Controlled Request Pacing
2025-06-30T09:35:07.7585685+05:30 [Information] [ OK       ]
2025-06-30T09:35:07.7589563+05:30 [Information] [ RUN      ] Performance Testing Examples > Fast Endpoint Baseline
2025-06-30T09:35:09.2253304+05:30 [Information] [ OK       ]
2025-06-30T09:35:09.2262611+05:30 [Information] [ RUN      ] Performance Testing Examples > Complex Endpoint Comparison
2025-06-30T09:35:11.2144951+05:30 [Information] [ OK       ]
2025-06-30T09:35:11.2151090+05:30 [Information] [ RUN      ] Performance Testing Examples > Service Warm-up Performance Test
2025-06-30T09:35:11.9893867+05:30 [Information] [ OK       ]
2025-06-30T09:35:11.9902541+05:30 [Information] [ RUN      ] Performance Testing Examples > Performance Stability Test
2025-06-30T09:35:13.2828762+05:30 [Information] [ OK       ]
2025-06-30T09:35:13.2839548+05:30 [Information] [ RUN      ] Performance Testing Examples > Performance Recovery Test
2025-06-30T09:35:17.2742138+05:30 [Information] [ OK       ]
2025-06-30T09:35:17.2747743+05:30 [Information] [ RUN      ] Performance Testing Examples > Database Connection Simulation Performance Test
2025-06-30T09:35:19.2086235+05:30 [Information] [ OK       ]
2025-06-30T09:35:19.2090606+05:30 [Information] [ RUN      ] Performance Testing Examples > System Information Performance Test
2025-06-30T09:35:22.5358483+05:30 [Information] [ OK       ]
2025-06-30T09:35:22.5362637+05:30 [Information] [ RUN      ] Performance Testing Examples > Directory Listing Performance Test
2025-06-30T09:35:22.5579753+05:30 [Information] [ OK       ]
2025-06-30T09:35:22.5583995+05:30 [Information] [ RUN      ] Performance Testing Examples > Network Service Availability Performance Test
2025-06-30T09:35:24.1664710+05:30 [Information] [ OK       ]
2025-06-30T09:35:24.1670187+05:30 [Information] [ RUN      ] Performance Testing Examples > API Rate Limiting Compliance Test
2025-06-30T09:35:26.9653968+05:30 [Information] [ OK       ]
2025-06-30T09:35:26.9664963+05:30 [Information] [ RUN      ] Performance Testing Examples > Performance Baseline - Fast Operation
2025-06-30T09:35:26.9867864+05:30 [Information] [ OK       ]
2025-06-30T09:35:26.9874191+05:30 [Information] [ RUN      ] Performance Testing Examples > Performance Comparison - Slower Operation
2025-06-30T09:35:28.2076684+05:30 [Information] [ OK       ]
2025-06-30T09:35:28.2080468+05:30 [Information] [ RUN      ] Performance Testing Examples > Process Enumeration Performance Test
2025-06-30T09:35:28.5175549+05:30 [Information] [ OK       ]
2025-06-30T09:35:28.5207773+05:30 [Information] [ RUN      ] Performance Testing Examples > Mathematical Expressions Demo
2025-06-30T09:35:29.8365412+05:30 [Information] [ OK       ]
2025-06-30T09:35:29.8371958+05:30 [Information] [ RUN      ] Performance Testing Examples > Performance Budget Calculation Demo
2025-06-30T09:35:30.7261128+05:30 [Error] Assert Failure : Less(0, 866.6667)
2025-06-30T09:35:30.7269294+05:30 [Error] Scaling performance failed: {{$curStep:performance.executionTimeMs}}ms vs {{$var:CriticalPathBudget / $var:ScalingFactor + $var:SafetyMargin}}ms
2025-06-30T09:35:30.7270642+05:30 [Error] [ FAILED   ]
2025-06-30T09:35:30.7280785+05:30 [Information] [----------] 19 steps from Performance Testing Examples (27435 ms total)
2025-06-30T09:35:30.7294772+05:30 [Information] [-------------------------------------  Test Results  -------------------------------------]
2025-06-30T09:35:30.7299872+05:30 [Information] [==========] 1 test from Yaml files ran. (27460 ms total)
2025-06-30T09:35:30.7312928+05:30 [Information] [  PASSED  ] 0 tests.
2025-06-30T09:35:30.7315444+05:30 [Error] [  FAILED  ] 1 test, listed below:
2025-06-30T09:35:30.7319529+05:30 [Error] [  FAILED  ] Performance Testing Examples
