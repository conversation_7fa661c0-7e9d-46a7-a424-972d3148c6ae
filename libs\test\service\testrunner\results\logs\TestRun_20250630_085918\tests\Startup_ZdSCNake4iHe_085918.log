2025-06-30T08:59:18.6032367+05:30 [Information] [----------] 1 step from Startup
2025-06-30T08:59:18.6055110+05:30 [Information] Set Value to Step 'c8e57ef9-7541-4fb2-a29d-735f2b7b81c7' name:
"Initialize Database"
2025-06-30T08:59:18.6057462+05:30 [Information] Set Value to Step 'c8e57ef9-7541-4fb2-a29d-735f2b7b81c7' description:
"Setup initial database state"
2025-06-30T08:59:18.6059657+05:30 [Information] [ RUN      ] Startup > Initialize Database
2025-06-30T08:59:18.6073668+05:30 [Information] Executing single instance of step 'Initialize Database'.
2025-06-30T08:59:18.6085695+05:30 [Information] Executing step 'Initialize Database' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "c8e57ef9-7541-4fb2-a29d-735f2b7b81c7",
        "tables": {},
        "variables": {},
        "name": "Initialize Database",
        "description": "Setup initial database state"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T08:59:18.6106390+05:30 [Information] Processing $var: path 'baseUrl'
2025-06-30T08:59:18.6121483+05:30 [Information] Checking if 'baseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T08:59:18.6136760+05:30 [Information] Resolving path 'baseUrl' using global variables.
2025-06-30T08:59:18.6138923+05:30 [Information] Resolved JPath '$var:baseUrl' to 'https://httpbin.org'
2025-06-30T08:59:18.6139850+05:30 [Information] Resolved template pattern '{{$var:baseUrl}}' to 'https://httpbin.org' in '{{$var:baseUrl}}/post'
2025-06-30T08:59:18.6141889+05:30 [Information] Resolved template '{{$var:baseUrl}}/post' to 'https://httpbin.org/post'
2025-06-30T08:59:18.6163587+05:30 [Information] Set Value to Step 'c8e57ef9-7541-4fb2-a29d-735f2b7b81c7' input:
{
  "Method": "POST",
  "RequestUri": "https://httpbin.org/post",
  "Headers": {
    "Content-Type": "application/json"
  },
  "Content": {
    "action": "initialize_db"
  }
}
2025-06-30T08:59:19.8148782+05:30 [Information] Set Value to Step 'c8e57ef9-7541-4fb2-a29d-735f2b7b81c7' output:
{
  "statusCode": 200,
  "reasonPhrase": "OK",
  "headers": [
    {
      "Key": "Date",
      "Value": [
        "Mon, 30 Jun 2025 03:29:25 GMT"
      ]
    },
    {
      "Key": "Connection",
      "Value": [
        "keep-alive"
      ]
    },
    {
      "Key": "Server",
      "Value": [
        "gunicorn/19.9.0"
      ]
    },
    {
      "Key": "Access-Control-Allow-Origin",
      "Value": [
        "*"
      ]
    },
    {
      "Key": "Access-Control-Allow-Credentials",
      "Value": [
        "true"
      ]
    }
  ],
  "content": {
    "args": {},
    "data": "{\r\n  \"action\": \"initialize_db\"\r\n}",
    "files": {},
    "form": {},
    "headers": {
      "Content-Length": "33",
      "Content-Type": "application/json; charset=utf-8",
      "Host": "httpbin.org",
      "X-Amzn-Trace-Id": "Root=1-68620495-4da9197e0e5a273c23e9cf1f"
    },
    "json": {
      "action": "initialize_db"
    },
    "origin": "**************",
    "url": "https://httpbin.org/post"
  }
}
2025-06-30T08:59:19.8154348+05:30 [Information] Resolving path 'output.content.json.action' using step input/output.
2025-06-30T08:59:19.8156267+05:30 [Information] Resolved JPath 'output.content.json.action' to 'initialize_db'
2025-06-30T08:59:19.8166561+05:30 [Information] Set Value to Step 'c8e57ef9-7541-4fb2-a29d-735f2b7b81c7' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T03:29:18.6073316Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T08:59:19.8195307+05:30 [Information] Run Asserter : {"Expression1":{"JPath":"$curStep:output.content.json.action","ErrorMesage":null},"Expression2":{"Value":null,"ErrorMesage":null},"ErrorMesage":null}
2025-06-30T08:59:19.8199481+05:30 [Information] Resolving path 'output.content.json.action' using $curStep:
2025-06-30T08:59:19.8200923+05:30 [Information] Resolved JPath '$curStep:output.content.json.action' to 'initialize_db'
2025-06-30T08:59:19.8214976+05:30 [Information] Error in EqualExpression: The method or operation is not implemented.
2025-06-30T08:59:19.8224885+05:30 [Information] Set Value to Step 'c8e57ef9-7541-4fb2-a29d-735f2b7b81c7' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 1205,
  "startTime": "2025-06-30T03:29:18.6073316Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T08:59:19.8227011+05:30 [Information] Step 'Initialize Database' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "c8e57ef9-7541-4fb2-a29d-735f2b7b81c7",
        "tables": {},
        "variables": {},
        "name": "Initialize Database",
        "description": "Setup initial database state",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "action": "initialize_db"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 03:29:25 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"action\": \"initialize_db\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "33",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620495-4da9197e0e5a273c23e9cf1f"
            },
            "json": {
              "action": "initialize_db"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 1205,
          "startTime": "2025-06-30T03:29:18.6073316Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T08:59:19.8228914+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T08:59:19.8230549+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T08:59:19.8231276+05:30 [Information] No performance data found for step 'Initialize Database' (http)
2025-06-30T08:59:19.8234753+05:30 [Information] [ OK       ]
2025-06-30T08:59:19.8244715+05:30 [Information] [----------] 1 step from Startup (1219 ms total)
