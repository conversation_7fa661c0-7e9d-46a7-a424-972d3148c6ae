2025-06-30T09:00:15.7781233+05:30 [Information] [----------] 1 step from Startup
2025-06-30T09:00:15.7806584+05:30 [Information] Set Value to Step 'b2cfebe9-8d4b-46fe-8e9e-0f2393cafe04' name:
"Initialize Database"
2025-06-30T09:00:15.7809149+05:30 [Information] Set Value to Step 'b2cfebe9-8d4b-46fe-8e9e-0f2393cafe04' description:
"Setup initial database state"
2025-06-30T09:00:15.7811490+05:30 [Information] [ RUN      ] Startup > Initialize Database
2025-06-30T09:00:15.7821982+05:30 [Information] Executing single instance of step 'Initialize Database'.
2025-06-30T09:00:15.7832991+05:30 [Information] Executing step 'Initialize Database' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "b2cfebe9-8d4b-46fe-8e9e-0f2393cafe04",
        "tables": {},
        "variables": {},
        "name": "Initialize Database",
        "description": "Setup initial database state"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:00:15.7849370+05:30 [Information] Processing $var: path 'baseUrl'
2025-06-30T09:00:15.7863409+05:30 [Information] Checking if 'baseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:00:15.7878653+05:30 [Information] Resolving path 'baseUrl' using global variables.
2025-06-30T09:00:15.7880854+05:30 [Information] Resolved JPath '$var:baseUrl' to 'https://httpbin.org'
2025-06-30T09:00:15.7881835+05:30 [Information] Resolved template pattern '{{$var:baseUrl}}' to 'https://httpbin.org' in '{{$var:baseUrl}}/post'
2025-06-30T09:00:15.7884396+05:30 [Information] Resolved template '{{$var:baseUrl}}/post' to 'https://httpbin.org/post'
2025-06-30T09:00:15.7905639+05:30 [Information] Set Value to Step 'b2cfebe9-8d4b-46fe-8e9e-0f2393cafe04' input:
{
  "Method": "POST",
  "RequestUri": "https://httpbin.org/post",
  "Headers": {
    "Content-Type": "application/json"
  },
  "Content": {
    "action": "initialize_db"
  }
}
2025-06-30T09:00:16.9043644+05:30 [Information] Set Value to Step 'b2cfebe9-8d4b-46fe-8e9e-0f2393cafe04' output:
{
  "statusCode": 200,
  "reasonPhrase": "OK",
  "headers": [
    {
      "Key": "Date",
      "Value": [
        "Mon, 30 Jun 2025 03:30:22 GMT"
      ]
    },
    {
      "Key": "Connection",
      "Value": [
        "keep-alive"
      ]
    },
    {
      "Key": "Server",
      "Value": [
        "gunicorn/19.9.0"
      ]
    },
    {
      "Key": "Access-Control-Allow-Origin",
      "Value": [
        "*"
      ]
    },
    {
      "Key": "Access-Control-Allow-Credentials",
      "Value": [
        "true"
      ]
    }
  ],
  "content": {
    "args": {},
    "data": "{\r\n  \"action\": \"initialize_db\"\r\n}",
    "files": {},
    "form": {},
    "headers": {
      "Content-Length": "33",
      "Content-Type": "application/json; charset=utf-8",
      "Host": "httpbin.org",
      "X-Amzn-Trace-Id": "Root=1-686204ce-5fbae10127fb874a183b9596"
    },
    "json": {
      "action": "initialize_db"
    },
    "origin": "**************",
    "url": "https://httpbin.org/post"
  }
}
2025-06-30T09:00:16.9048748+05:30 [Information] Resolving path 'output.content.json.action' using step input/output.
2025-06-30T09:00:16.9050418+05:30 [Information] Resolved JPath 'output.content.json.action' to 'initialize_db'
2025-06-30T09:00:16.9061703+05:30 [Information] Set Value to Step 'b2cfebe9-8d4b-46fe-8e9e-0f2393cafe04' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T03:30:15.7821487Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:00:16.9085435+05:30 [Information] Run Asserter : {"Expression1":{"JPath":"$curStep:output.content.json.action","ErrorMesage":null},"Expression2":{"Value":null,"ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:00:16.9089088+05:30 [Information] Resolving path 'output.content.json.action' using $curStep:
2025-06-30T09:00:16.9090451+05:30 [Information] Resolved JPath '$curStep:output.content.json.action' to 'initialize_db'
2025-06-30T09:00:16.9104817+05:30 [Information] Error in EqualExpression: The method or operation is not implemented.
2025-06-30T09:00:16.9114624+05:30 [Information] Set Value to Step 'b2cfebe9-8d4b-46fe-8e9e-0f2393cafe04' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 1119,
  "startTime": "2025-06-30T03:30:15.7821487Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:00:16.9116739+05:30 [Information] Step 'Initialize Database' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "b2cfebe9-8d4b-46fe-8e9e-0f2393cafe04",
        "tables": {},
        "variables": {},
        "name": "Initialize Database",
        "description": "Setup initial database state",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "action": "initialize_db"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 03:30:22 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"action\": \"initialize_db\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "33",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-686204ce-5fbae10127fb874a183b9596"
            },
            "json": {
              "action": "initialize_db"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 1119,
          "startTime": "2025-06-30T03:30:15.7821487Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:00:16.9118814+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:00:16.9119997+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:00:16.9120634+05:30 [Information] No performance data found for step 'Initialize Database' (http)
2025-06-30T09:00:16.9123994+05:30 [Information] [ OK       ]
2025-06-30T09:00:16.9133373+05:30 [Information] [----------] 1 step from Startup (1133 ms total)
