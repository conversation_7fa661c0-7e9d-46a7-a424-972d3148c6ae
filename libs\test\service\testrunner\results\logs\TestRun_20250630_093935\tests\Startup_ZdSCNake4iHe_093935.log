2025-06-30T09:39:35.3854343+05:30 [Information] [----------] 1 step from Startup
2025-06-30T09:39:35.3881189+05:30 [Information] Set Value to Step 'cf47f5fd-9cb4-494d-879b-f9443fbe2f48' name:
"Initialize Database"
2025-06-30T09:39:35.3884128+05:30 [Information] Set Value to Step 'cf47f5fd-9cb4-494d-879b-f9443fbe2f48' description:
"Setup initial database state"
2025-06-30T09:39:35.3886701+05:30 [Information] [ RUN      ] Startup > Initialize Database
2025-06-30T09:39:35.3899305+05:30 [Information] Executing single instance of step 'Initialize Database'.
2025-06-30T09:39:35.3910988+05:30 [Information] Executing step 'Initialize Database' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "cf47f5fd-9cb4-494d-879b-f9443fbe2f48",
        "tables": {},
        "variables": {},
        "name": "Initialize Database",
        "description": "Setup initial database state"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:39:35.3930620+05:30 [Information] Processing $var: path 'baseUrl'
2025-06-30T09:39:35.3946987+05:30 [Information] Checking if 'baseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:39:35.3963564+05:30 [Information] Resolving path 'baseUrl' using global variables.
2025-06-30T09:39:35.3966089+05:30 [Information] Resolved JPath '$var:baseUrl' to 'https://httpbin.org'
2025-06-30T09:39:35.3967111+05:30 [Information] Resolved template pattern '{{$var:baseUrl}}' to 'https://httpbin.org' in '{{$var:baseUrl}}/post'
2025-06-30T09:39:35.3969970+05:30 [Information] Resolved template '{{$var:baseUrl}}/post' to 'https://httpbin.org/post'
2025-06-30T09:39:35.3993082+05:30 [Information] Set Value to Step 'cf47f5fd-9cb4-494d-879b-f9443fbe2f48' input:
{
  "Method": "POST",
  "RequestUri": "https://httpbin.org/post",
  "Headers": {
    "Content-Type": "application/json"
  },
  "Content": {
    "action": "initialize_db"
  }
}
2025-06-30T09:39:37.9290913+05:30 [Information] Set Value to Step 'cf47f5fd-9cb4-494d-879b-f9443fbe2f48' output:
{
  "statusCode": 200,
  "reasonPhrase": "OK",
  "headers": [
    {
      "Key": "Date",
      "Value": [
        "Mon, 30 Jun 2025 04:09:43 GMT"
      ]
    },
    {
      "Key": "Connection",
      "Value": [
        "keep-alive"
      ]
    },
    {
      "Key": "Server",
      "Value": [
        "gunicorn/19.9.0"
      ]
    },
    {
      "Key": "Access-Control-Allow-Origin",
      "Value": [
        "*"
      ]
    },
    {
      "Key": "Access-Control-Allow-Credentials",
      "Value": [
        "true"
      ]
    }
  ],
  "content": {
    "args": {},
    "data": "{\r\n  \"action\": \"initialize_db\"\r\n}",
    "files": {},
    "form": {},
    "headers": {
      "Content-Length": "33",
      "Content-Type": "application/json; charset=utf-8",
      "Host": "httpbin.org",
      "X-Amzn-Trace-Id": "Root=1-68620e06-6c5f8fa57f91084e214cca0e"
    },
    "json": {
      "action": "initialize_db"
    },
    "origin": "**************",
    "url": "https://httpbin.org/post"
  }
}
2025-06-30T09:39:37.9296431+05:30 [Information] Resolving path 'output.content.json.action' using step input/output.
2025-06-30T09:39:37.9298371+05:30 [Information] Resolved JPath 'output.content.json.action' to 'initialize_db'
2025-06-30T09:39:37.9308015+05:30 [Information] Set Value to Step 'cf47f5fd-9cb4-494d-879b-f9443fbe2f48' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:09:35.3899016Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:39:37.9334063+05:30 [Information] Run Asserter : {"Expression1":{"JPath":"$curStep:output.content.json.action","ErrorMesage":null},"Expression2":{"Value":null,"ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:39:37.9337527+05:30 [Information] Resolving path 'output.content.json.action' using $curStep:
2025-06-30T09:39:37.9338816+05:30 [Information] Resolved JPath '$curStep:output.content.json.action' to 'initialize_db'
2025-06-30T09:39:37.9354150+05:30 [Information] Error in EqualExpression: The method or operation is not implemented.
2025-06-30T09:39:37.9363237+05:30 [Information] Set Value to Step 'cf47f5fd-9cb4-494d-879b-f9443fbe2f48' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 2536,
  "startTime": "2025-06-30T04:09:35.3899016Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:39:37.9364962+05:30 [Information] Step 'Initialize Database' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "cf47f5fd-9cb4-494d-879b-f9443fbe2f48",
        "tables": {},
        "variables": {},
        "name": "Initialize Database",
        "description": "Setup initial database state",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "action": "initialize_db"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:09:43 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"action\": \"initialize_db\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "33",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620e06-6c5f8fa57f91084e214cca0e"
            },
            "json": {
              "action": "initialize_db"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 2536,
          "startTime": "2025-06-30T04:09:35.3899016Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:39:37.9366660+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:39:37.9367651+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:39:37.9368241+05:30 [Information] No performance data found for step 'Initialize Database' (http)
2025-06-30T09:39:37.9370995+05:30 [Information] [ OK       ]
2025-06-30T09:39:37.9382316+05:30 [Information] [----------] 1 step from Startup (2551 ms total)
