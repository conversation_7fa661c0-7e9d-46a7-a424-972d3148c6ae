2025-06-30T09:37:43.9518340+05:30 [Information] [----------] 4 steps from ProtoReference test
2025-06-30T09:37:43.9525466+05:30 [Information] Set Value to Step '5b64d78b-9a26-4d79-acf9-39eec84fd985' name:
"Step 1: Connect to the MBusClient with admin credentials"
2025-06-30T09:37:43.9526707+05:30 [Information] Set Value to Step '5b64d78b-9a26-4d79-acf9-39eec84fd985' description:
"Connect to the MBusClient with admin credentials"
2025-06-30T09:37:43.9527460+05:30 [Information] [ RUN      ] ProtoReference test > Step 1: Connect to the MBusClient with admin credentials
2025-06-30T09:37:43.9531095+05:30 [Information] Executing single instance of step 'Step 1: Connect to the MBusClient with admin credentials'.
2025-06-30T09:37:43.9539170+05:30 [Information] Executing step 'Step 1: Connect to the MBusClient with admin credentials' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "5b64d78b-9a26-4d79-acf9-39eec84fd985",
        "tables": {},
        "variables": {},
        "name": "Step 1: Connect to the MBusClient with admin credentials",
        "description": "Connect to the MBusClient with admin credentials"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "userId": "surgeon2"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:37:43.9540384+05:30 [Information] Processing $var: path 'serverIp'
2025-06-30T09:37:43.9540968+05:30 [Information] Checking if 'serverIp' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:43.9541418+05:30 [Information] Resolving path 'serverIp' using global variables.
2025-06-30T09:37:43.9541961+05:30 [Information] Resolved JPath '$var:serverIp' to '127.0.0.1'
2025-06-30T09:37:43.9542350+05:30 [Information] Resolved template pattern '{{$var:serverIp}}' to '127.0.0.1' in '{{$var:serverIp}}'
2025-06-30T09:37:43.9542700+05:30 [Information] Resolved template '{{$var:serverIp}}' to '127.0.0.1'
2025-06-30T09:37:43.9543689+05:30 [Information] Processing $var: path 'serverPort'
2025-06-30T09:37:43.9544377+05:30 [Information] Checking if 'serverPort' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:43.9544856+05:30 [Information] Resolving path 'serverPort' using global variables.
2025-06-30T09:37:43.9545385+05:30 [Information] Resolved JPath '$var:serverPort' to '60606'
2025-06-30T09:37:43.9545780+05:30 [Information] Resolved template pattern '{{$var:serverPort}}' to '60606' in '{{$var:serverPort}}'
2025-06-30T09:37:43.9546131+05:30 [Information] Resolved template '{{$var:serverPort}}' to '60606'
2025-06-30T09:37:43.9546794+05:30 [Information] Set Value to Step '5b64d78b-9a26-4d79-acf9-39eec84fd985' input:
{
  "ServerIp": "127.0.0.1",
  "ServerPort": 60606,
  "Username": "admin",
  "Password": "admin"
}
2025-06-30T09:37:45.9692828+05:30 [Information] Failed to execute step 'Step 1: Connect to the MBusClient with admin credentials': Failed to execute step : Cannot connect to 127.0.0.1:60606.
2025-06-30T09:37:45.9705823+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:37:45.9712261+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:37:45.9716034+05:30 [Information] No performance data found for step 'Step 1: Connect to the MBusClient with admin credentials' (mbus)
2025-06-30T09:37:45.9738927+05:30 [Error] Failed to execute step : Cannot connect to 127.0.0.1:60606.
2025-06-30T09:37:45.9741077+05:30 [Error] [ FAILED   ]
2025-06-30T09:37:45.9743599+05:30 [Information] [----------] 4 steps from ProtoReference test (2021 ms total)
