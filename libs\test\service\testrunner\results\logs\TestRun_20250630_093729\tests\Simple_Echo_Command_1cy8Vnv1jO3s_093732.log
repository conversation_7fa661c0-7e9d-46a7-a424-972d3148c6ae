2025-06-30T09:37:32.6917027+05:30 [Information] [----------] 1 step from Simple Echo Command
2025-06-30T09:37:32.6922816+05:30 [Information] Set Value to Step '171942f8-d6c3-45ff-8347-d2997c289169' name:
"Step echo"
2025-06-30T09:37:32.6923799+05:30 [Information] Set Value to Step '171942f8-d6c3-45ff-8347-d2997c289169' description:
"Echo Hello, World!"
2025-06-30T09:37:32.6924445+05:30 [Information] [ RUN      ] Simple Echo Command > Step echo
2025-06-30T09:37:32.6927442+05:30 [Information] Executing single instance of step 'Step echo'.
2025-06-30T09:37:32.6928779+05:30 [Information] Executing step 'Step echo' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "171942f8-d6c3-45ff-8347-d2997c289169",
        "tables": {},
        "variables": {},
        "name": "Step echo",
        "description": "Echo Hello, World!"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:37:32.6930817+05:30 [Information] Set Value to Step '171942f8-d6c3-45ff-8347-d2997c289169' input:
{
  "command": "echo",
  "arguments": [
    "Hello, World!"
  ],
  "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
  "printOutput": false
}
2025-06-30T09:37:32.7109328+05:30 [Information] Set Value to Step '171942f8-d6c3-45ff-8347-d2997c289169' output:
{
  "stdout": "Hello, World!",
  "stderr": "",
  "exitCode": 0
}
2025-06-30T09:37:32.7110996+05:30 [Information] Set Value to Step '171942f8-d6c3-45ff-8347-d2997c289169' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:07:32.6927417Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "cmd"
}
2025-06-30T09:37:32.7111962+05:30 [Information] Run Asserter : {"Expression1":{"Value":"Hello, World!","ErrorMesage":null},"Expression2":{"JPath":"$curStep:$.output.stdout","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:37:32.7113393+05:30 [Information] Resolving path '$.output.stdout' using $curStep:
2025-06-30T09:37:32.7114141+05:30 [Information] Resolved JPath '$curStep:$.output.stdout' to 'Hello, World!'
2025-06-30T09:37:32.7114862+05:30 [Information] Set Value to Step '171942f8-d6c3-45ff-8347-d2997c289169' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 18,
  "startTime": "2025-06-30T04:07:32.6927417Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "cmd"
}
2025-06-30T09:37:32.7115683+05:30 [Information] Step 'Step echo' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "171942f8-d6c3-45ff-8347-d2997c289169",
        "tables": {},
        "variables": {},
        "name": "Step echo",
        "description": "Echo Hello, World!",
        "input": {
          "command": "echo",
          "arguments": [
            "Hello, World!"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Hello, World!",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 18,
          "startTime": "2025-06-30T04:07:32.6927417Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:37:32.7116430+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:37:32.7116986+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:37:32.7117487+05:30 [Information] No performance data found for step 'Step echo' (cmd)
2025-06-30T09:37:32.7117906+05:30 [Information] [ OK       ]
2025-06-30T09:37:32.7120520+05:30 [Information] [----------] 1 step from Simple Echo Command (19 ms total)
