<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2013/05/nuspec.xsd">
<metadata>
    <id>interop_api_csharp.win.x64.Debug</id>
    <version>1.0.0.3190</version>
    <title>interop_api_csharp.win.x64.Debug</title>
    <authors>Alcon Inc.</authors>
    <owners>alcon,interop</owners>
    <requireLicenseAcceptance>false</requireLicenseAcceptance>           
    <projectUrl>https://github.com/AlconRD/avs-interop.git</projectUrl>
    <description>C# Interop API library</description>
    <dependencies>
        <group targetFramework="net8.0">
                <dependency id="Google.Protobuf" version="3.21.12"/>
            </group>
    </dependencies>         
</metadata>   
<files>
 <file src="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\build\testlib\apicsharp\Debug\interop_api_csharp.*" target="lib\net8.0" />
 <file src="D:\kibisoft\workarea\git\interop\api\protobuf\alcon\**" target="lib\net8.0\protos\alcon" />
 
</files>
</package>