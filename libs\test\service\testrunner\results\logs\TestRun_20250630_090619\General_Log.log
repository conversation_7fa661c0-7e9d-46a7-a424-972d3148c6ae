2025-06-30T09:06:19.4459239+05:30 [Information] Global variables set to: baseUrl: https://httpbin.org, serverIp: 127.0.0.1, serverPort: 60606
2025-06-30T09:06:19.4612310+05:30 [Information] Loading tests from D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc with pattern *math-expressions-simple.yaml
2025-06-30T09:06:19.4652616+05:30 [Information] Loading test from D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc\math-expressions-simple.yaml
2025-06-30T09:06:19.4777112+05:30 [Information] Global variables set to: 
2025-06-30T09:06:19.4788566+05:30 [Information] [==========] Running 1 test from Yaml files
2025-06-30T09:06:19.4802809+05:30 [Information] [-------------------------------------    Startup     -------------------------------------]
2025-06-30T09:06:19.5010404+05:30 [Information] [----------] 1 step from Startup
2025-06-30T09:06:19.5041157+05:30 [Information] [ RUN      ] Startup > Initialize Database
2025-06-30T09:06:21.5342546+05:30 [Information] [ OK       ]
2025-06-30T09:06:21.5354426+05:30 [Information] [----------] 1 step from Startup (2032 ms total)
2025-06-30T09:06:21.5378512+05:30 [Information] Global variables set to: curDir: D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc, baseUrl: https://httpbin.org, serverIp: 127.0.0.1, serverPort: 60606, dbInitStatus: initialize_db
2025-06-30T09:06:21.5384129+05:30 [Information] [------------------------------------- Test Execution -------------------------------------]
2025-06-30T09:06:21.5396741+05:30 [Information] [----------] 6 steps from Simple Mathematical Expressions Demo
2025-06-30T09:06:21.5413284+05:30 [Information] [ RUN      ] Simple Mathematical Expressions Demo > Fast Baseline Operation
2025-06-30T09:06:21.6488983+05:30 [Information] [ OK       ]
2025-06-30T09:06:21.6493529+05:30 [Information] [ RUN      ] Simple Mathematical Expressions Demo > Regression Threshold Test
2025-06-30T09:06:22.7961517+05:30 [Error] Assert Failure : Less(150, 0)
2025-06-30T09:06:22.7968962+05:30 [Error] Performance regression detected: {{$curStep:performance.executionTimeMs}}ms vs allowed {{$var:MathBaseline * $var:MaxRegressionPercent / 100}}ms ({{$var:MathBaseline}}ms * {{$var:MaxRegressionPercent}}% / 100)
2025-06-30T09:06:22.7970227+05:30 [Error] [ FAILED   ]
2025-06-30T09:06:22.7974944+05:30 [Information] [----------] 6 steps from Simple Mathematical Expressions Demo (1257 ms total)
2025-06-30T09:06:22.7980162+05:30 [Information] [-------------------------------------    Teardown    -------------------------------------]
2025-06-30T09:06:22.7991163+05:30 [Information] [----------] 1 step from Teardown
2025-06-30T09:06:22.7997940+05:30 [Information] [ RUN      ] Teardown > Clean up Database
2025-06-30T09:06:23.6153528+05:30 [Information] [ OK       ]
2025-06-30T09:06:23.6157781+05:30 [Information] [----------] 1 step from Teardown (816 ms total)
2025-06-30T09:06:23.6162944+05:30 [Information] Global variables set to: curDir: D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc, baseUrl: https://httpbin.org, serverIp: 127.0.0.1, serverPort: 60606, dbInitStatus: initialize_db
2025-06-30T09:06:23.6163756+05:30 [Information] [-------------------------------------  Test Results  -------------------------------------]
2025-06-30T09:06:23.6169614+05:30 [Information] [==========] 1 test from Yaml files ran. (4138 ms total)
2025-06-30T09:06:23.6182061+05:30 [Information] [  PASSED  ] 0 tests.
2025-06-30T09:06:23.6184605+05:30 [Error] [  FAILED  ] 1 test, listed below:
2025-06-30T09:06:23.6188180+05:30 [Error] [  FAILED  ] Simple Mathematical Expressions Demo
