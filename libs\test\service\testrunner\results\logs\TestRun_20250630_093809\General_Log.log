2025-06-30T09:38:09.5142689+05:30 [Information] Global variables set to: baseUrl: https://httpbin.org, serverIp: 127.0.0.1, serverPort: 60606
2025-06-30T09:38:09.5312555+05:30 [Information] Loading tests from D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc with pattern *-test.yaml
2025-06-30T09:38:09.5351789+05:30 [Information] Loading test from D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc\cmd\chained-cmd-test.yaml
2025-06-30T09:38:09.5466149+05:30 [Information] Loading test from D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc\cmd\docker-list-test.yaml
2025-06-30T09:38:09.5473395+05:30 [Information] Loading test from D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc\cmd\eco-test.yaml
2025-06-30T09:38:09.5478896+05:30 [Information] Loading test from D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc\cmd\long-output-test.yaml
2025-06-30T09:38:09.5482929+05:30 [Information] Loading test from D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc\cmd\none-zero-exit-test.yaml
2025-06-30T09:38:09.5487172+05:30 [Information] Loading test from D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc\http\get-test.yaml
2025-06-30T09:38:09.5501939+05:30 [Information] Loading test from D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc\http\multistep-test.yaml
2025-06-30T09:38:09.5508999+05:30 [Information] Loading test from D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc\http\table-step-iteration-sample-test.yaml
2025-06-30T09:38:09.5518392+05:30 [Information] Loading test from D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc\http\table-test-iteration-sample-test.yaml
2025-06-30T09:38:09.5526517+05:30 [Information] Loading test from D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc\http\table-test-step-iteration-sample-test.yaml
2025-06-30T09:38:09.5539746+05:30 [Information] Loading test from D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc\http\with-include-test.yaml
2025-06-30T09:38:09.5554573+05:30 [Information] Loading test from D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc\rpc\protoFieldSetters-test.yaml
2025-06-30T09:38:10.0239845+05:30 [Information] Loading test from D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc\rpc\protoReference-test.yaml
2025-06-30T09:38:10.0272842+05:30 [Information] Global variables set to: 
2025-06-30T09:38:10.0286029+05:30 [Information] [==========] Running 13 tests from Yaml files
2025-06-30T09:38:10.0304713+05:30 [Information] [-------------------------------------    Startup     -------------------------------------]
2025-06-30T09:38:10.0521819+05:30 [Information] [----------] 1 step from Startup
2025-06-30T09:38:10.0553213+05:30 [Information] [ RUN      ] Startup > Initialize Database
2025-06-30T09:38:11.6729100+05:30 [Information] [ OK       ]
2025-06-30T09:38:11.6742010+05:30 [Information] [----------] 1 step from Startup (1620 ms total)
2025-06-30T09:38:11.6763328+05:30 [Information] Global variables set to: curDir: D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc, baseUrl: https://httpbin.org, serverIp: 127.0.0.1, serverPort: 60606, dbInitStatus: initialize_db
2025-06-30T09:38:11.6769012+05:30 [Information] [------------------------------------- Test Execution -------------------------------------]
2025-06-30T09:38:11.6780805+05:30 [Information] Processing $var: path 'ProductID'
2025-06-30T09:38:11.6782882+05:30 [Information] Checking if 'ProductID' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:11.6784071+05:30 [Information] Resolving path 'ProductID' using global variables.
2025-06-30T09:38:11.6784935+05:30 [Information] Resolved JPath '$var:ProductID' to '123'
2025-06-30T09:38:11.6786677+05:30 [Information] Resolved template pattern '{{$var:ProductID}}' to '123' in '[
  {
    "ProductID": "{{$var:ProductID}}",
    "Name": "Laptop",
    "Price": "1500",
    "Specifications": {
      "Processor": "Intel i7",
      "RAM": "16GB",
      "Storage": "512GB SSD"
    }
  },
  {
    "ProductID": "102",
    "Name": "Smartphone",
    "Price": "800",
    "Specifications": {
      "Processor": "Snapdragon 888",
      "RAM": "8GB",
      "Storage": "128GB"
    }
  }
]'
2025-06-30T09:38:11.6789692+05:30 [Information] Resolved template '[
  {
    "ProductID": "{{$var:ProductID}}",
    "Name": "Laptop",
    "Price": "1500",
    "Specifications": {
      "Processor": "Intel i7",
      "RAM": "16GB",
      "Storage": "512GB SSD"
    }
  },
  {
    "ProductID": "102",
    "Name": "Smartphone",
    "Price": "800",
    "Specifications": {
      "Processor": "Snapdragon 888",
      "RAM": "8GB",
      "Storage": "128GB"
    }
  }
]' to '[
  {
    "ProductID": "123",
    "Name": "Laptop",
    "Price": "1500",
    "Specifications": {
      "Processor": "Intel i7",
      "RAM": "16GB",
      "Storage": "512GB SSD"
    }
  },
  {
    "ProductID": "102",
    "Name": "Smartphone",
    "Price": "800",
    "Specifications": {
      "Processor": "Snapdragon 888",
      "RAM": "8GB",
      "Storage": "128GB"
    }
  }
]'
2025-06-30T09:38:11.6802758+05:30 [Information] [----------] 1 step from Simple Get Request
2025-06-30T09:38:11.6814724+05:30 [Information] [ RUN      ] Simple Get Request > Step get request
2025-06-30T09:38:12.4255529+05:30 [Information] [ OK       ]
2025-06-30T09:38:12.4259047+05:30 [Information] [----------] 1 step from Simple Get Request (744 ms total)
2025-06-30T09:38:12.4274509+05:30 [Information] [----------] 1 step from Chained Commands with Piping
2025-06-30T09:38:12.4288344+05:30 [Information] [ RUN      ] Chained Commands with Piping > Step pipe
2025-06-30T09:38:12.4712337+05:30 [Information] [ OK       ]
2025-06-30T09:38:12.4716323+05:30 [Information] [----------] 1 step from Chained Commands with Piping (43 ms total)
2025-06-30T09:38:12.4731077+05:30 [Information] [----------] 1 step from Docker PS Test
2025-06-30T09:38:12.4738509+05:30 [Information] [ RUN      ] Docker PS Test > Step list containers
2025-06-30T09:38:12.6544540+05:30 [Information] [ OK       ]
2025-06-30T09:38:12.6549515+05:30 [Information] [----------] 1 step from Docker PS Test (181 ms total)
2025-06-30T09:38:12.6564813+05:30 [Information] [----------] 1 step from Simple Echo Command
2025-06-30T09:38:12.6573427+05:30 [Information] [ RUN      ] Simple Echo Command > Step echo
2025-06-30T09:38:12.6835141+05:30 [Information] [ OK       ]
2025-06-30T09:38:12.6839885+05:30 [Information] [----------] 1 step from Simple Echo Command (26 ms total)
2025-06-30T09:38:12.6853012+05:30 [Information] [----------] 1 step from Long Output Command
2025-06-30T09:38:12.6860353+05:30 [Information] [ RUN      ] Long Output Command > Step long output
2025-06-30T09:38:12.7192802+05:30 [Information] [ OK       ]
2025-06-30T09:38:12.7196610+05:30 [Information] [----------] 1 step from Long Output Command (33 ms total)
2025-06-30T09:38:12.7210651+05:30 [Information] [----------] 1 step from Non-Zero Exit Code Test
2025-06-30T09:38:12.7218581+05:30 [Information] [ RUN      ] Non-Zero Exit Code Test > Step exit failure
2025-06-30T09:38:12.7573785+05:30 [Information] [ OK       ]
2025-06-30T09:38:12.7577689+05:30 [Information] [----------] 1 step from Non-Zero Exit Code Test (36 ms total)
2025-06-30T09:38:12.7593142+05:30 [Information] [----------] 2 steps from Testing MultiStep with JOSNPath
2025-06-30T09:38:12.7600003+05:30 [Information] [ RUN      ] Testing MultiStep with JOSNPath > GET Token
2025-06-30T09:38:13.5678562+05:30 [Information] [ OK       ]
2025-06-30T09:38:13.5687043+05:30 [Information] [ RUN      ] Testing MultiStep with JOSNPath > Step to test POST request with token
2025-06-30T09:38:14.3182515+05:30 [Information] [ OK       ]
2025-06-30T09:38:14.3191414+05:30 [Information] [----------] 2 steps from Testing MultiStep with JOSNPath (1559 ms total)
2025-06-30T09:38:14.3210745+05:30 [Information] [----------] 1 step from Demo HTTP Table Step Iteration
2025-06-30T09:38:14.3229760+05:30 [Information] [ RUN      ] Demo HTTP Table Step Iteration > Run for Each User
2025-06-30T09:38:14.3274726+05:30 [Information] Step Iteration 1/2: Fetching user Tom details
2025-06-30T09:38:15.1041024+05:30 [Information] Step Iteration 2/2: Fetching user John details
2025-06-30T09:38:16.1196252+05:30 [Information] [ OK       ]
2025-06-30T09:38:16.1201095+05:30 [Information] [----------] 1 step from Demo HTTP Table Step Iteration (1798 ms total)
2025-06-30T09:38:16.1213628+05:30 [Information] [----------] 1 step from Demo HTTP Table Test Iteration
2025-06-30T09:38:16.1272231+05:30 [Information] Test Iteration 1/2: Demo HTTP Table Test Iteration
2025-06-30T09:38:16.1278276+05:30 [Information] [ RUN      ] Demo HTTP Table Test Iteration > Fetching user Tom details
2025-06-30T09:38:16.9103302+05:30 [Information] [ OK       ]
2025-06-30T09:38:16.9120539+05:30 [Information] Test Iteration 2/2: Demo HTTP Table Test Iteration
2025-06-30T09:38:16.9125086+05:30 [Information] [ RUN      ] Demo HTTP Table Test Iteration > Fetching user John details
2025-06-30T09:38:17.4645280+05:30 [Information] [ OK       ]
2025-06-30T09:38:17.4647490+05:30 [Information] [----------] 1 step from Demo HTTP Table Test Iteration (1343 ms total)
2025-06-30T09:38:17.4659526+05:30 [Information] [----------] 2 steps from Itterative Test and Step
2025-06-30T09:38:17.4676485+05:30 [Information] Test Iteration 1/2: Running Test for Tom
2025-06-30T09:38:17.4683746+05:30 [Information] [ RUN      ] Itterative Test and Step > Step to get token for Tom
2025-06-30T09:38:18.6563717+05:30 [Information] [ OK       ]
2025-06-30T09:38:18.6572677+05:30 [Information] [ RUN      ] Itterative Test and Step > Itterative Step for user Tom
2025-06-30T09:38:18.6587996+05:30 [Information] Step Iteration 1/2: Running for Tom and role Admin
2025-06-30T09:38:19.4247112+05:30 [Information] Step Iteration 2/2: Running for Tom and role User
2025-06-30T09:38:19.6827757+05:30 [Information] [ OK       ]
2025-06-30T09:38:19.6854574+05:30 [Information] Test Iteration 2/2: Running Test for John
2025-06-30T09:38:19.6859770+05:30 [Information] [ RUN      ] Itterative Test and Step > Step to get token for John
2025-06-30T09:38:19.9366478+05:30 [Information] [ OK       ]
2025-06-30T09:38:19.9374074+05:30 [Information] [ RUN      ] Itterative Test and Step > Itterative Step for user John
2025-06-30T09:38:19.9389753+05:30 [Information] Step Iteration 1/2: Running for John and role Admin
2025-06-30T09:38:20.2713718+05:30 [Information] Step Iteration 2/2: Running for John and role User
2025-06-30T09:38:20.9790023+05:30 [Information] [ OK       ]
2025-06-30T09:38:20.9793161+05:30 [Information] [----------] 2 steps from Itterative Test and Step (3512 ms total)
2025-06-30T09:38:20.9803669+05:30 [Information] [----------] 2 steps from Test with include
2025-06-30T09:38:20.9812181+05:30 [Information] [ RUN      ] Test with include > GET Token
2025-06-30T09:38:21.7603674+05:30 [Information] [ OK       ]
2025-06-30T09:38:21.7626588+05:30 [Information] [ RUN      ] Test with include > Step to test POST request with token
2025-06-30T09:38:22.5411947+05:30 [Information] [ OK       ]
2025-06-30T09:38:22.5417471+05:30 [Information] [----------] 2 steps from Test with include (1560 ms total)
2025-06-30T09:38:22.5428357+05:30 [Information] [----------] 5 steps from ProtoFieldSetters test
2025-06-30T09:38:22.5437478+05:30 [Information] [ RUN      ] ProtoFieldSetters test > Step 1: Connect to the MBusClient with admin credentials
2025-06-30T09:38:24.5744421+05:30 [Error] Failed to execute step : Cannot connect to 127.0.0.1:60606.
2025-06-30T09:38:24.5746099+05:30 [Error] [ FAILED   ]
2025-06-30T09:38:24.5753036+05:30 [Information] [----------] 5 steps from ProtoFieldSetters test (2031 ms total)
2025-06-30T09:38:24.5774259+05:30 [Information] [----------] 4 steps from ProtoReference test
2025-06-30T09:38:24.5783506+05:30 [Information] [ RUN      ] ProtoReference test > Step 1: Connect to the MBusClient with admin credentials
2025-06-30T09:38:26.5905576+05:30 [Error] Failed to execute step : Cannot connect to 127.0.0.1:60606.
2025-06-30T09:38:26.5914884+05:30 [Error] [ FAILED   ]
2025-06-30T09:38:26.5922283+05:30 [Information] [----------] 4 steps from ProtoReference test (2014 ms total)
2025-06-30T09:38:26.5933527+05:30 [Information] [-------------------------------------    Teardown    -------------------------------------]
2025-06-30T09:38:26.5949767+05:30 [Information] [----------] 1 step from Teardown
2025-06-30T09:38:26.5956159+05:30 [Information] [ RUN      ] Teardown > Clean up Database
2025-06-30T09:38:27.6976809+05:30 [Information] [ OK       ]
2025-06-30T09:38:27.6983272+05:30 [Information] [----------] 1 step from Teardown (1102 ms total)
2025-06-30T09:38:27.6988167+05:30 [Information] Global variables set to: curDir: D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc, baseUrl: https://httpbin.org, serverIp: 127.0.0.1, serverPort: 60606, dbInitStatus: initialize_db
2025-06-30T09:38:27.6989605+05:30 [Information] [-------------------------------------  Test Results  -------------------------------------]
2025-06-30T09:38:27.6994490+05:30 [Information] [==========] 13 tests from Yaml files ran. (17671 ms total)
2025-06-30T09:38:27.7005961+05:30 [Information] [  PASSED  ] 11 tests.
2025-06-30T09:38:27.7008161+05:30 [Error] [  FAILED  ] 2 tests, listed below:
2025-06-30T09:38:27.7011918+05:30 [Error] [  FAILED  ] ProtoFieldSetters test
2025-06-30T09:38:27.7014069+05:30 [Error] [  FAILED  ] ProtoReference test
