2025-06-30T09:37:31.7045277+05:30 [Information] [----------] 1 step from Simple Get Request
2025-06-30T09:37:31.7051905+05:30 [Information] Set Value to Step 'faa9602f-d15a-409c-9308-c1a079cc0ae3' name:
"Step get request"
2025-06-30T09:37:31.7053130+05:30 [Information] Set Value to Step 'faa9602f-d15a-409c-9308-c1a079cc0ae3' description:
"Simple GET Request"
2025-06-30T09:37:31.7054466+05:30 [Information] [ RUN      ] Simple Get Request > Step get request
2025-06-30T09:37:31.7058637+05:30 [Information] Executing single instance of step 'Step get request'.
2025-06-30T09:37:31.7060091+05:30 [Information] Executing step 'Step get request' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "faa9602f-d15a-409c-9308-c1a079cc0ae3",
        "tables": {},
        "variables": {},
        "name": "Step get request",
        "description": "Simple GET Request"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "ProductID": 123
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ],
      "ProductTable": [
        {
          "ProductID": "123",
          "Name": "Laptop",
          "Price": "1500",
          "Specifications": {
            "Processor": "Intel i7",
            "RAM": "16GB",
            "Storage": "512GB SSD"
          }
        },
        {
          "ProductID": "102",
          "Name": "Smartphone",
          "Price": "800",
          "Specifications": {
            "Processor": "Snapdragon 888",
            "RAM": "8GB",
            "Storage": "128GB"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:37:31.7063284+05:30 [Information] Processing $var: path 'baseUrl'
2025-06-30T09:37:31.7064286+05:30 [Information] Checking if 'baseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:31.7065305+05:30 [Information] Resolving path 'baseUrl' using global variables.
2025-06-30T09:37:31.7066468+05:30 [Information] Resolved JPath '$var:baseUrl' to 'https://httpbin.org'
2025-06-30T09:37:31.7067264+05:30 [Information] Resolved template pattern '{{$var:baseUrl}}' to 'https://httpbin.org' in '{{$var:baseUrl}}/get'
2025-06-30T09:37:31.7068040+05:30 [Information] Resolved template '{{$var:baseUrl}}/get' to 'https://httpbin.org/get'
2025-06-30T09:37:31.7070265+05:30 [Information] Set Value to Step 'faa9602f-d15a-409c-9308-c1a079cc0ae3' input:
{
  "Method": "GET",
  "RequestUri": "https://httpbin.org/get",
  "Headers": {
    "Content-Type": "application/json"
  }
}
2025-06-30T09:37:32.4589705+05:30 [Information] Set Value to Step '0fcdf269-3018-46b2-8d5c-521f0edf5800' output:
{
  "statusCode": 200,
  "reasonPhrase": "OK",
  "headers": [
    {
      "Key": "Date",
      "Value": [
        "Mon, 30 Jun 2025 04:07:37 GMT"
      ]
    },
    {
      "Key": "Connection",
      "Value": [
        "keep-alive"
      ]
    },
    {
      "Key": "Server",
      "Value": [
        "gunicorn/19.9.0"
      ]
    },
    {
      "Key": "Access-Control-Allow-Origin",
      "Value": [
        "*"
      ]
    },
    {
      "Key": "Access-Control-Allow-Credentials",
      "Value": [
        "true"
      ]
    }
  ],
  "content": {
    "args": {},
    "headers": {
      "Host": "httpbin.org",
      "X-Amzn-Trace-Id": "Root=1-68620d89-20733f8c0012f8072abe782e"
    },
    "origin": "**************",
    "url": "https://httpbin.org/get"
  }
}
2025-06-30T09:37:32.4627652+05:30 [Information] Run Asserter : {"Expression1":{"Value":"200","ErrorMesage":null},"Expression2":{"JPath":"$curStep:$.output.statusCode","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:37:32.4632215+05:30 [Information] Resolving path '$.output.statusCode' using $curStep:
2025-06-30T09:37:32.4634793+05:30 [Information] Resolved JPath '$curStep:$.output.statusCode' to '200'
2025-06-30T09:37:32.4639894+05:30 [Information] Run Asserter : {"Expression1":{"Value":"OK","ErrorMesage":null},"Expression2":{"JPath":"output.reasonPhrase","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:37:32.4643556+05:30 [Information] Resolving path 'output.reasonPhrase' using step input/output.
2025-06-30T09:37:32.4645483+05:30 [Information] Resolved JPath 'output.reasonPhrase' to 'OK'
2025-06-30T09:37:32.4648712+05:30 [Information] Set Value to Step 'faa9602f-d15a-409c-9308-c1a079cc0ae3' output:
{
  "statusCode": 200,
  "reasonPhrase": "OK",
  "headers": [
    {
      "Key": "Date",
      "Value": [
        "Mon, 30 Jun 2025 04:07:37 GMT"
      ]
    },
    {
      "Key": "Connection",
      "Value": [
        "keep-alive"
      ]
    },
    {
      "Key": "Server",
      "Value": [
        "gunicorn/19.9.0"
      ]
    },
    {
      "Key": "Access-Control-Allow-Origin",
      "Value": [
        "*"
      ]
    },
    {
      "Key": "Access-Control-Allow-Credentials",
      "Value": [
        "true"
      ]
    }
  ],
  "content": {
    "args": {},
    "headers": {
      "Host": "httpbin.org",
      "X-Amzn-Trace-Id": "Root=1-68620d89-20733f8c0012f8072abe782e"
    },
    "origin": "**************",
    "url": "https://httpbin.org/get"
  }
}
2025-06-30T09:37:32.4650814+05:30 [Information] Set Value to Step 'faa9602f-d15a-409c-9308-c1a079cc0ae3' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:07:31.7058604Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:37:32.4652049+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$var:baseUrl}}/get","ErrorMesage":null},"Expression2":{"JPath":"$curStep:$.output.content.url","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:37:32.4653034+05:30 [Information] Processing $var: path 'baseUrl'
2025-06-30T09:37:32.4653911+05:30 [Information] Checking if 'baseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:32.4654657+05:30 [Information] Resolving path 'baseUrl' using global variables.
2025-06-30T09:37:32.4655365+05:30 [Information] Resolved JPath '$var:baseUrl' to 'https://httpbin.org'
2025-06-30T09:37:32.4655946+05:30 [Information] Resolved template pattern '{{$var:baseUrl}}' to 'https://httpbin.org' in '{{$var:baseUrl}}/get'
2025-06-30T09:37:32.4656546+05:30 [Information] Resolved template '{{$var:baseUrl}}/get' to 'https://httpbin.org/get'
2025-06-30T09:37:32.4658465+05:30 [Information] Resolving path '$.output.content.url' using $curStep:
2025-06-30T09:37:32.4660180+05:30 [Information] Resolved JPath '$curStep:$.output.content.url' to 'https://httpbin.org/get'
2025-06-30T09:37:32.4661215+05:30 [Information] Run Asserter : {"Expression1":{"Value":"200","ErrorMesage":null},"Expression2":{"JPath":"$curStep:$.output.statusCode","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:37:32.4661977+05:30 [Information] Resolving path '$.output.statusCode' using $curStep:
2025-06-30T09:37:32.4664110+05:30 [Information] Resolved JPath '$curStep:$.output.statusCode' to '200'
2025-06-30T09:37:32.4665283+05:30 [Information] Run Asserter : {"Expression1":{"Value":"OK","ErrorMesage":null},"Expression2":{"JPath":"output.reasonPhrase","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:37:32.4666441+05:30 [Information] Resolving path 'output.reasonPhrase' using step input/output.
2025-06-30T09:37:32.4667178+05:30 [Information] Resolved JPath 'output.reasonPhrase' to 'OK'
2025-06-30T09:37:32.4668151+05:30 [Information] Run Asserter : {"Expression1":{"JPath":"$table:ProductTable[0].ProductID","ErrorMesage":null},"Expression2":{"Value":"{{$var:ProductID}}","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:37:32.4678652+05:30 [Information] Resolving path 'ProductTable[0].ProductID' using global tables.
2025-06-30T09:37:32.4681381+05:30 [Information] Resolved JPath '$table:ProductTable[0].ProductID' to '123'
2025-06-30T09:37:32.4682527+05:30 [Information] Processing $var: path 'ProductID'
2025-06-30T09:37:32.4683226+05:30 [Information] Checking if 'ProductID' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:32.4684590+05:30 [Information] Resolving path 'ProductID' using global variables.
2025-06-30T09:37:32.4685796+05:30 [Information] Resolved JPath '$var:ProductID' to '123'
2025-06-30T09:37:32.4686403+05:30 [Information] Resolved template pattern '{{$var:ProductID}}' to '123' in '{{$var:ProductID}}'
2025-06-30T09:37:32.4687031+05:30 [Information] Resolved template '{{$var:ProductID}}' to '123'
2025-06-30T09:37:32.4687780+05:30 [Information] Run Asserter : {"Expression1":{"JPath":"$table:UserTable[0].Username","ErrorMesage":null},"Expression2":{"Value":"user1","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:37:32.4688317+05:30 [Information] Resolving path 'UserTable[0].Username' using global tables.
2025-06-30T09:37:32.4688891+05:30 [Information] Resolved JPath '$table:UserTable[0].Username' to 'user1'
2025-06-30T09:37:32.4691806+05:30 [Information] Set Value to Step 'faa9602f-d15a-409c-9308-c1a079cc0ae3' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 762,
  "startTime": "2025-06-30T04:07:31.7058604Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:37:32.4693304+05:30 [Information] Step 'Step get request' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "faa9602f-d15a-409c-9308-c1a079cc0ae3",
        "tables": {},
        "variables": {},
        "name": "Step get request",
        "description": "Simple GET Request",
        "input": {
          "Method": "GET",
          "RequestUri": "https://httpbin.org/get",
          "Headers": {
            "Content-Type": "application/json"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:07:37 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "headers": {
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620d89-20733f8c0012f8072abe782e"
            },
            "origin": "**************",
            "url": "https://httpbin.org/get"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 762,
          "startTime": "2025-06-30T04:07:31.7058604Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "ProductID": 123
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ],
      "ProductTable": [
        {
          "ProductID": "123",
          "Name": "Laptop",
          "Price": "1500",
          "Specifications": {
            "Processor": "Intel i7",
            "RAM": "16GB",
            "Storage": "512GB SSD"
          }
        },
        {
          "ProductID": "102",
          "Name": "Smartphone",
          "Price": "800",
          "Specifications": {
            "Processor": "Snapdragon 888",
            "RAM": "8GB",
            "Storage": "128GB"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:37:32.4694104+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:37:32.4694640+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:37:32.4695057+05:30 [Information] No performance data found for step 'Step get request' (http)
2025-06-30T09:37:32.4695514+05:30 [Information] [ OK       ]
2025-06-30T09:37:32.4701619+05:30 [Information] [----------] 1 step from Simple Get Request (764 ms total)
