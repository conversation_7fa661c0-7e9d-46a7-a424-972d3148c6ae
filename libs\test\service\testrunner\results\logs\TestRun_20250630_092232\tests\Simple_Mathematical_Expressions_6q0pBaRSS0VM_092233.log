2025-06-30T09:22:33.3293449+05:30 [Information] [----------] 6 steps from Simple Mathematical Expressions Demo
2025-06-30T09:22:33.3306649+05:30 [Information] Set Value to Step '59947fc5-e7e8-476b-9fc5-ba066db3e734' name:
"Fast Baseline Operation"
2025-06-30T09:22:33.3308083+05:30 [Information] [ RUN      ] Simple Mathematical Expressions Demo > Fast Baseline Operation
2025-06-30T09:22:33.3312045+05:30 [Information] Executing single instance of step 'Fast Baseline Operation'.
2025-06-30T09:22:33.3313859+05:30 [Information] Executing step 'Fast Baseline Operation' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "59947fc5-e7e8-476b-9fc5-ba066db3e734",
        "tables": {},
        "variables": {},
        "name": "Fast Baseline Operation"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaselineThreshold": 100,
      "MaxRegressionPercent": 150,
      "BufferTime": 50,
      "FastThreshold": 5000,
      "SlowThreshold": 10000,
      "MathBaseline": 1
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:22:33.3323862+05:30 [Information] Set Value to Step '59947fc5-e7e8-476b-9fc5-ba066db3e734' input:
{
  "command": "ping",
  "arguments": [
    "127.0.0.1",
    "-n",
    "1"
  ],
  "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
  "printOutput": false
}
2025-06-30T09:22:33.3325457+05:30 [Information] Delaying step execution for 50ms
2025-06-30T09:22:33.4289772+05:30 [Information] Set Value to Step '59947fc5-e7e8-476b-9fc5-ba066db3e734' output:
{
  "stdout": "Pinging 127.0.0.1 with 32 bytes of data:\r\nReply from 127.0.0.1: bytes=32 time<1ms TTL=128\r\nPing statistics for 127.0.0.1:\r\n    Packets: Sent = 1, Received = 1, Lost = 0 (0% loss),\r\nApproximate round trip times in milli-seconds:\r\n    Minimum = 0ms, Maximum = 0ms, Average = 0ms",
  "stderr": "",
  "exitCode": 0
}
2025-06-30T09:22:33.4292118+05:30 [Information] Resolving path 'performance.executionTimeMs' using global context.
2025-06-30T09:22:33.4293258+05:30 [Information] Resolved JPath 'performance.executionTimeMs' to ''
2025-06-30T09:22:33.4294486+05:30 [Information] Set Value to Step '59947fc5-e7e8-476b-9fc5-ba066db3e734' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T03:52:33.3311963Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "cmd"
}
2025-06-30T09:22:33.4301842+05:30 [Information] Run Asserter : {"Expression1":{"Value":"0","ErrorMesage":"Ping command failed"},"Expression2":{"JPath":"$curStep:$.output.exitCode","ErrorMesage":"Ping command failed"},"ErrorMesage":"Ping command failed"}
2025-06-30T09:22:33.4303380+05:30 [Information] Resolving path '$.output.exitCode' using $curStep:
2025-06-30T09:22:33.4304444+05:30 [Information] Resolved JPath '$curStep:$.output.exitCode' to '0'
2025-06-30T09:22:33.4314773+05:30 [Information] Run Asserter : {"_lessExpression":{"Expression1":{"Value":"0","ErrorMesage":"Performance data not available"},"Expression2":{"JPath":"$curStep:performance.executionTimeMs","ErrorMesage":"Performance data not available"},"ErrorMesage":null},"Expression1":{"Value":"0","ErrorMesage":"Performance data not available"},"Expression2":{"JPath":"$curStep:performance.executionTimeMs","ErrorMesage":"Performance data not available"},"ErrorMesage":"Performance data not available"}
2025-06-30T09:22:33.4316367+05:30 [Information] Resolving path 'performance.executionTimeMs' using $curStep:
2025-06-30T09:22:33.4317335+05:30 [Information] Resolved JPath '$curStep:performance.executionTimeMs' to '0'
2025-06-30T09:22:33.4320947+05:30 [Information] Set Value to Step '59947fc5-e7e8-476b-9fc5-ba066db3e734' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 99,
  "startTime": "2025-06-30T03:52:33.3311963Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "cmd"
}
2025-06-30T09:22:33.4322565+05:30 [Information] Step 'Fast Baseline Operation' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "59947fc5-e7e8-476b-9fc5-ba066db3e734",
        "tables": {},
        "variables": {},
        "name": "Fast Baseline Operation",
        "input": {
          "command": "ping",
          "arguments": [
            "127.0.0.1",
            "-n",
            "1"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Pinging 127.0.0.1 with 32 bytes of data:\r\nReply from 127.0.0.1: bytes=32 time<1ms TTL=128\r\nPing statistics for 127.0.0.1:\r\n    Packets: Sent = 1, Received = 1, Lost = 0 (0% loss),\r\nApproximate round trip times in milli-seconds:\r\n    Minimum = 0ms, Maximum = 0ms, Average = 0ms",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 99,
          "startTime": "2025-06-30T03:52:33.3311963Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaselineThreshold": 100,
      "MaxRegressionPercent": 150,
      "BufferTime": 50,
      "FastThreshold": 5000,
      "SlowThreshold": 10000,
      "MathBaseline": 1
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:22:33.4323363+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:22:33.4324033+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:22:33.4324532+05:30 [Information] No performance data found for step 'Fast Baseline Operation' (cmd)
2025-06-30T09:22:33.4325013+05:30 [Information] [ OK       ]
2025-06-30T09:22:33.4329275+05:30 [Information] Set Value to Step '7760440e-6efd-45aa-ae3b-b13ca2de367f' name:
"Regression Threshold Test"
2025-06-30T09:22:33.4330434+05:30 [Information] [ RUN      ] Simple Mathematical Expressions Demo > Regression Threshold Test
2025-06-30T09:22:33.4333297+05:30 [Information] Executing single instance of step 'Regression Threshold Test'.
2025-06-30T09:22:33.4334756+05:30 [Information] Executing step 'Regression Threshold Test' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "59947fc5-e7e8-476b-9fc5-ba066db3e734",
        "tables": {},
        "variables": {},
        "name": "Fast Baseline Operation",
        "input": {
          "command": "ping",
          "arguments": [
            "127.0.0.1",
            "-n",
            "1"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Pinging 127.0.0.1 with 32 bytes of data:\r\nReply from 127.0.0.1: bytes=32 time<1ms TTL=128\r\nPing statistics for 127.0.0.1:\r\n    Packets: Sent = 1, Received = 1, Lost = 0 (0% loss),\r\nApproximate round trip times in milli-seconds:\r\n    Minimum = 0ms, Maximum = 0ms, Average = 0ms",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 99,
          "startTime": "2025-06-30T03:52:33.3311963Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "7760440e-6efd-45aa-ae3b-b13ca2de367f",
        "tables": {},
        "variables": {},
        "name": "Regression Threshold Test"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaselineThreshold": 100,
      "MaxRegressionPercent": 150,
      "BufferTime": 50,
      "FastThreshold": 5000,
      "SlowThreshold": 10000,
      "MathBaseline": 1
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:22:33.4336395+05:30 [Information] Set Value to Step '7760440e-6efd-45aa-ae3b-b13ca2de367f' input:
{
  "command": "timeout",
  "arguments": [
    "/t",
    "1",
    "/nobreak"
  ],
  "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
  "printOutput": false
}
2025-06-30T09:22:33.4337268+05:30 [Information] Delaying step execution for 200ms
2025-06-30T09:22:34.2174825+05:30 [Information] Set Value to Step '7760440e-6efd-45aa-ae3b-b13ca2de367f' output:
{
  "stdout": "Waiting for 1 seconds, press CTRL+C to quit ...\b0",
  "stderr": "",
  "exitCode": 0
}
2025-06-30T09:22:34.2183080+05:30 [Information] Set Value to Step '7760440e-6efd-45aa-ae3b-b13ca2de367f' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T03:52:33.433327Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "cmd"
}
2025-06-30T09:22:34.2185717+05:30 [Information] Run Asserter : {"Expression1":{"Value":"0","ErrorMesage":"Ping command failed"},"Expression2":{"JPath":"$curStep:$.output.exitCode","ErrorMesage":"Ping command failed"},"ErrorMesage":"Ping command failed"}
2025-06-30T09:22:34.2187133+05:30 [Information] Resolving path '$.output.exitCode' using $curStep:
2025-06-30T09:22:34.2188362+05:30 [Information] Resolved JPath '$curStep:$.output.exitCode' to '0'
2025-06-30T09:22:34.2190104+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$var:MathBaseline * $var:MaxRegressionPercent / 100}}","ErrorMesage":"Performance regression detected: {{$curStep:performance.executionTimeMs}}ms vs allowed {{$var:MathBaseline * $var:MaxRegressionPercent / 100}}ms ({{$var:MathBaseline}}ms * {{$var:MaxRegressionPercent}}% / 100)"},"Expression2":{"JPath":"$curStep:performance.executionTimeMs","ErrorMesage":"Performance regression detected: {{$curStep:performance.executionTimeMs}}ms vs allowed {{$var:MathBaseline * $var:MaxRegressionPercent / 100}}ms ({{$var:MathBaseline}}ms * {{$var:MaxRegressionPercent}}% / 100)"},"ErrorMesage":"Performance regression detected: {{$curStep:performance.executionTimeMs}}ms vs allowed {{$var:MathBaseline * $var:MaxRegressionPercent / 100}}ms ({{$var:MathBaseline}}ms * {{$var:MaxRegressionPercent}}% / 100)"}
2025-06-30T09:22:34.2191540+05:30 [Information] Processing $var: path 'MathBaseline * $var:MaxRegressionPercent / 100'
2025-06-30T09:22:34.2192494+05:30 [Information] Checking if 'MathBaseline * $var:MaxRegressionPercent / 100' is mathematical expression: hasArithmetic=True, hasArray=False, hasProperty=False, result=True
2025-06-30T09:22:34.2193174+05:30 [Information] Detected mathematical expression in $var: 'MathBaseline * $var:MaxRegressionPercent / 100'
2025-06-30T09:22:34.2196050+05:30 [Information] Evaluating mathematical expression: 'MathBaseline * $var:MaxRegressionPercent / 100'
2025-06-30T09:22:34.2198596+05:30 [Information] Resolving variables in expression: 'MathBaseline * $var:MaxRegressionPercent / 100'
2025-06-30T09:22:34.2202738+05:30 [Information] Resolved variable 'MaxRegressionPercent' from global variables: 150
2025-06-30T09:22:34.2205760+05:30 [Information] Resolved variable 'MathBaseline' from global variables: 1
2025-06-30T09:22:34.2206776+05:30 [Information] Expression after variable resolution: '1 * 150 / 100'
2025-06-30T09:22:34.2207369+05:30 [Information] Expression after variable resolution: '1 * 150 / 100'
2025-06-30T09:22:34.2237353+05:30 [Information] Mathematical expression 'MathBaseline * $var:MaxRegressionPercent / 100' evaluated to: 1.5
2025-06-30T09:22:34.2238513+05:30 [Information] Mathematical expression result: '1.5'
2025-06-30T09:22:34.2239415+05:30 [Information] Resolved JPath '$var:MathBaseline * $var:MaxRegressionPercent / 100' to '1.5'
2025-06-30T09:22:34.2240023+05:30 [Information] Resolved template pattern '{{$var:MathBaseline * $var:MaxRegressionPercent / 100}}' to '1.5' in '{{$var:MathBaseline * $var:MaxRegressionPercent / 100}}'
2025-06-30T09:22:34.2242376+05:30 [Information] Resolved template '{{$var:MathBaseline * $var:MaxRegressionPercent / 100}}' to '1.5'
2025-06-30T09:22:34.2244567+05:30 [Information] Resolving path 'performance.executionTimeMs' using $curStep:
2025-06-30T09:22:34.2245577+05:30 [Information] Resolved JPath '$curStep:performance.executionTimeMs' to '0'
2025-06-30T09:22:34.2247292+05:30 [Error] Asserter error : {"Expression1":{"Value":"{{$var:MathBaseline * $var:MaxRegressionPercent / 100}}","ErrorMesage":"Performance regression detected: {{$curStep:performance.executionTimeMs}}ms vs allowed {{$var:MathBaseline * $var:MaxRegressionPercent / 100}}ms ({{$var:MathBaseline}}ms * {{$var:MaxRegressionPercent}}% / 100)"},"Expression2":{"JPath":"$curStep:performance.executionTimeMs","ErrorMesage":"Performance regression detected: {{$curStep:performance.executionTimeMs}}ms vs allowed {{$var:MathBaseline * $var:MaxRegressionPercent / 100}}ms ({{$var:MathBaseline}}ms * {{$var:MaxRegressionPercent}}% / 100)"},"ErrorMesage":"Performance regression detected: {{$curStep:performance.executionTimeMs}}ms vs allowed {{$var:MathBaseline * $var:MaxRegressionPercent / 100}}ms ({{$var:MathBaseline}}ms * {{$var:MaxRegressionPercent}}% / 100)"}
2025-06-30T09:22:34.2260410+05:30 [Error] Assert Failure : Less(1.5, 0)
2025-06-30T09:22:34.2265237+05:30 [Information] Failed to execute step 'Regression Threshold Test': Performance regression detected: {{$curStep:performance.executionTimeMs}}ms vs allowed {{$var:MathBaseline * $var:MaxRegressionPercent / 100}}ms ({{$var:MathBaseline}}ms * {{$var:MaxRegressionPercent}}% / 100)
2025-06-30T09:22:34.2266743+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:22:34.2267906+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:22:34.2271448+05:30 [Information] No performance data found for step 'Regression Threshold Test' (cmd)
2025-06-30T09:22:34.2274419+05:30 [Error] Performance regression detected: {{$curStep:performance.executionTimeMs}}ms vs allowed {{$var:MathBaseline * $var:MaxRegressionPercent / 100}}ms ({{$var:MathBaseline}}ms * {{$var:MaxRegressionPercent}}% / 100)
2025-06-30T09:22:34.2276391+05:30 [Error] [ FAILED   ]
2025-06-30T09:22:34.2280728+05:30 [Information] [----------] 6 steps from Simple Mathematical Expressions Demo (898 ms total)
