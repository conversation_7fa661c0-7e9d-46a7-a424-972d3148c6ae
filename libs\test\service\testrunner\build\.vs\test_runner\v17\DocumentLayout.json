{"Version": 1, "WorkspaceRootPath": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\build\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{FD0E94C6-25B2-30BC-8BAE-D1D40566DA32}|test_runner.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\build\\test_runner.csproj||{04B8AB82-A572-4FEF-95CE-5222444B6B64}|", "RelativeMoniker": "D:0:0:{FD0E94C6-25B2-30BC-8BAE-D1D40566DA32}|test_runner.csproj|solutionrelative:test_runner.csproj||{04B8AB82-A572-4FEF-95CE-5222444B6B64}|"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\teststep.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\http\\httpteststepexecutor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\utils\\performancereporttemplate.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\rpc\\rpcteststepexecutor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\cmd\\cmdteststepfactory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\teststepfactory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\rpc\\rpcteststepfactory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\http\\httpteststepfactory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\mbus\\mbusteststepfactory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\baseteststepexecutor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\rpc\\rpcteststepinput.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\utils\\testloggingmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\rpc\\rpcteststepoutput.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\utils\\asserterbasedretrycondition.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\rpc\\rpcteststepinputfactory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\testoutputsettings.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{FD0E94C6-25B2-30BC-8BAE-D1D40566DA32}|test_runner.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\src\\runneroptions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\testsuite.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\utils\\filelogger.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\test.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\utils\\testlogger.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{FD0E94C6-25B2-30BC-8BAE-D1D40566DA32}|test_runner.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\src\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\utils\\testconsolewriter.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\context\\contextutils.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\testfactory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\build\\testlib\\testlib.csproj||{04B8AB82-A572-4FEF-95CE-5222444B6B64}|", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:testlib\\testlib.csproj||{04B8AB82-A572-4FEF-95CE-5222444B6B64}|"}, {"AbsoluteMoniker": "D:0:0:{D1380BDD-0CE7-3746-B747-203D6F813B9D}|tesertestli\\cocshmercur\\mercury_csharp.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\comm\\csharp\\mercury-csharp\\src\\rpcclient.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D1380BDD-0CE7-3746-B747-203D6F813B9D}|tesertestli\\cocshmercur\\mercury_csharp.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\comm\\csharp\\mercury-csharp\\src\\mbusclient.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9C2F0F76-3F36-3FB6-8A0B-7E50A7549EAA}|tesertestli\\apicsharp\\interop_api_csharp.csproj|d:\\kibisoft\\workarea\\git\\interop\\api\\csharp\\generated\\iamservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9C2F0F76-3F36-3FB6-8A0B-7E50A7549EAA}|tesertestli\\apicsharp\\interop_api_csharp.csproj|d:\\kibisoft\\workarea\\git\\interop\\api\\csharp\\generated\\user.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\context\\testdata.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\context\\tablemanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\context\\testcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\notequalexpression.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\context\\jpathresolver.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\lessexpression.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\context\\variablemanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\lessorequalexpression.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\greaterorequalexpression.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\jsonpathexpression.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\greaterexpression.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\iexpression.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\binaryexpression.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\constantexpression.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\equalexpression.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\utils\\testutils.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\containexpression.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\notcontainexpression.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\utils\\jtokencomparer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\csscriptexpression.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\rpc\\rpcteststepinputtemplate.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\utils\\jtokenextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\context\\tablefileloader.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\teststepoutputsettings.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\jsonschemaexpression.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\testsuiteconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\cmd\\cmdteststepinputfactory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\mbus\\mbusteststepoutput.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\mbus\\mbusteststepinputtemplate.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\mbus\\mbusteststepinputfactory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\mbus\\mbusteststepexecutor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\assertions\\iasserter.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\assertions\\asserterfactory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\cmd\\cmdteststepexecutor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\utils\\fileloggerprovider.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\assertions\\assert.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\teststepexecutionsettings.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\http\\httpteststepoutputsettings.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\cmd\\cmdteststepinputtemplate.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\cmd\\cmdteststepinput.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\http\\httpteststepoutput.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\http\\httpteststepinputfactory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\http\\httpteststepinput.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\http\\httpteststepinputtemplate.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\mbus\\mbusteststepinput.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\cmd\\cmdteststepoutput.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{606C93E9-2AF2-30BF-BE22-0955749D5A65}|tesertestli\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\pyscriptexpression.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\utils\\SkipEmptyCollectionsTypeInspector .cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{e1b7d1f8-9b3c-49b1-8f4f-bfc63a88835d}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "test_runner", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\build\\test_runner.csproj", "RelativeDocumentMoniker": "test_runner.csproj", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\build\\test_runner.csproj", "RelativeToolTip": "test_runner.csproj", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-06-11T11:05:30.648Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 24, "Title": "ContextUtils.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\context\\ContextUtils.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\context\\ContextUtils.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\context\\ContextUtils.cs", "RelativeToolTip": "..\\..\\testlib\\src\\context\\ContextUtils.cs", "ViewState": "AgIAAKUAAAAAAAAAAIAwwNUAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-21T17:11:49.143Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "RpcTestStepInput.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\rpc\\RpcTestStepInput.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\interop\\rpc\\RpcTestStepInput.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\rpc\\RpcTestStepInput.cs", "RelativeToolTip": "..\\..\\testlib\\src\\interop\\rpc\\RpcTestStepInput.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA4AAAAVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-10-09T09:06:12.931Z"}, {"$type": "Document", "DocumentIndex": 1, "Title": "TestStep.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\TestStep.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\TestStep.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\TestStep.cs", "RelativeToolTip": "..\\..\\testlib\\src\\TestStep.cs", "ViewState": "AgIAAAQBAAAAAAAAAAAAABMBAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-26T06:13:43.173Z"}, {"$type": "Document", "DocumentIndex": 12, "Title": "TestLoggingManager.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\utils\\TestLoggingManager.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\utils\\TestLoggingManager.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\utils\\TestLoggingManager.cs", "RelativeToolTip": "..\\..\\testlib\\src\\utils\\TestLoggingManager.cs", "ViewState": "AgIAACkAAAAAAAAAAAAqwDIAAABfAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-27T05:59:01.493Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "HttpTestStepFactory.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\http\\HttpTestStepFactory.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\http\\HttpTestStepFactory.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\http\\HttpTestStepFactory.cs", "RelativeToolTip": "..\\..\\testlib\\src\\http\\HttpTestStepFactory.cs", "ViewState": "AgIAAB8AAAAAAAAAAAAswCoAAAAaAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-26T07:17:33.36Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "MBusTestStepFactory.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\mbus\\MBusTestStepFactory.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\interop\\mbus\\MBusTestStepFactory.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\mbus\\MBusTestStepFactory.cs", "RelativeToolTip": "..\\..\\testlib\\src\\interop\\mbus\\MBusTestStepFactory.cs", "ViewState": "AgIAABsAAAAAAAAAAAAswCkAAAAaAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-11T09:15:45.258Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "RpcTestStepFactory.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\rpc\\RpcTestStepFactory.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\interop\\rpc\\RpcTestStepFactory.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\rpc\\RpcTestStepFactory.cs", "RelativeToolTip": "..\\..\\testlib\\src\\interop\\rpc\\RpcTestStepFactory.cs", "ViewState": "AgIAACUAAAAAAAAAAAAswDcAAAAbAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-10-09T05:03:04.875Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "TestStepFactory.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\TestStepFactory.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\TestStepFactory.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\TestStepFactory.cs", "RelativeToolTip": "..\\..\\testlib\\src\\TestStepFactory.cs", "ViewState": "AgIAAHEAAAAAAAAAAADwv4oAAAA0AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-25T06:39:06.021Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "PerformanceReportTemplate.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\utils\\PerformanceReportTemplate.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\utils\\PerformanceReportTemplate.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\utils\\PerformanceReportTemplate.cs", "RelativeToolTip": "..\\..\\testlib\\src\\utils\\PerformanceReportTemplate.cs", "ViewState": "AgIAADIAAAAAAAAAAAAUwEcAAAAwAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-04T06:19:27.916Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "HttpTestStepExecutor.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\http\\HttpTestStepExecutor.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\http\\HttpTestStepExecutor.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\http\\HttpTestStepExecutor.cs", "RelativeToolTip": "..\\..\\testlib\\src\\http\\HttpTestStepExecutor.cs", "ViewState": "AgIAABUAAAAAAAAAAAAowCgAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-11T09:12:20.7Z"}, {"$type": "Document", "DocumentIndex": 13, "Title": "RpcTestStepOutput.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\rpc\\RpcTestStepOutput.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\interop\\rpc\\RpcTestStepOutput.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\rpc\\RpcTestStepOutput.cs", "RelativeToolTip": "..\\..\\testlib\\src\\interop\\rpc\\RpcTestStepOutput.cs", "ViewState": "AgIAACUAAAAAAAAAAAA0wDEAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-10-08T06:49:34.225Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "BaseTestStepExecutor.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\BaseTestStepExecutor.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\BaseTestStepExecutor.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\BaseTestStepExecutor.cs", "RelativeToolTip": "..\\..\\testlib\\src\\BaseTestStepExecutor.cs", "ViewState": "AgIAADgAAAAAAAAAAADwv0sAAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-26T06:14:04.542Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "RpcTestStepExecutor.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\rpc\\RpcTestStepExecutor.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\interop\\rpc\\RpcTestStepExecutor.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\rpc\\RpcTestStepExecutor.cs", "RelativeToolTip": "..\\..\\testlib\\src\\interop\\rpc\\RpcTestStepExecutor.cs", "ViewState": "AgIAAF0AAAAAAAAAAADwv3MAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-26T04:39:42.821Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "CmdTestStepFactory.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\cmd\\CmdTestStepFactory.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\cmd\\CmdTestStepFactory.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\cmd\\CmdTestStepFactory.cs", "RelativeToolTip": "..\\..\\testlib\\src\\cmd\\CmdTestStepFactory.cs", "ViewState": "AgIAAAkAAAAAAAAAAADwvxYAAAAZAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-11T09:16:43.908Z"}, {"$type": "Document", "DocumentIndex": 14, "Title": "AsserterBasedRetryCondition.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\utils\\AsserterBasedRetryCondition.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\utils\\AsserterBasedRetryCondition.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\utils\\AsserterBasedRetryCondition.cs", "RelativeToolTip": "..\\..\\testlib\\src\\utils\\AsserterBasedRetryCondition.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-16T01:35:27.668Z"}, {"$type": "Document", "DocumentIndex": 16, "Title": "TestOutputSettings.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\TestOutputSettings.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\TestOutputSettings.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\TestOutputSettings.cs", "RelativeToolTip": "..\\..\\testlib\\src\\TestOutputSettings.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAUwA4AAAAUAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-09T11:17:43.702Z"}, {"$type": "Document", "DocumentIndex": 21, "Title": "TestLogger.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\utils\\TestLogger.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\utils\\TestLogger.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\utils\\TestLogger.cs", "RelativeToolTip": "..\\..\\testlib\\src\\utils\\TestLogger.cs", "ViewState": "AgIAALcAAAAAAAAAAAD4v7oAAAAXAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-27T05:35:46.981Z"}, {"$type": "Document", "DocumentIndex": 15, "Title": "RpcTestStepInputFactory.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\rpc\\RpcTestStepInputFactory.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\interop\\rpc\\RpcTestStepInputFactory.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\rpc\\RpcTestStepInputFactory.cs", "RelativeToolTip": "..\\..\\testlib\\src\\interop\\rpc\\RpcTestStepInputFactory.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABYAAAAkAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-10-06T04:29:16.022Z"}, {"$type": "Document", "DocumentIndex": 17, "Title": "RunnerOptions.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\src\\RunnerOptions.cs", "RelativeDocumentMoniker": "..\\src\\RunnerOptions.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\src\\RunnerOptions.cs", "RelativeToolTip": "..\\src\\RunnerOptions.cs", "ViewState": "AgIAACIAAAAAAAAAAADwvycAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-26T06:22:17.037Z"}, {"$type": "Document", "DocumentIndex": 22, "Title": "Program.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\src\\Program.cs", "RelativeDocumentMoniker": "..\\src\\Program.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\src\\Program.cs", "RelativeToolTip": "..\\src\\Program.cs", "ViewState": "AgIAABEAAAAAAAAAAAAtwCEAAAA6AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-26T06:22:22.811Z"}, {"$type": "Document", "DocumentIndex": 20, "Title": "Test.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\Test.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\Test.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\Test.cs", "RelativeToolTip": "..\\..\\testlib\\src\\Test.cs", "ViewState": "AgIAAHkAAAAAAAAAAAAWwIwAAAAlAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-26T16:42:15.751Z"}, {"$type": "Document", "DocumentIndex": 18, "Title": "TestSuite.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\TestSuite.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\TestSuite.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\TestSuite.cs", "RelativeToolTip": "..\\..\\testlib\\src\\TestSuite.cs", "ViewState": "AgIAAEIBAAAAAAAAAAAmwFABAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-26T08:45:04.77Z"}, {"$type": "Document", "DocumentIndex": 19, "Title": "FileLogger.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\utils\\FileLogger.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\utils\\FileLogger.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\utils\\FileLogger.cs", "RelativeToolTip": "..\\..\\testlib\\src\\utils\\FileLogger.cs", "ViewState": "AgIAAAYAAAAAAAAAAAAAABQAAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-27T05:59:28.717Z"}, {"$type": "Document", "DocumentIndex": 23, "Title": "TestConsoleWriter.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\utils\\TestConsoleWriter.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\utils\\TestConsoleWriter.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\utils\\TestConsoleWriter.cs", "RelativeToolTip": "..\\..\\testlib\\src\\utils\\TestConsoleWriter.cs", "ViewState": "AgIAAC0AAAAAAAAAAAD4vzwAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-27T06:14:46.349Z"}, {"$type": "Document", "DocumentIndex": 25, "Title": "TestFactory.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\TestFactory.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\TestFactory.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\TestFactory.cs", "RelativeToolTip": "..\\..\\testlib\\src\\TestFactory.cs", "ViewState": "AgIAACsAAAAAAAAAAAAhwCIAAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-26T16:32:00.76Z"}, {"$type": "Document", "DocumentIndex": 31, "Title": "TestData.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\context\\TestData.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\context\\TestData.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\context\\TestData.cs", "RelativeToolTip": "..\\..\\testlib\\src\\context\\TestData.cs", "ViewState": "AgIAAEYAAAAAAAAAAAAMwFcAAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-10T06:34:53.118Z"}, {"$type": "Document", "DocumentIndex": 32, "Title": "TableManager.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\context\\TableManager.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\context\\TableManager.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\context\\TableManager.cs", "RelativeToolTip": "..\\..\\testlib\\src\\context\\TableManager.cs", "ViewState": "AgIAADYAAAAAAAAAAAAswFMAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-10T06:12:50.634Z"}, {"$type": "Document", "DocumentIndex": 33, "Title": "TestContext.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\context\\TestContext.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\context\\TestContext.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\context\\TestContext.cs", "RelativeToolTip": "..\\..\\testlib\\src\\context\\TestContext.cs", "ViewState": "AgIAADcAAAAAAAAAAAA7wEkAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-09T10:35:43.559Z"}, {"$type": "Document", "DocumentIndex": 34, "Title": "NotEqualExpression.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\NotEqualExpression.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\expressions\\NotEqualExpression.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\NotEqualExpression.cs", "RelativeToolTip": "..\\..\\testlib\\src\\expressions\\NotEqualExpression.cs", "ViewState": "AgIAAA4AAAAAAAAAAAAmwB8AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-19T07:33:25.858Z"}, {"$type": "Document", "DocumentIndex": 35, "Title": "JPathResolver.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\context\\JPathResolver.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\context\\JPathResolver.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\context\\JPathResolver.cs", "RelativeToolTip": "..\\..\\testlib\\src\\context\\JPathResolver.cs", "ViewState": "AgIAACUAAAAAAAAAAAAswDgAAAB5AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-20T09:02:47.514Z"}, {"$type": "Document", "DocumentIndex": 36, "Title": "LessExpression.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\LessExpression.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\expressions\\LessExpression.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\LessExpression.cs", "RelativeToolTip": "..\\..\\testlib\\src\\expressions\\LessExpression.cs", "ViewState": "AgIAABEAAAAAAAAAAAAQwDAAAAByAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-10T06:49:02.639Z"}, {"$type": "Document", "DocumentIndex": 37, "Title": "VariableManager.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\context\\VariableManager.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\context\\VariableManager.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\context\\VariableManager.cs", "RelativeToolTip": "..\\..\\testlib\\src\\context\\VariableManager.cs", "ViewState": "AgIAAFAAAAAAAAAAAAAkwGEAAABwAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-10T06:40:11.652Z"}, {"$type": "Document", "DocumentIndex": 38, "Title": "LessOrEqualExpression.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\LessOrEqualExpression.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\expressions\\LessOrEqualExpression.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\LessOrEqualExpression.cs", "RelativeToolTip": "..\\..\\testlib\\src\\expressions\\LessOrEqualExpression.cs", "ViewState": "AgIAAAYAAAAAAAAAAAAqwCEAAAB5AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-06T14:30:43.763Z"}, {"$type": "Document", "DocumentIndex": 39, "Title": "GreaterOrEqualExpression.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\GreaterOrEqualExpression.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\expressions\\GreaterOrEqualExpression.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\GreaterOrEqualExpression.cs", "RelativeToolTip": "..\\..\\testlib\\src\\expressions\\GreaterOrEqualExpression.cs", "ViewState": "AgIAAAYAAAAAAAAAAAAqwB4AAAB8AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-06T09:35:20.84Z"}, {"$type": "Document", "DocumentIndex": 40, "Title": "JsonPathExpression.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\JsonPathExpression.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\expressions\\JsonPathExpression.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\JsonPathExpression.cs", "RelativeToolTip": "..\\..\\testlib\\src\\expressions\\JsonPathExpression.cs", "ViewState": "AgIAABMAAAAAAAAAAIA9wBsAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-06T09:40:35.622Z"}, {"$type": "Document", "DocumentIndex": 41, "Title": "GreaterExpression.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\GreaterExpression.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\expressions\\GreaterExpression.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\GreaterExpression.cs", "RelativeToolTip": "..\\..\\testlib\\src\\expressions\\GreaterExpression.cs", "ViewState": "AgIAAAwAAAAAAAAAAAAqwCIAAAB1AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-10T06:48:43.792Z"}, {"$type": "Document", "DocumentIndex": 42, "Title": "IExpression.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\IExpression.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\expressions\\IExpression.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\IExpression.cs", "RelativeToolTip": "..\\..\\testlib\\src\\expressions\\IExpression.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-06T09:23:35.342Z"}, {"$type": "Document", "DocumentIndex": 43, "Title": "BinaryExpression.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\BinaryExpression.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\expressions\\BinaryExpression.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\BinaryExpression.cs", "RelativeToolTip": "..\\..\\testlib\\src\\expressions\\BinaryExpression.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAMwBQAAAAVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-10T06:32:02.207Z"}, {"$type": "Document", "DocumentIndex": 44, "Title": "ConstantExpression.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\ConstantExpression.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\expressions\\ConstantExpression.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\ConstantExpression.cs", "RelativeToolTip": "..\\..\\testlib\\src\\expressions\\ConstantExpression.cs", "ViewState": "AgIAAC4AAAAAAAAAAAAgwC8AAAA6AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-20T06:18:16.121Z"}, {"$type": "Document", "DocumentIndex": 45, "Title": "EqualExpression.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\EqualExpression.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\expressions\\EqualExpression.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\EqualExpression.cs", "RelativeToolTip": "..\\..\\testlib\\src\\expressions\\EqualExpression.cs", "ViewState": "AgIAADwAAAAAAAAAAAAowE4AAABQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-20T08:48:44.553Z"}, {"$type": "Document", "DocumentIndex": 46, "Title": "TestUtils.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\utils\\TestUtils.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\utils\\TestUtils.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\utils\\TestUtils.cs", "RelativeToolTip": "..\\..\\testlib\\src\\utils\\TestUtils.cs", "ViewState": "AgIAAI0AAAAAAAAAAIAwwJ4AAAAZAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-31T05:36:30.032Z"}, {"$type": "Document", "DocumentIndex": 47, "Title": "ContainExpression.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\ContainExpression.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\expressions\\ContainExpression.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\ContainExpression.cs", "RelativeToolTip": "..\\..\\testlib\\src\\expressions\\ContainExpression.cs", "ViewState": "AgIAAB8AAAAAAAAAAAAmwD4AAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-20T09:41:59.545Z"}, {"$type": "Document", "DocumentIndex": 48, "Title": "NotContainExpression.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\NotContainExpression.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\expressions\\NotContainExpression.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\NotContainExpression.cs", "RelativeToolTip": "..\\..\\testlib\\src\\expressions\\NotContainExpression.cs", "ViewState": "AgIAACMAAAAAAAAAAAAMwDoAAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-20T07:41:15.84Z"}, {"$type": "Document", "DocumentIndex": 49, "Title": "JTokenComparer.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\utils\\JTokenComparer.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\utils\\JTokenComparer.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\utils\\JTokenComparer.cs", "RelativeToolTip": "..\\..\\testlib\\src\\utils\\JTokenComparer.cs", "ViewState": "AgIAAG8AAAAAAAAAAAAIwIoAAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-20T08:49:09.209Z"}, {"$type": "Document", "DocumentIndex": 50, "Title": "CsScriptExpression.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\CsScriptExpression.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\expressions\\CsScriptExpression.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\CsScriptExpression.cs", "RelativeToolTip": "..\\..\\testlib\\src\\expressions\\CsScriptExpression.cs", "ViewState": "AgIAADgAAAAAAAAAAAAwwEsAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-06T09:45:23.367Z"}, {"$type": "Document", "DocumentIndex": 51, "Title": "RpcTestStepInputTemplate.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\rpc\\RpcTestStepInputTemplate.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\interop\\rpc\\RpcTestStepInputTemplate.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\rpc\\RpcTestStepInputTemplate.cs", "RelativeToolTip": "..\\..\\testlib\\src\\interop\\rpc\\RpcTestStepInputTemplate.cs", "ViewState": "AgIAAAcAAAAAAAAAAAApwBcAAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-10-09T05:51:33.913Z"}, {"$type": "Document", "DocumentIndex": 52, "Title": "JTokenExtensions.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\utils\\JTokenExtensions.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\utils\\JTokenExtensions.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\utils\\JTokenExtensions.cs", "RelativeToolTip": "..\\..\\testlib\\src\\utils\\JTokenExtensions.cs", "ViewState": "AgIAABEAAAAAAAAAAAAQwAkAAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-20T09:14:37.249Z"}, {"$type": "Document", "DocumentIndex": 53, "Title": "TableFileLoader.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\context\\TableFileLoader.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\context\\TableFileLoader.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\context\\TableFileLoader.cs", "RelativeToolTip": "..\\..\\testlib\\src\\context\\TableFileLoader.cs", "ViewState": "AgIAADwAAAAAAAAAAAAowEkAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-11T03:39:51.362Z"}, {"$type": "Document", "DocumentIndex": 54, "Title": "TestStepOutputSettings.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\TestStepOutputSettings.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\TestStepOutputSettings.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\TestStepOutputSettings.cs", "RelativeToolTip": "..\\..\\testlib\\src\\TestStepOutputSettings.cs", "ViewState": "AgIAAAwAAAAAAAAAAAAMwB0AAAAqAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-28T04:59:00.061Z"}, {"$type": "Document", "DocumentIndex": 55, "Title": "JsonSchemaExpression.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\JsonSchemaExpression.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\expressions\\JsonSchemaExpression.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\JsonSchemaExpression.cs", "RelativeToolTip": "..\\..\\testlib\\src\\expressions\\JsonSchemaExpression.cs", "ViewState": "AgIAAGsAAAAAAAAAAAAwwHAAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-06T09:41:32.196Z"}, {"$type": "Document", "DocumentIndex": 56, "Title": "TestSuiteConfig.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\TestSuiteConfig.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\TestSuiteConfig.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\TestSuiteConfig.cs", "RelativeToolTip": "..\\..\\testlib\\src\\TestSuiteConfig.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-26T16:30:19.217Z"}, {"$type": "Document", "DocumentIndex": 57, "Title": "CmdTestStepInputFactory.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\cmd\\CmdTestStepInputFactory.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\cmd\\CmdTestStepInputFactory.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\cmd\\CmdTestStepInputFactory.cs", "RelativeToolTip": "..\\..\\testlib\\src\\cmd\\CmdTestStepInputFactory.cs", "ViewState": "AgIAAAMAAAAAAAAAAIAwwBQAAACYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-28T02:31:54.923Z"}, {"$type": "Document", "DocumentIndex": 58, "Title": "MBusTestStepOutput.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\mbus\\MBusTestStepOutput.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\interop\\mbus\\MBusTestStepOutput.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\mbus\\MBusTestStepOutput.cs", "RelativeToolTip": "..\\..\\testlib\\src\\interop\\mbus\\MBusTestStepOutput.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAqwAkAAAAfAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-25T06:23:53.858Z"}, {"$type": "Document", "DocumentIndex": 59, "Title": "MBusTestStepInputTemplate.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\mbus\\MBusTestStepInputTemplate.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\interop\\mbus\\MBusTestStepInputTemplate.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\mbus\\MBusTestStepInputTemplate.cs", "RelativeToolTip": "..\\..\\testlib\\src\\interop\\mbus\\MBusTestStepInputTemplate.cs", "ViewState": "AgIAABoAAAAAAAAAAAAwwAwAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-10-08T06:51:12.532Z"}, {"$type": "Document", "DocumentIndex": 60, "Title": "MBusTestStepInputFactory.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\mbus\\MBusTestStepInputFactory.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\interop\\mbus\\MBusTestStepInputFactory.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\mbus\\MBusTestStepInputFactory.cs", "RelativeToolTip": "..\\..\\testlib\\src\\interop\\mbus\\MBusTestStepInputFactory.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAAZAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-15T07:46:13.142Z"}, {"$type": "Document", "DocumentIndex": 61, "Title": "MBusTestStepExecutor.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\mbus\\MBusTestStepExecutor.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\interop\\mbus\\MBusTestStepExecutor.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\mbus\\MBusTestStepExecutor.cs", "RelativeToolTip": "..\\..\\testlib\\src\\interop\\mbus\\MBusTestStepExecutor.cs", "ViewState": "AgIAAAYAAAAAAAAAAIBJwBoAAAA2AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-25T06:29:21.65Z"}, {"$type": "Document", "DocumentIndex": 27, "Title": "RpcClient.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\comm\\csharp\\mercury-csharp\\src\\RpcClient.cs", "RelativeDocumentMoniker": "..\\..\\..\\..\\comm\\csharp\\mercury-csharp\\src\\RpcClient.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\comm\\csharp\\mercury-csharp\\src\\RpcClient.cs", "RelativeToolTip": "..\\..\\..\\..\\comm\\csharp\\mercury-csharp\\src\\RpcClient.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-31T05:42:19.905Z"}, {"$type": "Document", "DocumentIndex": 62, "Title": "IAsserter.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\assertions\\IAsserter.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\assertions\\IAsserter.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\assertions\\IAsserter.cs", "RelativeToolTip": "..\\..\\testlib\\src\\assertions\\IAsserter.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-10-10T09:55:30.101Z"}, {"$type": "Document", "DocumentIndex": 63, "Title": "AsserterFactory.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\assertions\\AsserterFactory.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\assertions\\AsserterFactory.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\assertions\\AsserterFactory.cs", "RelativeToolTip": "..\\..\\testlib\\src\\assertions\\AsserterFactory.cs", "ViewState": "AgIAAEAAAAAAAAAAAAAgwE8AAABuAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-12T09:36:55.647Z"}, {"$type": "Document", "DocumentIndex": 64, "Title": "CmdTestStepExecutor.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\cmd\\CmdTestStepExecutor.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\cmd\\CmdTestStepExecutor.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\cmd\\CmdTestStepExecutor.cs", "RelativeToolTip": "..\\..\\testlib\\src\\cmd\\CmdTestStepExecutor.cs", "ViewState": "AgIAAGYAAAAAAAAAAAAqwH8AAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-11T09:08:26.784Z"}, {"$type": "Document", "DocumentIndex": 65, "Title": "FileLoggerProvider.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\utils\\FileLoggerProvider.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\utils\\FileLoggerProvider.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\utils\\FileLoggerProvider.cs", "RelativeToolTip": "..\\..\\testlib\\src\\utils\\FileLoggerProvider.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAcAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-27T05:59:14.835Z"}, {"$type": "Document", "DocumentIndex": 66, "Title": "Assert.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\assertions\\Assert.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\assertions\\Assert.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\assertions\\Assert.cs", "RelativeToolTip": "..\\..\\testlib\\src\\assertions\\Assert.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-30T08:52:31.894Z"}, {"$type": "Document", "DocumentIndex": 67, "Title": "TestStepExecutionSettings.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\TestStepExecutionSettings.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\TestStepExecutionSettings.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\TestStepExecutionSettings.cs", "RelativeToolTip": "..\\..\\testlib\\src\\TestStepExecutionSettings.cs", "ViewState": "AgIAABkAAAAAAAAAAADwvy8AAABsAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-26T06:14:30.556Z"}, {"$type": "Document", "DocumentIndex": 68, "Title": "HttpTestStepOutputSettings.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\http\\HttpTestStepOutputSettings.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\http\\HttpTestStepOutputSettings.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\http\\HttpTestStepOutputSettings.cs", "RelativeToolTip": "..\\..\\testlib\\src\\http\\HttpTestStepOutputSettings.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAABJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-11T07:37:17.107Z"}, {"$type": "Document", "DocumentIndex": 69, "Title": "CmdTestStepInputTemplate.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\cmd\\CmdTestStepInputTemplate.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\cmd\\CmdTestStepInputTemplate.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\cmd\\CmdTestStepInputTemplate.cs", "RelativeToolTip": "..\\..\\testlib\\src\\cmd\\CmdTestStepInputTemplate.cs", "ViewState": "AgIAAAAAAAAAAAAAAIBJwAcAAAAWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-28T02:32:53.507Z"}, {"$type": "Document", "DocumentIndex": 70, "Title": "CmdTestStepInput.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\cmd\\CmdTestStepInput.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\cmd\\CmdTestStepInput.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\cmd\\CmdTestStepInput.cs", "RelativeToolTip": "..\\..\\testlib\\src\\cmd\\CmdTestStepInput.cs", "ViewState": "AgIAAAMAAAAAAAAAAIBJwAsAAAAbAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-03T06:12:27.898Z"}, {"$type": "Document", "DocumentIndex": 71, "Title": "HttpTestStepOutput.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\http\\HttpTestStepOutput.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\http\\HttpTestStepOutput.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\http\\HttpTestStepOutput.cs", "RelativeToolTip": "..\\..\\testlib\\src\\http\\HttpTestStepOutput.cs", "ViewState": "AgIAABcAAAAAAAAAAAAIwA0AAAAfAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-25T06:23:14.79Z"}, {"$type": "Document", "DocumentIndex": 72, "Title": "HttpTestStepInputFactory.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\http\\HttpTestStepInputFactory.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\http\\HttpTestStepInputFactory.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\http\\HttpTestStepInputFactory.cs", "RelativeToolTip": "..\\..\\testlib\\src\\http\\HttpTestStepInputFactory.cs", "ViewState": "AgIAACgAAAAAAAAAAAAgwDIAAABKAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-26T16:42:52.239Z"}, {"$type": "Document", "DocumentIndex": 73, "Title": "HttpTestStepInput.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\http\\HttpTestStepInput.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\http\\HttpTestStepInput.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\http\\HttpTestStepInput.cs", "RelativeToolTip": "..\\..\\testlib\\src\\http\\HttpTestStepInput.cs", "ViewState": "AgIAAAUAAAAAAAAAAAAAwDUAAAAvAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-18T14:37:02.598Z"}, {"$type": "Document", "DocumentIndex": 74, "Title": "HttpTestStepInputTemplate.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\http\\HttpTestStepInputTemplate.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\http\\HttpTestStepInputTemplate.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\http\\HttpTestStepInputTemplate.cs", "RelativeToolTip": "..\\..\\testlib\\src\\http\\HttpTestStepInputTemplate.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-12T10:28:58.2Z"}, {"$type": "Document", "DocumentIndex": 75, "Title": "MBusTestStepInput.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\mbus\\MBusTestStepInput.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\interop\\mbus\\MBusTestStepInput.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\mbus\\MBusTestStepInput.cs", "RelativeToolTip": "..\\..\\testlib\\src\\interop\\mbus\\MBusTestStepInput.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAqwBkAAAAqAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-10-08T06:49:49.64Z"}, {"$type": "Document", "DocumentIndex": 76, "Title": "CmdTestStepOutput.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\cmd\\CmdTestStepOutput.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\cmd\\CmdTestStepOutput.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\cmd\\CmdTestStepOutput.cs", "RelativeToolTip": "..\\..\\testlib\\src\\cmd\\CmdTestStepOutput.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAcAAABLAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-11T09:09:48.813Z"}, {"$type": "Document", "DocumentIndex": 77, "Title": "PyScriptExpression.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\PyScriptExpression.cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\expressions\\PyScriptExpression.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\PyScriptExpression.cs", "RelativeToolTip": "..\\..\\testlib\\src\\expressions\\PyScriptExpression.cs", "ViewState": "AgIAAB8AAAAAAAAAAAAgwCoAAAAmAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-06T08:45:19.007Z"}, {"$type": "Document", "DocumentIndex": 28, "Title": "MBusClient.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\comm\\csharp\\mercury-csharp\\src\\MBusClient.cs", "RelativeDocumentMoniker": "..\\..\\..\\..\\comm\\csharp\\mercury-csharp\\src\\MBusClient.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\comm\\csharp\\mercury-csharp\\src\\MBusClient.cs", "RelativeToolTip": "..\\..\\..\\..\\comm\\csharp\\mercury-csharp\\src\\MBusClient.cs", "ViewState": "AgIAAI4AAAAAAAAAAAAwwJwAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-10-14T16:54:07.624Z"}, {"$type": "Document", "DocumentIndex": 29, "Title": "IamService.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\api\\csharp\\generated\\IamService.cs", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\api\\csharp\\generated\\IamService.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\api\\csharp\\generated\\IamService.cs", "RelativeToolTip": "..\\..\\..\\..\\..\\api\\csharp\\generated\\IamService.cs", "ViewState": "AgIAAAUAAAAAAAAAAAAjwAUAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-10-12T04:57:10.907Z"}, {"$type": "Document", "DocumentIndex": 30, "Title": "User.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\api\\csharp\\generated\\User.cs", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\api\\csharp\\generated\\User.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\api\\csharp\\generated\\User.cs", "RelativeToolTip": "..\\..\\..\\..\\..\\api\\csharp\\generated\\User.cs", "ViewState": "AgIAAAUAAAAAAAAAAAAowAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-10-06T04:43:38.725Z"}, {"$type": "Document", "DocumentIndex": 78, "Title": "SkipEmptyCollectionsTypeInspector .cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\utils\\SkipEmptyCollectionsTypeInspector .cs", "RelativeDocumentMoniker": "..\\..\\testlib\\src\\utils\\SkipEmptyCollectionsTypeInspector .cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\utils\\SkipEmptyCollectionsTypeInspector .cs", "RelativeToolTip": "..\\..\\testlib\\src\\utils\\SkipEmptyCollectionsTypeInspector .cs", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-12T03:55:37.252Z"}, {"$type": "Document", "DocumentIndex": 26, "Title": "testlib.csproj", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\build\\testlib\\testlib.csproj", "RelativeDocumentMoniker": "testlib\\testlib.csproj", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\build\\testlib\\testlib.csproj", "RelativeToolTip": "testlib\\testlib.csproj", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2024-10-06T03:38:24.966Z"}]}]}]}