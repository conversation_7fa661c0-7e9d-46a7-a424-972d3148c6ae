syntax = "proto3";

package testdatamaker.examinationreportdatamaker;

import "alcon/interop/type/CoreTypes.proto";
import "alcon/interop/type/devices/MetiorExamination.proto";
import "alcon/interop/type/devices/ArgosExamination.proto";
import "alcon/interop/type/Ophthalmic.proto";
import "alcon/interop/type/ExaminationReport.proto";
import "alcon/interop/type/cataract/Planning.proto";
import "PatientDataMaker.proto";
import "KeratometryDataMaker.proto";
import "AxialBiometryDataMaker.proto";
import "RefractionDataMaker.proto";
import "EnfaceBiometryDataMaker.proto";
import "ProtoUtils.proto";
import "CoreTypesDataMaker.proto";

enum DeviceEnum {
    NONE = 0;
    METIOR = 1;
    ARGOS = 2;
}

message MetiorArgs {
    alcon.interop.type.devices.MetiorExamination metiorExaminationSeed = 1;
}

message ArgosArgs {
    alcon.interop.type.devices.ArgosExamination argosExaminationSeed = 1;
}

message KeratometryArgs {
    alcon.interop.type.Keratometry keratometrySeed = 1;
}

message EyeContextArgs {
    alcon.interop.type.cataract.EyeContext eyeContextSeed = 1;
}

message ExaminationReportDataMakerParams {
    optional protoutils.RevisionArgs revisionArgs = 1;
    optional string description = 2;
    optional protoutils.NotesArgs notesArgs = 3;
    optional patientdatamaker.PatientConfig patientConfig = 4;
    optional EyeContextArgs eyeContextArgs = 5;
    optional keratometrydatamaker.KeratometryConfig anteriorKeratometryConfig = 6;
    optional keratometrydatamaker.KeratometryConfig posteriorKeratometryConfig = 7;
    optional keratometrydatamaker.KeratometryConfig totalKeratometryConfig = 8;
    optional keratometrydatamaker.KeratometryConfig alternativeAnteriorKeratometryConfig = 9;
    optional axialbiometrydatamaker.AxialBiometryConfig axialBiometryConfig = 10;
    optional enfacebiometrydatamaker.EnfaceBiometryConfig enfaceBiometryConfig = 11;
    optional alcon.interop.type.ReferenceImage referenceImage = 12;
    optional refractiondatamaker.RefractionConfig  refractionConfig = 13;
    optional alcon.interop.type.FileResource pdfDocument = 14;
    optional ArgosArgs argosArgs = 15;
    optional MetiorArgs metiorArgs = 16;
    DeviceEnum device = 17;
}

message ExaminationReportDataMakerConfig {
  coretypes.GeneratingPolicy policy = 1;
  optional bool exists = 2;
  ExaminationReportDataMakerParams params = 3;
  alcon.interop.type.ExaminationReport seed = 4;
}
