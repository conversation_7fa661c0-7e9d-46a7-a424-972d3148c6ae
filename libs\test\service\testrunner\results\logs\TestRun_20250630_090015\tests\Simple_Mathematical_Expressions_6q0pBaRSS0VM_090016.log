2025-06-30T09:00:16.9171916+05:30 [Information] [----------] 6 steps from Simple Mathematical Expressions Demo
2025-06-30T09:00:16.9185358+05:30 [Information] Set Value to Step 'e42f3321-c16c-4eaa-a5e2-4d0bd09902e8' name:
"Fast Baseline Operation"
2025-06-30T09:00:16.9186967+05:30 [Information] [ RUN      ] Simple Mathematical Expressions Demo > Fast Baseline Operation
2025-06-30T09:00:16.9191174+05:30 [Information] Executing single instance of step 'Fast Baseline Operation'.
2025-06-30T09:00:16.9192983+05:30 [Information] Executing step 'Fast Baseline Operation' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "e42f3321-c16c-4eaa-a5e2-4d0bd09902e8",
        "tables": {},
        "variables": {},
        "name": "Fast Baseline Operation"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaselineThreshold": 100,
      "MaxRegressionPercent": 150,
      "BufferTime": 50,
      "FastThreshold": 200,
      "SlowThreshold": 1000
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:00:16.9203874+05:30 [Information] Set Value to Step 'e42f3321-c16c-4eaa-a5e2-4d0bd09902e8' input:
{
  "command": "ping",
  "arguments": [
    "127.0.0.1",
    "-n",
    "1"
  ],
  "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
  "printOutput": false
}
2025-06-30T09:00:16.9205397+05:30 [Information] Delaying step execution for 50ms
2025-06-30T09:00:17.0108130+05:30 [Information] Set Value to Step 'e42f3321-c16c-4eaa-a5e2-4d0bd09902e8' output:
{
  "stdout": "Pinging 127.0.0.1 with 32 bytes of data:\r\nReply from 127.0.0.1: bytes=32 time<1ms TTL=128\r\nPing statistics for 127.0.0.1:\r\n    Packets: Sent = 1, Received = 1, Lost = 0 (0% loss),\r\nApproximate round trip times in milli-seconds:\r\n    Minimum = 0ms, Maximum = 0ms, Average = 0ms",
  "stderr": "",
  "exitCode": 0
}
2025-06-30T09:00:17.0110961+05:30 [Information] Resolving path 'performance.executionTimeMs' using global context.
2025-06-30T09:00:17.0113279+05:30 [Information] Resolved JPath 'performance.executionTimeMs' to ''
2025-06-30T09:00:17.0114758+05:30 [Information] Set Value to Step 'e42f3321-c16c-4eaa-a5e2-4d0bd09902e8' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T03:30:16.9191136Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "cmd"
}
2025-06-30T09:00:17.0123108+05:30 [Information] Run Asserter : {"Expression1":{"Value":"0","ErrorMesage":"Ping command failed"},"Expression2":{"JPath":"$curStep:$.output.exitCode","ErrorMesage":"Ping command failed"},"ErrorMesage":"Ping command failed"}
2025-06-30T09:00:17.0124803+05:30 [Information] Resolving path '$.output.exitCode' using $curStep:
2025-06-30T09:00:17.0125852+05:30 [Information] Resolved JPath '$curStep:$.output.exitCode' to '0'
2025-06-30T09:00:17.0133435+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$var:FastThreshold}}","ErrorMesage":"Baseline too slow: {{$curStep:performance.executionTimeMs}}ms > {{$var:FastThreshold}}ms"},"Expression2":{"JPath":"$curStep:performance.executionTimeMs","ErrorMesage":"Baseline too slow: {{$curStep:performance.executionTimeMs}}ms > {{$var:FastThreshold}}ms"},"ErrorMesage":"Baseline too slow: {{$curStep:performance.executionTimeMs}}ms > {{$var:FastThreshold}}ms"}
2025-06-30T09:00:17.0135129+05:30 [Information] Processing $var: path 'FastThreshold'
2025-06-30T09:00:17.0136167+05:30 [Information] Checking if 'FastThreshold' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:00:17.0136885+05:30 [Information] Resolving path 'FastThreshold' using global variables.
2025-06-30T09:00:17.0137516+05:30 [Information] Resolved JPath '$var:FastThreshold' to '200'
2025-06-30T09:00:17.0138017+05:30 [Information] Resolved template pattern '{{$var:FastThreshold}}' to '200' in '{{$var:FastThreshold}}'
2025-06-30T09:00:17.0138743+05:30 [Information] Resolved template '{{$var:FastThreshold}}' to '200'
2025-06-30T09:00:17.0139377+05:30 [Information] Resolving path 'performance.executionTimeMs' using $curStep:
2025-06-30T09:00:17.0140045+05:30 [Information] Resolved JPath '$curStep:performance.executionTimeMs' to '0'
2025-06-30T09:00:17.0142874+05:30 [Error] Asserter error : {"Expression1":{"Value":"{{$var:FastThreshold}}","ErrorMesage":"Baseline too slow: {{$curStep:performance.executionTimeMs}}ms > {{$var:FastThreshold}}ms"},"Expression2":{"JPath":"$curStep:performance.executionTimeMs","ErrorMesage":"Baseline too slow: {{$curStep:performance.executionTimeMs}}ms > {{$var:FastThreshold}}ms"},"ErrorMesage":"Baseline too slow: {{$curStep:performance.executionTimeMs}}ms > {{$var:FastThreshold}}ms"}
2025-06-30T09:00:17.0149085+05:30 [Error] Assert Failure : Less(200, 0)
2025-06-30T09:00:17.0151726+05:30 [Information] Failed to execute step 'Fast Baseline Operation': Baseline too slow: {{$curStep:performance.executionTimeMs}}ms > {{$var:FastThreshold}}ms
2025-06-30T09:00:17.0152858+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:00:17.0153595+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:00:17.0154131+05:30 [Information] No performance data found for step 'Fast Baseline Operation' (cmd)
2025-06-30T09:00:17.0156041+05:30 [Error] Baseline too slow: {{$curStep:performance.executionTimeMs}}ms > {{$var:FastThreshold}}ms
2025-06-30T09:00:17.0157887+05:30 [Error] [ FAILED   ]
2025-06-30T09:00:17.0161801+05:30 [Information] [----------] 6 steps from Simple Mathematical Expressions Demo (98 ms total)
