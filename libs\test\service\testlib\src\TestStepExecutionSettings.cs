/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using TestLib.Assertions;

namespace TestLib;

public class TestStepExecutionSettings
{
    public int MaxRetries { get; set; } = 1;
    public int RetryDelayMs { get; set; } = 0;
    public int DelayMs { get; set; } = 0;
    public IRetryCondition RetryUntil { get; set; } = null;
    public int? RequestTimeoutMs { get; set; }
    public TestStepExecutionSettings(JToken executionSettings = null)
    {
        if (executionSettings != null)
        {
            var maxRetriesToken = executionSettings["MaxRetries"];
            if (maxRetriesToken != null && int.TryParse(maxRetriesToken.ToString(), out int maxRetriesVal))
            {
                MaxRetries = maxRetriesVal;
            }

            var retryDelayMsToken = executionSettings["RetryDelayMs"];
            if (retryDelayMsToken != null && int.TryParse(retryDelayMsToken.ToString(), out int retryDelayVal))
            {
                RetryDelayMs = retryDelayVal;
            }

            var delayMsToken = executionSettings["DelayMs"];
            if (delayMsToken != null && int.TryParse(delayMsToken.ToString(), out int delayVal))
            {
                DelayMs = delayVal;
            }

            var retryUntilToken = executionSettings["RetryUntil"];
            if (retryUntilToken != null && retryUntilToken.Type == JTokenType.Array)
            {
                List<Asserter> asserters = ((JArray)retryUntilToken)
                        .Select(AsserterFactory.Create)
                        .ToList();

                RetryUntil = new AsserterBasedRetryCondition(asserters);
            }

            var requestTimeoutMs = executionSettings["RequestTimeoutMs"];
            if (requestTimeoutMs != null && int.TryParse(requestTimeoutMs.ToString(), out int timeoutMsVal))
            {
                RequestTimeoutMs = timeoutMsVal;
            }

            // Validation: If MaxRetries > 1, RetryUntil and RetryDelayMs must exist
            if (MaxRetries > 1 && (RetryUntil == null || RetryDelayMs <= 0))
            {
                throw new ArgumentException("RetryUntil and RetryDelayMs must be specified when MaxRetries is greater than 1.");
            }
        }
    }
    public TimeSpan GetRequestTimeoutOrDefault(TimeSpan defaultTimeout)
    {
        if (RequestTimeoutMs.HasValue && RequestTimeoutMs.Value > 0)
        {
            return TimeSpan.FromMilliseconds(RequestTimeoutMs.Value);
        }
        return defaultTimeout;
    }
}
