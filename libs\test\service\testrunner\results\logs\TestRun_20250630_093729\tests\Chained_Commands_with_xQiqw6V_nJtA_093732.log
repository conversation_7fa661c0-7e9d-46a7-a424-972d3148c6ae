2025-06-30T09:37:32.4711382+05:30 [Information] [----------] 1 step from Chained Commands with Piping
2025-06-30T09:37:32.4715357+05:30 [Information] Set Value to Step 'b74b194a-0bfd-4892-b8e0-f184f5e11c96' name:
"Step pipe"
2025-06-30T09:37:32.4716286+05:30 [Information] Set Value to Step 'b74b194a-0bfd-4892-b8e0-f184f5e11c96' description:
"Echo and filter output using findstr"
2025-06-30T09:37:32.4716872+05:30 [Information] [ RUN      ] Chained Commands with Piping > Step pipe
2025-06-30T09:37:32.4719730+05:30 [Information] Executing single instance of step 'Step pipe'.
2025-06-30T09:37:32.4722035+05:30 [Information] Executing step 'Step pipe' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "b74b194a-0bfd-4892-b8e0-f184f5e11c96",
        "tables": {},
        "variables": {},
        "name": "Step pipe",
        "description": "Echo and filter output using findstr"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:37:32.4755766+05:30 [Information] Set Value to Step 'b74b194a-0bfd-4892-b8e0-f184f5e11c96' input:
{
  "command": "cmd.exe",
  "arguments": [
    "/c",
    "echo Hello | findstr Hello"
  ],
  "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
  "printOutput": false
}
2025-06-30T09:37:32.5095946+05:30 [Information] Set Value to Step 'b74b194a-0bfd-4892-b8e0-f184f5e11c96' output:
{
  "stdout": "Hello",
  "stderr": "",
  "exitCode": 0
}
2025-06-30T09:37:32.5097840+05:30 [Information] Set Value to Step 'b74b194a-0bfd-4892-b8e0-f184f5e11c96' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:07:32.4719692Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "cmd"
}
2025-06-30T09:37:32.5099006+05:30 [Information] Run Asserter : {"Expression1":{"Value":"Hello","ErrorMesage":null},"Expression2":{"JPath":"$curStep:$.output.stdout","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:37:32.5100456+05:30 [Information] Resolving path '$.output.stdout' using $curStep:
2025-06-30T09:37:32.5101269+05:30 [Information] Resolved JPath '$curStep:$.output.stdout' to 'Hello'
2025-06-30T09:37:32.5102112+05:30 [Information] Set Value to Step 'b74b194a-0bfd-4892-b8e0-f184f5e11c96' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 34,
  "startTime": "2025-06-30T04:07:32.4719692Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "cmd"
}
2025-06-30T09:37:32.5102993+05:30 [Information] Step 'Step pipe' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "b74b194a-0bfd-4892-b8e0-f184f5e11c96",
        "tables": {},
        "variables": {},
        "name": "Step pipe",
        "description": "Echo and filter output using findstr",
        "input": {
          "command": "cmd.exe",
          "arguments": [
            "/c",
            "echo Hello | findstr Hello"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Hello",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 34,
          "startTime": "2025-06-30T04:07:32.4719692Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:37:32.5104034+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:37:32.5104602+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:37:32.5105049+05:30 [Information] No performance data found for step 'Step pipe' (cmd)
2025-06-30T09:37:32.5105509+05:30 [Information] [ OK       ]
2025-06-30T09:37:32.5108047+05:30 [Information] [----------] 1 step from Chained Commands with Piping (39 ms total)
