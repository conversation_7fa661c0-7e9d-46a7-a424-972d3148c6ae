/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

#include "SubjectiveExaminationReportDataMaker.h"

namespace testdatamaker {
// -------------------------------------------------------------------------------------------------

std::optional<type::SubjectiveExaminationReport> 
SubjectiveExaminationReportDataMaker::make(const serdmp::SubjectiveExaminationReportConfig& config) {
    return make(ProtoUtils::fromProtoConfig(config));
}

// -------------------------------------------------------------------------------------------------

std::optional<type::SubjectiveExaminationReport>
SubjectiveExaminationReportDataMaker::make(const subjectiveexaminationreportdatamaker_config_t& config) {
    if (!config.exists) {
        return std::nullopt;
    }

    switch (config.policy) {
        case coretypesdm::GeneratingPolicy::RANDOM:
            return makeRandom(config.params);
        case coretypesdm::GeneratingPolicy::BASED_ON:
            return makeBasedOn(config.seed, config.params);
        case coretypesdm::GeneratingPolicy::PARTIAL:
            return makeBasedOn(std::nullopt, config.params);
        default:
            return std::nullopt;
    }
}

// -------------------------------------------------------------------------------------------------

std::vector<type::SubjectiveExaminationReport>
SubjectiveExaminationReportDataMaker::extractSubjectiveExaminationReportFromDb(const mpdb::InMultiProtobufDb& data) {
    return ProtoUtils::extractMessagesFromDb<type::SubjectiveExaminationReport>(data);
}

// -------------------------------------------------------------------------------------------------

type::SubjectiveExaminationReport 
SubjectiveExaminationReportDataMaker::makeRandom(const serdmp::SubjectiveExaminationReportParams& params) {
    type::SubjectiveExaminationReport report;

    if (params.has_revisionargs()) {
        *report.mutable_revision() = ProtoUtils::makeRevision(params.revisionargs());
    } else {
        *report.mutable_revision() = ProtoUtils::makeRevision(std::nullopt);
    }

    if (params.has_description()) {
        report.set_description(params.description());
    } else {
        report.set_description(RandomUtils::makeRandomString(30));
    }

    if (params.has_notesargs()) {
        ctu::assign(report.mutable_notes(), ProtoUtils::makeNotes(params.notesargs()));
    } else {
        ctu::assign(report.mutable_notes(), ProtoUtils::makeNotes(std::nullopt));
    }

    if (params.has_patientargs()) {
        *report.mutable_patientref() = pddm::makeRandomPatientValueRef(params.patientargs());
    } else {
        *report.mutable_patientref() = pddm::makeRandomPatientValueRef(patientdmp::PatientParams{});
    }

    if (params.has_refractionargs()) {
        ctu::assign(report.mutable_refractions(),
                    makeRefractions(params.refractionargs().count(), (params.refractionargs().has_refractionconfig()
                                                                          ? params.refractionargs().refractionconfig()
                                                                          : rdmp::RefractionConfig{})));
    } else {
        ctu::assign(report.mutable_refractions(),
                    makeRefractions(RandomUtils::makeRandomInt(1, 3), rdmp::RefractionConfig{}));
    }

    if (params.has_bcvaargs()) {
        ctu::assign(report.mutable_bcva(),
                    vadm::makeManyRandom(params.bcvaargs().count(),
                                         (params.bcvaargs().has_bcvaparams() ? params.bcvaargs().bcvaparams()
                                                                             : vadmp::VisualAcuityParams{})));
    } else {
        ctu::assign(report.mutable_bcva(),
                    vadm::makeManyRandom(RandomUtils::makeRandomInt(1, 3), vadmp::VisualAcuityParams{}));
    }

    if (params.has_ucvaargs()) {
        ctu::assign(report.mutable_ucva(),
                    vadm::makeManyRandom(params.ucvaargs().count(), params.ucvaargs().has_ucvaparams()
                                                                        ? params.ucvaargs().ucvaparams()
                                                                        : vadmp::VisualAcuityParams{}));
    } else {
        ctu::assign(report.mutable_ucva(),
                    vadm::makeManyRandom(RandomUtils::makeRandomInt(1, 3), vadmp::VisualAcuityParams{}));
    }

    if (params.has_visualacuityadditionalassesmentargs()) {
        ctu::assign(report.mutable_visualacuityadditionalassessment(),
                    makeVisualAcuitiesAdditionalAssessments(
                        params.visualacuityadditionalassesmentargs().count(),
                        (params.visualacuityadditionalassesmentargs().has_visualacuityparams())
                            ? params.visualacuityadditionalassesmentargs().visualacuityparams()
                            : vadmp::VisualAcuityParams{}));
    } else {
        ctu::assign(
            report.mutable_visualacuityadditionalassessment(),
            makeVisualAcuitiesAdditionalAssessments(RandomUtils::makeRandomInt(1, 3), vadmp::VisualAcuityParams{}));
    }

    return report;
}

// -------------------------------------------------------------------------------------------------

type::SubjectiveExaminationReport 
SubjectiveExaminationReportDataMaker::makeBasedOn(
    const std::optional<type::SubjectiveExaminationReport>& seed,
    const serdmp::SubjectiveExaminationReportParams& params) {
    type::SubjectiveExaminationReport report = seed.value_or(type::SubjectiveExaminationReport{});

    if (params.has_revisionargs()) {
        *report.mutable_revision() = ProtoUtils::makeRevision(params.revisionargs());
    }

    if (params.has_description()) {
        report.set_description(params.description());
    }

    if (params.has_notesargs()) {
        ctu::assign(report.mutable_notes(), ProtoUtils::makeNotes(params.notesargs()));
    }

    if (params.has_patientargs()) {
        *report.mutable_patientref() = pddm::makeRandomPatientValueRef(params.patientargs());
    }

    if (params.has_refractionargs()) {
        ctu::assign(report.mutable_refractions(),
                    makeRefractions(params.refractionargs().count(), params.refractionargs().has_refractionconfig()
                                                                         ? params.refractionargs().refractionconfig()
                                                                         : rdmp::RefractionConfig{}));
    }

    if (params.has_bcvaargs()) {
        ctu::assign(report.mutable_bcva(),
                    vadm::makeManyRandom(params.bcvaargs().count(), params.bcvaargs().has_bcvaparams()
                                                                        ? params.bcvaargs().bcvaparams()
                                                                        : vadmp::VisualAcuityParams{}));
    }

    if (params.has_ucvaargs()) {
        ctu::assign(report.mutable_ucva(),
                    vadm::makeManyRandom(params.ucvaargs().count(), params.ucvaargs().has_ucvaparams()
                                                                        ? params.ucvaargs().ucvaparams()
                                                                        : vadmp::VisualAcuityParams{}));
    }

    if (params.has_visualacuityadditionalassesmentargs()) {
        ctu::assign(report.mutable_visualacuityadditionalassessment(),
                    makeVisualAcuitiesAdditionalAssessments(
                        params.visualacuityadditionalassesmentargs().count(),
                        params.visualacuityadditionalassesmentargs().has_visualacuityparams()
                            ? params.visualacuityadditionalassesmentargs().visualacuityparams()
                            : vadmp::VisualAcuityParams{}));
    }

    return report;
}

// -------------------------------------------------------------------------------------------------

std::vector<type::VisualAcuityAdditionalAssessment>
SubjectiveExaminationReportDataMaker::makeVisualAcuitiesAdditionalAssessments(const int& count, const vadmp::VisualAcuityParams& visualAcuityArgs) {
    std::vector<type::VisualAcuityAdditionalAssessment> visualAcuitiesAdditionalAssessments;
    for (int i = 0; i < count; i++) {
        type::VisualAcuityAdditionalAssessment visualAcuitiesAdditionalAssessment;
        visualAcuitiesAdditionalAssessment.set_type(static_cast<type::VisualAcuityAdditionalAssessment::AssessmentType>(
            RandomUtils::makeRandomInt(type::VisualAcuityAdditionalAssessment::AssessmentType_MIN,
                                       type::VisualAcuityAdditionalAssessment::AssessmentType_MAX)));
        *visualAcuitiesAdditionalAssessment.mutable_visualacuity() = vadm::makeRandom(visualAcuityArgs);
        visualAcuitiesAdditionalAssessments.push_back(visualAcuitiesAdditionalAssessment);
    }
    return visualAcuitiesAdditionalAssessments;
}

// -------------------------------------------------------------------------------------------------

std::vector<type::Refraction>
SubjectiveExaminationReportDataMaker::makeRefractions(const int& count, const rdmp::RefractionConfig& refractionConfig) {
    std::vector<type::Refraction> refractions;

    for (int i = 0; i < count; i++) {
        auto maybeRefraction = rdm::make(refractionConfig);
        if (maybeRefraction.has_value()) {
            refractions.push_back(maybeRefraction.value());
        } else {
            throw std::runtime_error("Error occured while creating Refraction");
        }
    }
    return refractions;
}

// -------------------------------------------------------------------------------------------------

} // testdatamaker
