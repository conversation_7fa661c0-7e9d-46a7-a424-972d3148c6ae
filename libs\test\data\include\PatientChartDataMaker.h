/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

#pragma once

#include "RandomUtils.h"
#include "ProtoUtils.h"
#include "ExaminationReportDataMaker.h"
#include "SubjectiveExaminationReportDataMaker.h"
#include "SurgeryReportDataMaker.h"
#include "../generated/PatientChartDataMaker.pb.h"

namespace testdatamaker {

namespace pcdm = testdatamaker::patientchartdatamaker;
namespace coretypesdm = testdatamaker::coretypes;

class PatientChartDataMaker {
public:
    using patientchartdatamaker_config_t = dmb::datamaker_config_t<type::PatientChart, pcdm::PatientChartParams>;
    using patientchartdatamaker_config_Builder = dmb::datamaker_config_Builder<type::PatientChart, pcdm::PatientChartParams>;

    static std::optional<type::PatientChart> make(const pcdm::PatientChartConfig& config);
    static std::optional<type::PatientChart> make(const patientchartdatamaker_config_t& config);

private:
    static type::PatientChart makeRandom(const pcdm::PatientChartParams& params);
    static type::PatientChart makeBasedOn(
        const std::optional<type::PatientChart>& seed,
        const pcdm::PatientChartParams& params,
        const coretypesdm::GeneratingPolicy mode);
};

} // testdatamaker
