2025-06-30T09:31:54.0889892+05:30 [Information] [----------] 1 step from Teardown
2025-06-30T09:31:54.0895801+05:30 [Information] Set Value to Step '0e17587e-89f6-4571-b021-416da9f1851f' name:
"Clean up Database"
2025-06-30T09:31:54.0897047+05:30 [Information] Set Value to Step '0e17587e-89f6-4571-b021-416da9f1851f' description:
"Tear down the database state"
2025-06-30T09:31:54.0897688+05:30 [Information] [ RUN      ] Teardown > Clean up Database
2025-06-30T09:31:54.0900160+05:30 [Information] Executing single instance of step 'Clean up Database'.
2025-06-30T09:31:54.0901294+05:30 [Information] Executing step 'Clean up Database' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "0e17587e-89f6-4571-b021-416da9f1851f",
        "tables": {},
        "variables": {},
        "name": "Clean up Database",
        "description": "Tear down the database state"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:31:54.0903694+05:30 [Information] Processing $var: path 'baseUrl'
2025-06-30T09:31:54.0904738+05:30 [Information] Checking if 'baseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:31:54.0905417+05:30 [Information] Resolving path 'baseUrl' using global variables.
2025-06-30T09:31:54.0906040+05:30 [Information] Resolved JPath '$var:baseUrl' to 'https://httpbin.org'
2025-06-30T09:31:54.0906793+05:30 [Information] Resolved template pattern '{{$var:baseUrl}}' to 'https://httpbin.org' in '{{$var:baseUrl}}/post'
2025-06-30T09:31:54.0908191+05:30 [Information] Resolved template '{{$var:baseUrl}}/post' to 'https://httpbin.org/post'
2025-06-30T09:31:54.0910485+05:30 [Information] Set Value to Step '0e17587e-89f6-4571-b021-416da9f1851f' input:
{
  "Method": "POST",
  "RequestUri": "https://httpbin.org/post",
  "Headers": {
    "Content-Type": "application/json"
  },
  "Content": {
    "action": "cleanup_db"
  }
}
2025-06-30T09:31:54.8904649+05:30 [Information] Set Value to Step '0e17587e-89f6-4571-b021-416da9f1851f' output:
{
  "statusCode": 200,
  "reasonPhrase": "OK",
  "headers": [
    {
      "Key": "Date",
      "Value": [
        "Mon, 30 Jun 2025 04:02:00 GMT"
      ]
    },
    {
      "Key": "Connection",
      "Value": [
        "keep-alive"
      ]
    },
    {
      "Key": "Server",
      "Value": [
        "gunicorn/19.9.0"
      ]
    },
    {
      "Key": "Access-Control-Allow-Origin",
      "Value": [
        "*"
      ]
    },
    {
      "Key": "Access-Control-Allow-Credentials",
      "Value": [
        "true"
      ]
    }
  ],
  "content": {
    "args": {},
    "data": "{\r\n  \"action\": \"cleanup_db\"\r\n}",
    "files": {},
    "form": {},
    "headers": {
      "Content-Length": "30",
      "Content-Type": "application/json; charset=utf-8",
      "Host": "httpbin.org",
      "X-Amzn-Trace-Id": "Root=1-68620c38-77822b6e248e95986681a932"
    },
    "json": {
      "action": "cleanup_db"
    },
    "origin": "**************",
    "url": "https://httpbin.org/post"
  }
}
2025-06-30T09:31:54.8909718+05:30 [Information] Set Value to Step '0e17587e-89f6-4571-b021-416da9f1851f' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:01:54.0900134Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:31:54.8911548+05:30 [Information] Set Value to Step '0e17587e-89f6-4571-b021-416da9f1851f' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 799,
  "startTime": "2025-06-30T04:01:54.0900134Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:31:54.8913149+05:30 [Information] Step 'Clean up Database' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "0e17587e-89f6-4571-b021-416da9f1851f",
        "tables": {},
        "variables": {},
        "name": "Clean up Database",
        "description": "Tear down the database state",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "action": "cleanup_db"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:02:00 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"action\": \"cleanup_db\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "30",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620c38-77822b6e248e95986681a932"
            },
            "json": {
              "action": "cleanup_db"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 799,
          "startTime": "2025-06-30T04:01:54.0900134Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:31:54.8913908+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:31:54.8915154+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:31:54.8915722+05:30 [Information] No performance data found for step 'Clean up Database' (http)
2025-06-30T09:31:54.8916152+05:30 [Information] [ OK       ]
2025-06-30T09:31:54.8921601+05:30 [Information] [----------] 1 step from Teardown (802 ms total)
