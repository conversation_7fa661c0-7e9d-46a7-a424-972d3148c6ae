/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;

namespace TestLib;

/// <summary>
/// Provides utility methods for managing and resolving context data, including variables, tables, and file paths.
/// </summary>
/// <remarks>
/// Initializes a new instance of the <see cref="ContextUtils"/> class.
/// </remarks>
/// <param name="context">The test context instance.</param>
public class ContextUtils(TestContext context)
{
    // Public Methods

    /// <summary>
    /// Resolves the file path for external files, handling "file://" prefixes and relative paths.
    /// </summary>
    /// <param name="path">The file path to resolve.</param>
    /// <returns>The resolved absolute file path.</returns>
    public string ResolveFilePath(string path)
    {
        if (path.StartsWith("file://"))
        {
            path = path.Substring("file://".Length);
        }

        string curDir = context.Variables.Get("curDir")?.ToString();

        if (string.IsNullOrEmpty(curDir))
        {
            throw new InvalidOperationException("curDir is not set in the context.");
        }

        return Path.Combine(curDir, path);
    }

    /// <summary>
    /// Applies a conversion function to the resolved value.
    /// </summary>
    /// <param name="functionName">The name of the conversion function.</param>
    /// <param name="value">The value to be converted.</param>
    /// <returns>The converted value.</returns>
    internal static string ApplyConversionFunction(string functionName, string value)
    {
        try
        {
            return functionName switch
            {
                "URLEncode" => Uri.EscapeDataString(value),
                "String" => value.ToString(),
                "ToUpper" => value.ToUpperInvariant(),
                "ToLower" => value.ToLowerInvariant(),
                "Trim" => value.Trim(),
                "Base64Encode" => Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(value)),
                "Base64Decode" => System.Text.Encoding.UTF8.GetString(Convert.FromBase64String(value)),
                "Reverse" => new string(value.Reverse().ToArray()),
                _ => throw new NotSupportedException($"Conversion function '{functionName}' is not supported.")
            };
        }
        catch (Exception ex)
        {
            TestLogger.Trace($"Error applying conversion function '{functionName}' to value '{value}': {ex.Message}");
            return value;
        }
    }

    /// <summary>
    /// Evaluates a mathematical expression that may contain variable references.
    /// Supports basic arithmetic operations: +, -, *, /, and parentheses.
    /// Variables can be referenced using $var:variableName syntax.
    /// </summary>
    /// <param name="expression">The mathematical expression to evaluate.</param>
    /// <param name="context">The test context for variable resolution.</param>
    /// <returns>The result of the mathematical expression as a string.</returns>
    internal static string EvaluateMathematicalExpression(string expression, TestContext context)
    {
        try
        {
            TestLogger.Trace($"Evaluating mathematical expression: '{expression}'");

            // First, resolve any variable references in the expression
            var resolvedExpression = ResolveVariablesInExpression(expression, context);
            TestLogger.Trace($"Expression after variable resolution: '{resolvedExpression}'");

            // Evaluate the mathematical expression
            var result = EvaluateArithmeticExpression(resolvedExpression);
            TestLogger.Trace($"Mathematical expression '{expression}' evaluated to: {result}");

            return result.ToString(CultureInfo.InvariantCulture);
        }
        catch (Exception ex)
        {
            TestLogger.Trace($"Error evaluating mathematical expression '{expression}': {ex.Message}");
            return expression; // Return original expression if evaluation fails
        }
    }

    /// <summary>
    /// Resolves variable references ($var:variableName) within a mathematical expression.
    /// Also resolves simple variable names that are not prefixed with $var:.
    /// </summary>
    /// <param name="expression">The expression containing variable references.</param>
    /// <param name="context">The test context for variable resolution.</param>
    /// <returns>The expression with variables replaced by their values.</returns>
    private static string ResolveVariablesInExpression(string expression, TestContext context)
    {
        TestLogger.Trace($"Resolving variables in expression: '{expression}'");

        // First, resolve $var:variableName patterns
        var variablePattern = @"\$var:([a-zA-Z_][a-zA-Z0-9_]*)";
        var result = Regex.Replace(expression, variablePattern, match =>
        {
            var variableName = match.Groups[1].Value;
            return ResolveVariableValue(variableName, context, match.Value);
        });

        // Then, resolve simple variable names (identifiers that are not numbers)
        var simpleVariablePattern = @"\b([a-zA-Z_][a-zA-Z0-9_]*)\b";
        result = Regex.Replace(result, simpleVariablePattern, match =>
        {
            var variableName = match.Groups[1].Value;

            // Skip if it's already a number or a resolved value
            if (double.TryParse(variableName, out _))
            {
                return variableName;
            }

            return ResolveVariableValue(variableName, context, variableName);
        });

        TestLogger.Trace($"Expression after variable resolution: '{result}'");
        return result;
    }

    /// <summary>
    /// Resolves a single variable value from the context.
    /// </summary>
    /// <param name="variableName">The name of the variable to resolve.</param>
    /// <param name="context">The test context.</param>
    /// <param name="fallback">The fallback value if variable is not found.</param>
    /// <returns>The resolved variable value or fallback.</returns>
    private static string ResolveVariableValue(string variableName, TestContext context, string fallback)
    {
        // Try to resolve the variable from step-specific variables first
        var currentStep = context.Data.GetCurrentStep();
        if (currentStep?["variables"] is JObject stepVariables)
        {
            var stepResult = stepVariables.SelectToken(variableName);
            if (stepResult != null)
            {
                TestLogger.Trace($"Resolved variable '{variableName}' from step variables: {stepResult}");
                return stepResult.ToString();
            }
        }

        // Try global variables
        var globalResult = context.Data.VariablesNode?.SelectToken(variableName);
        if (globalResult != null)
        {
            TestLogger.Trace($"Resolved variable '{variableName}' from global variables: {globalResult}");
            return globalResult.ToString();
        }

        TestLogger.Trace($"Variable '{variableName}' not found, using fallback: {fallback}");
        return fallback; // Keep original if not found
    }

    /// <summary>
    /// Evaluates a simple arithmetic expression containing numbers and basic operators.
    /// Supports +, -, *, /, and parentheses with proper operator precedence.
    /// </summary>
    /// <param name="expression">The arithmetic expression to evaluate.</param>
    /// <returns>The result of the arithmetic expression.</returns>
    private static double EvaluateArithmeticExpression(string expression)
    {
        // Remove whitespace
        expression = expression.Replace(" ", "");

        // Validate expression contains only allowed characters
        if (!Regex.IsMatch(expression, @"^[0-9+\-*/.()]+$"))
        {
            throw new ArgumentException($"Invalid characters in arithmetic expression: {expression}");
        }

        return EvaluateExpression(expression);
    }

    /// <summary>
    /// Recursive descent parser for arithmetic expressions.
    /// Handles operator precedence: parentheses > multiplication/division > addition/subtraction.
    /// </summary>
    /// <param name="expression">The expression to evaluate.</param>
    /// <returns>The result of the expression.</returns>
    private static double EvaluateExpression(string expression)
    {
        return ParseAdditionSubtraction(expression, ref expression);
    }

    // Internal Methods

    /// <summary>
    /// Resolves JPaths for the step and stores variables, tables, or rows in the context.
    /// </summary>
    /// <param name="storeDefinitions">The definitions of what to store.</param>
    internal void Store(Dictionary<string, JToken> storeDefinitions)
    {
        if (storeDefinitions == null) return;

        foreach (var entry in storeDefinitions)
        {
            var key = entry.Key;
            var value = entry.Value;

            if (value == null) continue;

            switch (key)
            {
                case "Variables":
                    if (value is JObject variablesObject)
                    {
                        context.Variables.Store(variablesObject);
                    }
                    else
                    {
                        throw new InvalidOperationException("Expected JObject for Variables.");
                    }
                    break;

                case "Tables":
                    if (value is JObject tablesObject)
                    {
                        context.Tables.Store(tablesObject);
                    }
                    else
                    {
                        throw new InvalidOperationException("Expected JObject for Tables.");
                    }
                    break;

                case "Row":
                case "StepRow":
                    if (value is JObject rowObject)
                    {
                        StoreRow(rowObject, false);
                    }
                    else
                    {
                        throw new InvalidOperationException("Expected JObject for Row.");
                    }
                    break;
                case "TestRow":
                    if (value is JObject stepRowObject)
                    {
                        StoreRow(stepRowObject, true);
                    }
                    else
                    {
                        throw new InvalidOperationException("Expected JObject for TestRow.");
                    }
                    break;

                default:
                    context.Variables.Store(key, value.ToString());
                    break;
            }
        }
    }

    /// <summary>
    /// Stores the current row in the context.
    /// </summary>
    /// <param name="rowMappings"></param>
    /// <param name="onlyTestRow"></param>
    /// <exception cref="InvalidOperationException"></exception>
    internal void StoreRow(JObject rowMappings, bool onlyTestRow = false)
    {
        string repeatForTableName = null;
        int currentRowIndex = -1;
        bool isTestRow = false;

        if (onlyTestRow)
        {
            repeatForTableName = GetTestContext("repeatForTable")?.ToString();
            currentRowIndex = GetTestContext("rowIndex")?.ToObject<int>() ?? -1;
            isTestRow = true;
        }
        else
        {
            repeatForTableName = GetStepContext("repeatForTable")?.ToString();
            currentRowIndex = GetStepContext("rowIndex")?.ToObject<int>() ?? -1;
            if (string.IsNullOrWhiteSpace(repeatForTableName))
            {
                repeatForTableName = GetTestContext("repeatForTable")?.ToString();
                currentRowIndex = GetTestContext("rowIndex")?.ToObject<int>() ?? -1;
                isTestRow = true;
            }
        }

        if (string.IsNullOrWhiteSpace(repeatForTableName) || currentRowIndex < 0)
        {
            throw new InvalidOperationException("Cannot store row values. Missing current table or row index context.");
        }

        var table = context.Tables.GetAsList(repeatForTableName);
        if (table == null || currentRowIndex >= table.Count)
        {
            throw new InvalidOperationException($"Invalid table '{repeatForTableName}' or row index '{currentRowIndex}'.");
        }

        var currentRow = table[currentRowIndex];
        foreach (var entry in rowMappings)
        {
            var resolvedValue = context.JPathResolver.Resolve<string>(entry.Value.ToString());
            var value = context.JPathResolver.ResolveJPath(resolvedValue);

            if (value != null)
            {
                currentRow[entry.Key] = value;
            }
        }

        context.Tables.UpdateTableRow(repeatForTableName, currentRowIndex, currentRow);
        context.Utils.UpsertContext("row", JToken.FromObject(currentRow), isTestRow ? null : context.Data.GetCurrentStepId);

    }

    /// <summary>
    /// The method is used to store a variable in the context.
    /// </summary>
    /// <param name="variableName"></param>
    /// <param name="variableValue"></param>
    /// <param name="id"></param>
    internal void UpsertContext(string variableName, JToken variableValue, string id = null)
    {

        if (!string.IsNullOrEmpty(id))
        {
            var step = context.Data.GetStepById(id) ?? context.Data.CreateStep(id);

            if (step["context"] == null || step["context"].Type != JTokenType.Object)
            {
                step["context"] = new JObject();
            }

            var stepVariables = step["context"] as JObject;
            stepVariables[variableName] = variableValue;
        }
        else
        {
            var globalVariables = context.Data.ContextNode as JObject;
            globalVariables[variableName] = variableValue;
        }
    }

    /// <summary>
    /// This method is used to retrieve the value from the step context.
    /// </summary>
    /// <param name="key"></param>
    /// <returns></returns>
    private JToken GetStepContext(string key)
    {
        var currentStep = context.Data.GetCurrentStep();
        if (currentStep?["context"] is JObject stepContextVariables && 
            stepContextVariables.TryGetValue(key, out var value))
        {
            return value;
        }
        else
        {
            return null;
        }
    }

    /// <summary>
    /// This method is used to retrieve the value from the test context.
    /// </summary>
    /// <param name="key"></param>
    /// <returns></returns>
    private JToken GetTestContext(string key)
    {
        var testContext = (JObject)context.Data.ContextNode;
        return testContext?[key];
    }

    /// <summary>
    /// Parses addition and subtraction operations (lowest precedence).
    /// </summary>
    private static double ParseAdditionSubtraction(string expression, ref string remaining)
    {
        var result = ParseMultiplicationDivision(expression, ref remaining);

        while (remaining.Length > 0 && (remaining[0] == '+' || remaining[0] == '-'))
        {
            var op = remaining[0];
            remaining = remaining.Substring(1);
            var right = ParseMultiplicationDivision(remaining, ref remaining);

            result = op == '+' ? result + right : result - right;
        }

        return result;
    }

    /// <summary>
    /// Parses multiplication and division operations (higher precedence).
    /// </summary>
    private static double ParseMultiplicationDivision(string expression, ref string remaining)
    {
        var result = ParseFactor(expression, ref remaining);

        while (remaining.Length > 0 && (remaining[0] == '*' || remaining[0] == '/'))
        {
            var op = remaining[0];
            remaining = remaining.Substring(1);
            var right = ParseFactor(remaining, ref remaining);

            if (op == '*')
            {
                result *= right;
            }
            else
            {
                if (Math.Abs(right) < double.Epsilon)
                {
                    throw new DivideByZeroException("Division by zero in mathematical expression");
                }
                result /= right;
            }
        }

        return result;
    }

    /// <summary>
    /// Parses factors: numbers, parentheses, and unary operators (highest precedence).
    /// </summary>
    private static double ParseFactor(string expression, ref string remaining)
    {
        // Handle unary minus
        if (remaining.Length > 0 && remaining[0] == '-')
        {
            remaining = remaining.Substring(1);
            return -ParseFactor(expression, ref remaining);
        }

        // Handle unary plus
        if (remaining.Length > 0 && remaining[0] == '+')
        {
            remaining = remaining.Substring(1);
            return ParseFactor(expression, ref remaining);
        }

        // Handle parentheses
        if (remaining.Length > 0 && remaining[0] == '(')
        {
            remaining = remaining.Substring(1);
            var result = ParseAdditionSubtraction(remaining, ref remaining);

            if (remaining.Length == 0 || remaining[0] != ')')
            {
                throw new ArgumentException("Mismatched parentheses in mathematical expression");
            }

            remaining = remaining.Substring(1);
            return result;
        }

        // Parse number
        return ParseNumber(ref remaining);
    }

    /// <summary>
    /// Parses a number from the beginning of the remaining expression.
    /// </summary>
    private static double ParseNumber(ref string remaining)
    {
        var numberMatch = Regex.Match(remaining, @"^(\d+(?:\.\d+)?)");

        if (!numberMatch.Success)
        {
            throw new ArgumentException($"Expected number at: {remaining}");
        }

        var numberStr = numberMatch.Groups[1].Value;
        remaining = remaining.Substring(numberStr.Length);

        return double.Parse(numberStr, CultureInfo.InvariantCulture);
    }
}
