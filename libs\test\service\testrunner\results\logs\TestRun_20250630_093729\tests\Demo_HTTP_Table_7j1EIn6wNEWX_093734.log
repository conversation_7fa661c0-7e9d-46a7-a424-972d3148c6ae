2025-06-30T09:37:34.3024326+05:30 [Information] [----------] 1 step from Demo HTTP Table Step Iteration
2025-06-30T09:37:34.3031062+05:30 [Information] Set Value to Step '7175e55d-6a7e-40de-8dfc-19512281ebd1' name:
"Run for Each User"
2025-06-30T09:37:34.3032528+05:30 [Information] Set Value to Step '7175e55d-6a7e-40de-8dfc-19512281ebd1' description:
"Run step for each user"
2025-06-30T09:37:34.3033193+05:30 [Information] [ RUN      ] Demo HTTP Table Step Iteration > Run for Each User
2025-06-30T09:37:34.3053950+05:30 [Information] Executing step 'Run for Each User' for row 1/2 in table 'UserTable'.
2025-06-30T09:37:34.3059510+05:30 [Information] Default-mapped column 'User' to variable 'User' with value '{
  "Username": "<PERSON>",
  "Email": "<EMAIL>"
}'
2025-06-30T09:37:34.3095341+05:30 [Information] Default-mapped column 'OrgName' to variable 'OrgName' with value 'Org1'
2025-06-30T09:37:34.3100177+05:30 [Information] Processing $var: path 'User.Username'
2025-06-30T09:37:34.3101577+05:30 [Information] Checking if 'User.Username' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=True, result=False
2025-06-30T09:37:34.3102309+05:30 [Information] Resolved path 'User.Username' in step-specific variables.
2025-06-30T09:37:34.3102890+05:30 [Information] Resolved JPath '$var:User.Username' to 'Tom'
2025-06-30T09:37:34.3103290+05:30 [Information] Resolved template pattern '{{$var:User.Username}}' to 'Tom' in '{{$var:User.Username}}'
2025-06-30T09:37:34.3103752+05:30 [Information] Resolved template '{{$var:User.Username}}' to 'Tom'
2025-06-30T09:37:34.3104662+05:30 [Information] Mapped column '{{$var:User.Username}}' to variable 'username' with value 'Tom'
2025-06-30T09:37:34.3105270+05:30 [Information] Processing $var: path 'User.Email'
2025-06-30T09:37:34.3105708+05:30 [Information] Checking if 'User.Email' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=True, result=False
2025-06-30T09:37:34.3106229+05:30 [Information] Resolved path 'User.Email' in step-specific variables.
2025-06-30T09:37:34.3106605+05:30 [Information] Resolved JPath '$var:User.Email' to '<EMAIL>'
2025-06-30T09:37:34.3106951+05:30 [Information] Resolved template pattern '{{$var:User.Email}}' to '<EMAIL>' in '{{$var:User.Email}}'
2025-06-30T09:37:34.3107378+05:30 [Information] Resolved template '{{$var:User.Email}}' to '<EMAIL>'
2025-06-30T09:37:34.3108079+05:30 [Information] Mapped column '{{$var:User.Email}}' to variable 'email' with value '<EMAIL>'
2025-06-30T09:37:34.3109443+05:30 [Information] Processing $var: path 'username'
2025-06-30T09:37:34.3110225+05:30 [Information] Checking if 'username' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:34.3110757+05:30 [Information] Resolved path 'username' in step-specific variables.
2025-06-30T09:37:34.3111282+05:30 [Information] Resolved JPath '$var:username' to 'Tom'
2025-06-30T09:37:34.3111652+05:30 [Information] Resolved template pattern '{{$var:username}}' to 'Tom' in 'Fetching user {{$var:username}} details'
2025-06-30T09:37:34.3112153+05:30 [Information] Resolved template 'Fetching user {{$var:username}} details' to 'Fetching user Tom details'
2025-06-30T09:37:34.3115845+05:30 [Information] Step Iteration 1/2: Fetching user Tom details
2025-06-30T09:37:34.3118459+05:30 [Information] Executing step 'Fetching user Tom details' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "7175e55d-6a7e-40de-8dfc-19512281ebd1",
        "tables": {},
        "variables": {
          "User": {
            "Username": "Tom",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org1",
          "username": "Tom",
          "email": "<EMAIL>"
        },
        "name": "Run for Each User",
        "description": "Run step for each user",
        "context": {
          "repeatForTable": "UserTable",
          "row": {
            "User": {
              "Username": "Tom",
              "Email": "<EMAIL>"
            },
            "OrgName": "Org1"
          },
          "rowIndex": 0
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaseUrl": "https://httpbin.org"
    },
    "tables": {
      "UserTable": [
        {
          "User": {
            "Username": "Tom",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org1"
        },
        {
          "User": {
            "Username": "John",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org2"
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:37:34.3119532+05:30 [Information] Processing $var: path 'BaseUrl'
2025-06-30T09:37:34.3120029+05:30 [Information] Checking if 'BaseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:34.3120448+05:30 [Information] Resolving path 'BaseUrl' using global variables.
2025-06-30T09:37:34.3121000+05:30 [Information] Resolved JPath '$var:BaseUrl' to 'https://httpbin.org'
2025-06-30T09:37:34.3121364+05:30 [Information] Resolved template pattern '{{$var:BaseUrl}}' to 'https://httpbin.org' in '{{$var:BaseUrl}}/post'
2025-06-30T09:37:34.3121809+05:30 [Information] Resolved template '{{$var:BaseUrl}}/post' to 'https://httpbin.org/post'
2025-06-30T09:37:34.3122548+05:30 [Information] Processing $var: path 'username'
2025-06-30T09:37:34.3123025+05:30 [Information] Checking if 'username' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:34.3123457+05:30 [Information] Resolved path 'username' in step-specific variables.
2025-06-30T09:37:34.3124464+05:30 [Information] Resolved JPath '$var:username' to 'Tom'
2025-06-30T09:37:34.3126136+05:30 [Information] Resolved template pattern '{{$var:username}}' to 'Tom' in '{
  "username": "{{$var:username}}",
  "email": "{{$var:email}}",
  "org": "{{$var:OrgName}}"
}'
2025-06-30T09:37:34.3126995+05:30 [Information] Processing $var: path 'email'
2025-06-30T09:37:34.3127606+05:30 [Information] Checking if 'email' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:34.3128213+05:30 [Information] Resolved path 'email' in step-specific variables.
2025-06-30T09:37:34.3128791+05:30 [Information] Resolved JPath '$var:email' to '<EMAIL>'
2025-06-30T09:37:34.3129375+05:30 [Information] Resolved template pattern '{{$var:email}}' to '<EMAIL>' in '{
  "username": "{{$var:username}}",
  "email": "{{$var:email}}",
  "org": "{{$var:OrgName}}"
}'
2025-06-30T09:37:34.3129949+05:30 [Information] Processing $var: path 'OrgName'
2025-06-30T09:37:34.3130536+05:30 [Information] Checking if 'OrgName' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:34.3131105+05:30 [Information] Resolved path 'OrgName' in step-specific variables.
2025-06-30T09:37:34.3131669+05:30 [Information] Resolved JPath '$var:OrgName' to 'Org1'
2025-06-30T09:37:34.3132172+05:30 [Information] Resolved template pattern '{{$var:OrgName}}' to 'Org1' in '{
  "username": "{{$var:username}}",
  "email": "{{$var:email}}",
  "org": "{{$var:OrgName}}"
}'
2025-06-30T09:37:34.3132614+05:30 [Information] Resolved template '{
  "username": "{{$var:username}}",
  "email": "{{$var:email}}",
  "org": "{{$var:OrgName}}"
}' to '{
  "username": "Tom",
  "email": "<EMAIL>",
  "org": "Org1"
}'
2025-06-30T09:37:34.3133382+05:30 [Information] Set Value to Step '7175e55d-6a7e-40de-8dfc-19512281ebd1' input:
{
  "Method": "POST",
  "RequestUri": "https://httpbin.org/post",
  "Headers": {
    "Content-Type": "application/json"
  },
  "Content": {
    "username": "Tom",
    "email": "<EMAIL>",
    "org": "Org1"
  }
}
2025-06-30T09:37:35.1049986+05:30 [Information] Set Value to Step '7175e55d-6a7e-40de-8dfc-19512281ebd1' output:
{
  "statusCode": 200,
  "reasonPhrase": "OK",
  "headers": [
    {
      "Key": "Date",
      "Value": [
        "Mon, 30 Jun 2025 04:07:40 GMT"
      ]
    },
    {
      "Key": "Connection",
      "Value": [
        "keep-alive"
      ]
    },
    {
      "Key": "Server",
      "Value": [
        "gunicorn/19.9.0"
      ]
    },
    {
      "Key": "Access-Control-Allow-Origin",
      "Value": [
        "*"
      ]
    },
    {
      "Key": "Access-Control-Allow-Credentials",
      "Value": [
        "true"
      ]
    }
  ],
  "content": {
    "args": {},
    "data": "{\r\n  \"username\": \"Tom\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"org\": \"Org1\"\r\n}",
    "files": {},
    "form": {},
    "headers": {
      "Content-Length": "74",
      "Content-Type": "application/json; charset=utf-8",
      "Host": "httpbin.org",
      "X-Amzn-Trace-Id": "Root=1-68620d8c-7fb078e061f8c6544396b4c4"
    },
    "json": {
      "email": "<EMAIL>",
      "org": "Org1",
      "username": "Tom"
    },
    "origin": "**************",
    "url": "https://httpbin.org/post"
  }
}
2025-06-30T09:37:35.1083413+05:30 [Information] Resolving path 'output.content.json.url' using step input/output.
2025-06-30T09:37:35.1089145+05:30 [Information] Resolved JPath 'output.content.json.url' to ''
2025-06-30T09:37:35.1099840+05:30 [Information] Set Value to Step '7175e55d-6a7e-40de-8dfc-19512281ebd1' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:07:34.3038454Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:37:35.1101186+05:30 [Information] Run Asserter : {"Expression1":{"Value":"200","ErrorMesage":null},"Expression2":{"JPath":"output.statusCode","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:37:35.1101830+05:30 [Information] Resolving path 'output.statusCode' using step input/output.
2025-06-30T09:37:35.1102337+05:30 [Information] Resolved JPath 'output.statusCode' to '200'
2025-06-30T09:37:35.1102832+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$step:row.User.Username}}","ErrorMesage":null},"Expression2":{"JPath":"output.content.json.username","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:37:35.1103404+05:30 [Information] Resolved path 'row.User.Username' in step-specific context.
2025-06-30T09:37:35.1103802+05:30 [Information] Resolved JPath '$step:row.User.Username' to 'Tom'
2025-06-30T09:37:35.1104157+05:30 [Information] Resolved template pattern '{{$step:row.User.Username}}' to 'Tom' in '{{$step:row.User.Username}}'
2025-06-30T09:37:35.1104574+05:30 [Information] Resolved template '{{$step:row.User.Username}}' to 'Tom'
2025-06-30T09:37:35.1105366+05:30 [Information] Resolving path 'output.content.json.username' using step input/output.
2025-06-30T09:37:35.1105863+05:30 [Information] Resolved JPath 'output.content.json.username' to 'Tom'
2025-06-30T09:37:35.1106368+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$step:row.User.Email}}","ErrorMesage":null},"Expression2":{"JPath":"output.content.json.email","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:37:35.1106850+05:30 [Information] Resolved path 'row.User.Email' in step-specific context.
2025-06-30T09:37:35.1107238+05:30 [Information] Resolved JPath '$step:row.User.Email' to '<EMAIL>'
2025-06-30T09:37:35.1107646+05:30 [Information] Resolved template pattern '{{$step:row.User.Email}}' to '<EMAIL>' in '{{$step:row.User.Email}}'
2025-06-30T09:37:35.1108052+05:30 [Information] Resolved template '{{$step:row.User.Email}}' to '<EMAIL>'
2025-06-30T09:37:35.1108622+05:30 [Information] Resolving path 'output.content.json.email' using step input/output.
2025-06-30T09:37:35.1109030+05:30 [Information] Resolved JPath 'output.content.json.email' to '<EMAIL>'
2025-06-30T09:37:35.1109460+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$step:row.OrgName}}","ErrorMesage":null},"Expression2":{"JPath":"output.content.json.org","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:37:35.1110133+05:30 [Information] Resolved path 'row.OrgName' in step-specific context.
2025-06-30T09:37:35.1110507+05:30 [Information] Resolved JPath '$step:row.OrgName' to 'Org1'
2025-06-30T09:37:35.1110847+05:30 [Information] Resolved template pattern '{{$step:row.OrgName}}' to 'Org1' in '{{$step:row.OrgName}}'
2025-06-30T09:37:35.1111222+05:30 [Information] Resolved template '{{$step:row.OrgName}}' to 'Org1'
2025-06-30T09:37:35.1111830+05:30 [Information] Resolving path 'output.content.json.org' using step input/output.
2025-06-30T09:37:35.1112247+05:30 [Information] Resolved JPath 'output.content.json.org' to 'Org1'
2025-06-30T09:37:35.1112805+05:30 [Information] Set Value to Step '7175e55d-6a7e-40de-8dfc-19512281ebd1' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 797,
  "startTime": "2025-06-30T04:07:34.3038454Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:37:35.1113386+05:30 [Information] Step 'Fetching user Tom details' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "7175e55d-6a7e-40de-8dfc-19512281ebd1",
        "tables": {},
        "variables": {
          "User": {
            "Username": "Tom",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org1",
          "username": "Tom",
          "email": "<EMAIL>"
        },
        "name": "Run for Each User",
        "description": "Run step for each user",
        "context": {
          "repeatForTable": "UserTable",
          "row": {
            "User": {
              "Username": "Tom",
              "Email": "<EMAIL>"
            },
            "OrgName": "Org1"
          },
          "rowIndex": 0
        },
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "Tom",
            "email": "<EMAIL>",
            "org": "Org1"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:07:40 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"Tom\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"org\": \"Org1\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "74",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620d8c-7fb078e061f8c6544396b4c4"
            },
            "json": {
              "email": "<EMAIL>",
              "org": "Org1",
              "username": "Tom"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 797,
          "startTime": "2025-06-30T04:07:34.3038454Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaseUrl": "https://httpbin.org"
    },
    "tables": {
      "UserTable": [
        {
          "User": {
            "Username": "Tom",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org1"
        },
        {
          "User": {
            "Username": "John",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org2"
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:37:35.1113873+05:30 [Information] Executing step 'Run for Each User' for row 2/2 in table 'UserTable'.
2025-06-30T09:37:35.1114671+05:30 [Information] Default-mapped column 'User' to variable 'User' with value '{
  "Username": "John",
  "Email": "<EMAIL>"
}'
2025-06-30T09:37:35.1115139+05:30 [Information] Default-mapped column 'OrgName' to variable 'OrgName' with value 'Org2'
2025-06-30T09:37:35.1115581+05:30 [Information] Processing $var: path 'User.Username'
2025-06-30T09:37:35.1116003+05:30 [Information] Checking if 'User.Username' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=True, result=False
2025-06-30T09:37:35.1116393+05:30 [Information] Resolved path 'User.Username' in step-specific variables.
2025-06-30T09:37:35.1116765+05:30 [Information] Resolved JPath '$var:User.Username' to 'John'
2025-06-30T09:37:35.1117119+05:30 [Information] Resolved template pattern '{{$var:User.Username}}' to 'John' in '{{$var:User.Username}}'
2025-06-30T09:37:35.1117511+05:30 [Information] Resolved template '{{$var:User.Username}}' to 'John'
2025-06-30T09:37:35.1118145+05:30 [Information] Mapped column '{{$var:User.Username}}' to variable 'username' with value 'John'
2025-06-30T09:37:35.1118611+05:30 [Information] Processing $var: path 'User.Email'
2025-06-30T09:37:35.1119069+05:30 [Information] Checking if 'User.Email' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=True, result=False
2025-06-30T09:37:35.1119464+05:30 [Information] Resolved path 'User.Email' in step-specific variables.
2025-06-30T09:37:35.1119832+05:30 [Information] Resolved JPath '$var:User.Email' to '<EMAIL>'
2025-06-30T09:37:35.1120177+05:30 [Information] Resolved template pattern '{{$var:User.Email}}' to '<EMAIL>' in '{{$var:User.Email}}'
2025-06-30T09:37:35.1120569+05:30 [Information] Resolved template '{{$var:User.Email}}' to '<EMAIL>'
2025-06-30T09:37:35.1121236+05:30 [Information] Mapped column '{{$var:User.Email}}' to variable 'email' with value '<EMAIL>'
2025-06-30T09:37:35.1121701+05:30 [Information] Processing $var: path 'username'
2025-06-30T09:37:35.1122087+05:30 [Information] Checking if 'username' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:35.1122476+05:30 [Information] Resolved path 'username' in step-specific variables.
2025-06-30T09:37:35.1122844+05:30 [Information] Resolved JPath '$var:username' to 'John'
2025-06-30T09:37:35.1123211+05:30 [Information] Resolved template pattern '{{$var:username}}' to 'John' in 'Fetching user {{$var:username}} details'
2025-06-30T09:37:35.1123601+05:30 [Information] Resolved template 'Fetching user {{$var:username}} details' to 'Fetching user John details'
2025-06-30T09:37:35.1126367+05:30 [Information] Step Iteration 2/2: Fetching user John details
2025-06-30T09:37:35.1128361+05:30 [Information] Executing step 'Fetching user John details' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "7175e55d-6a7e-40de-8dfc-19512281ebd1",
        "tables": {},
        "variables": {
          "User": {
            "Username": "John",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org2",
          "username": "John",
          "email": "<EMAIL>"
        },
        "name": "Run for Each User",
        "description": "Run step for each user",
        "context": {
          "repeatForTable": "UserTable",
          "row": {
            "User": {
              "Username": "John",
              "Email": "<EMAIL>"
            },
            "OrgName": "Org2"
          },
          "rowIndex": 1
        },
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "Tom",
            "email": "<EMAIL>",
            "org": "Org1"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:07:40 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"Tom\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"org\": \"Org1\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "74",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620d8c-7fb078e061f8c6544396b4c4"
            },
            "json": {
              "email": "<EMAIL>",
              "org": "Org1",
              "username": "Tom"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 797,
          "startTime": "2025-06-30T04:07:34.3038454Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaseUrl": "https://httpbin.org"
    },
    "tables": {
      "UserTable": [
        {
          "User": {
            "Username": "Tom",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org1"
        },
        {
          "User": {
            "Username": "John",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org2"
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:37:35.1129049+05:30 [Information] Processing $var: path 'BaseUrl'
2025-06-30T09:37:35.1129496+05:30 [Information] Checking if 'BaseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:35.1129921+05:30 [Information] Resolving path 'BaseUrl' using global variables.
2025-06-30T09:37:35.1130312+05:30 [Information] Resolved JPath '$var:BaseUrl' to 'https://httpbin.org'
2025-06-30T09:37:35.1130665+05:30 [Information] Resolved template pattern '{{$var:BaseUrl}}' to 'https://httpbin.org' in '{{$var:BaseUrl}}/post'
2025-06-30T09:37:35.1131070+05:30 [Information] Resolved template '{{$var:BaseUrl}}/post' to 'https://httpbin.org/post'
2025-06-30T09:37:35.1131644+05:30 [Information] Processing $var: path 'username'
2025-06-30T09:37:35.1132121+05:30 [Information] Checking if 'username' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:35.1132573+05:30 [Information] Resolved path 'username' in step-specific variables.
2025-06-30T09:37:35.1132969+05:30 [Information] Resolved JPath '$var:username' to 'John'
2025-06-30T09:37:35.1133319+05:30 [Information] Resolved template pattern '{{$var:username}}' to 'John' in '{
  "username": "{{$var:username}}",
  "email": "{{$var:email}}",
  "org": "{{$var:OrgName}}"
}'
2025-06-30T09:37:35.1133712+05:30 [Information] Processing $var: path 'email'
2025-06-30T09:37:35.1134091+05:30 [Information] Checking if 'email' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:35.1134467+05:30 [Information] Resolved path 'email' in step-specific variables.
2025-06-30T09:37:35.1134825+05:30 [Information] Resolved JPath '$var:email' to '<EMAIL>'
2025-06-30T09:37:35.1135173+05:30 [Information] Resolved template pattern '{{$var:email}}' to '<EMAIL>' in '{
  "username": "{{$var:username}}",
  "email": "{{$var:email}}",
  "org": "{{$var:OrgName}}"
}'
2025-06-30T09:37:35.1135566+05:30 [Information] Processing $var: path 'OrgName'
2025-06-30T09:37:35.1135931+05:30 [Information] Checking if 'OrgName' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:35.1136320+05:30 [Information] Resolved path 'OrgName' in step-specific variables.
2025-06-30T09:37:35.1136688+05:30 [Information] Resolved JPath '$var:OrgName' to 'Org2'
2025-06-30T09:37:35.1137025+05:30 [Information] Resolved template pattern '{{$var:OrgName}}' to 'Org2' in '{
  "username": "{{$var:username}}",
  "email": "{{$var:email}}",
  "org": "{{$var:OrgName}}"
}'
2025-06-30T09:37:35.1137437+05:30 [Information] Resolved template '{
  "username": "{{$var:username}}",
  "email": "{{$var:email}}",
  "org": "{{$var:OrgName}}"
}' to '{
  "username": "John",
  "email": "<EMAIL>",
  "org": "Org2"
}'
2025-06-30T09:37:35.1138076+05:30 [Information] Set Value to Step '7175e55d-6a7e-40de-8dfc-19512281ebd1' input:
{
  "Method": "POST",
  "RequestUri": "https://httpbin.org/post",
  "Headers": {
    "Content-Type": "application/json"
  },
  "Content": {
    "username": "John",
    "email": "<EMAIL>",
    "org": "Org2"
  }
}
2025-06-30T09:37:35.5114743+05:30 [Information] Set Value to Step '7175e55d-6a7e-40de-8dfc-19512281ebd1' output:
{
  "statusCode": 200,
  "reasonPhrase": "OK",
  "headers": [
    {
      "Key": "Date",
      "Value": [
        "Mon, 30 Jun 2025 04:07:40 GMT"
      ]
    },
    {
      "Key": "Connection",
      "Value": [
        "keep-alive"
      ]
    },
    {
      "Key": "Server",
      "Value": [
        "gunicorn/19.9.0"
      ]
    },
    {
      "Key": "Access-Control-Allow-Origin",
      "Value": [
        "*"
      ]
    },
    {
      "Key": "Access-Control-Allow-Credentials",
      "Value": [
        "true"
      ]
    }
  ],
  "content": {
    "args": {},
    "data": "{\r\n  \"username\": \"John\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"org\": \"Org2\"\r\n}",
    "files": {},
    "form": {},
    "headers": {
      "Content-Length": "76",
      "Content-Type": "application/json; charset=utf-8",
      "Host": "httpbin.org",
      "X-Amzn-Trace-Id": "Root=1-68620d8c-7453176445a602dd7bb4dc56"
    },
    "json": {
      "email": "<EMAIL>",
      "org": "Org2",
      "username": "John"
    },
    "origin": "**************",
    "url": "https://httpbin.org/post"
  }
}
2025-06-30T09:37:35.5117515+05:30 [Information] Resolving path 'output.content.json.url' using step input/output.
2025-06-30T09:37:35.5118224+05:30 [Information] Resolved JPath 'output.content.json.url' to ''
2025-06-30T09:37:35.5120110+05:30 [Information] Set Value to Step '7175e55d-6a7e-40de-8dfc-19512281ebd1' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:07:34.3038454Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:37:35.5121154+05:30 [Information] Run Asserter : {"Expression1":{"Value":"200","ErrorMesage":null},"Expression2":{"JPath":"output.statusCode","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:37:35.5121665+05:30 [Information] Resolving path 'output.statusCode' using step input/output.
2025-06-30T09:37:35.5122112+05:30 [Information] Resolved JPath 'output.statusCode' to '200'
2025-06-30T09:37:35.5122584+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$step:row.User.Username}}","ErrorMesage":null},"Expression2":{"JPath":"output.content.json.username","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:37:35.5123363+05:30 [Information] Resolved path 'row.User.Username' in step-specific context.
2025-06-30T09:37:35.5123792+05:30 [Information] Resolved JPath '$step:row.User.Username' to 'John'
2025-06-30T09:37:35.5124151+05:30 [Information] Resolved template pattern '{{$step:row.User.Username}}' to 'John' in '{{$step:row.User.Username}}'
2025-06-30T09:37:35.5125201+05:30 [Information] Resolved template '{{$step:row.User.Username}}' to 'John'
2025-06-30T09:37:35.5128055+05:30 [Information] Resolving path 'output.content.json.username' using step input/output.
2025-06-30T09:37:35.5129432+05:30 [Information] Resolved JPath 'output.content.json.username' to 'John'
2025-06-30T09:37:35.5130850+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$step:row.User.Email}}","ErrorMesage":null},"Expression2":{"JPath":"output.content.json.email","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:37:35.5132209+05:30 [Information] Resolved path 'row.User.Email' in step-specific context.
2025-06-30T09:37:35.5133347+05:30 [Information] Resolved JPath '$step:row.User.Email' to '<EMAIL>'
2025-06-30T09:37:35.5134415+05:30 [Information] Resolved template pattern '{{$step:row.User.Email}}' to '<EMAIL>' in '{{$step:row.User.Email}}'
2025-06-30T09:37:35.5135617+05:30 [Information] Resolved template '{{$step:row.User.Email}}' to '<EMAIL>'
2025-06-30T09:37:35.5137508+05:30 [Information] Resolving path 'output.content.json.email' using step input/output.
2025-06-30T09:37:35.5138668+05:30 [Information] Resolved JPath 'output.content.json.email' to '<EMAIL>'
2025-06-30T09:37:35.5140340+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$step:row.OrgName}}","ErrorMesage":null},"Expression2":{"JPath":"output.content.json.org","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:37:35.5142065+05:30 [Information] Resolved path 'row.OrgName' in step-specific context.
2025-06-30T09:37:35.5143313+05:30 [Information] Resolved JPath '$step:row.OrgName' to 'Org2'
2025-06-30T09:37:35.5144260+05:30 [Information] Resolved template pattern '{{$step:row.OrgName}}' to 'Org2' in '{{$step:row.OrgName}}'
2025-06-30T09:37:35.5145263+05:30 [Information] Resolved template '{{$step:row.OrgName}}' to 'Org2'
2025-06-30T09:37:35.5146816+05:30 [Information] Resolving path 'output.content.json.org' using step input/output.
2025-06-30T09:37:35.5147693+05:30 [Information] Resolved JPath 'output.content.json.org' to 'Org2'
2025-06-30T09:37:35.5150144+05:30 [Information] Set Value to Step '7175e55d-6a7e-40de-8dfc-19512281ebd1' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 401,
  "startTime": "2025-06-30T04:07:34.3038454Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:37:35.5153974+05:30 [Information] Step 'Fetching user John details' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "7175e55d-6a7e-40de-8dfc-19512281ebd1",
        "tables": {},
        "variables": {
          "User": {
            "Username": "John",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org2",
          "username": "John",
          "email": "<EMAIL>"
        },
        "name": "Run for Each User",
        "description": "Run step for each user",
        "context": {
          "repeatForTable": "UserTable",
          "row": {
            "User": {
              "Username": "John",
              "Email": "<EMAIL>"
            },
            "OrgName": "Org2"
          },
          "rowIndex": 1
        },
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "John",
            "email": "<EMAIL>",
            "org": "Org2"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:07:40 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"John\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"org\": \"Org2\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "76",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620d8c-7453176445a602dd7bb4dc56"
            },
            "json": {
              "email": "<EMAIL>",
              "org": "Org2",
              "username": "John"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 401,
          "startTime": "2025-06-30T04:07:34.3038454Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaseUrl": "https://httpbin.org"
    },
    "tables": {
      "UserTable": [
        {
          "User": {
            "Username": "Tom",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org1"
        },
        {
          "User": {
            "Username": "John",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org2"
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:37:35.5156261+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:37:35.5157460+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:37:35.5158349+05:30 [Information] No performance data found for step 'Run for Each User' (http)
2025-06-30T09:37:35.5159101+05:30 [Information] [ OK       ]
2025-06-30T09:37:35.5167771+05:30 [Information] [----------] 1 step from Demo HTTP Table Step Iteration (1213 ms total)
