2025-06-30T09:43:36.8376880+05:30 [Information] [----------] 7 steps from Advanced Performance Mathematics
2025-06-30T09:43:36.8465173+05:30 [Information] Set Value to Step 'ddfa83db-573a-488c-9a12-f12b4ade90ae' name:
"Database Under Load"
2025-06-30T09:43:36.8472519+05:30 [Information] [ RUN      ] Advanced Performance Mathematics > Database Under Load
2025-06-30T09:43:36.8488969+05:30 [Information] Executing single instance of step 'Database Under Load'.
2025-06-30T09:43:36.8523307+05:30 [Information] Executing step 'Database Under Load' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "ddfa83db-573a-488c-9a12-f12b4ade90ae",
        "tables": {},
        "variables": {},
        "name": "Database Under Load"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc\\examples",
      "BaseUrl": "https://httpbin.org",
      "DatabaseQueryBaseline": 1,
      "APICallBaseline": 1,
      "FileProcessingBaseline": 1,
      "LoadFactor": 1.5,
      "ConcurrencyPenalty": 1.2,
      "NetworkLatency": 15,
      "P95Multiplier": 2.5,
      "P99Multiplier": 4.0,
      "MaxUserWaitTime": 30000,
      "CriticalPathBudget": 15000
    },
    "tables": {},
    "context": {}
  }
}
2025-06-30T09:43:36.8545212+05:30 [Information] Processing $var: path 'BaseUrl'
2025-06-30T09:43:36.8563258+05:30 [Information] Checking if 'BaseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:43:36.8580637+05:30 [Information] Resolving path 'BaseUrl' using global variables.
2025-06-30T09:43:36.8583564+05:30 [Information] Resolved JPath '$var:BaseUrl' to 'https://httpbin.org'
2025-06-30T09:43:36.8584667+05:30 [Information] Resolved template pattern '{{$var:BaseUrl}}' to 'https://httpbin.org' in '{{$var:BaseUrl}}/post'
2025-06-30T09:43:36.8587167+05:30 [Information] Resolved template '{{$var:BaseUrl}}/post' to 'https://httpbin.org/post'
2025-06-30T09:43:36.8604149+05:30 [Information] Set Value to Step 'ddfa83db-573a-488c-9a12-f12b4ade90ae' input:
{
  "Method": "POST",
  "RequestUri": "https://httpbin.org/post",
  "Headers": {}
}
2025-06-30T09:43:36.8614719+05:30 [Information] Delaying step execution for 100ms
2025-06-30T09:43:38.1694548+05:30 [Information] Set Value to Step 'ddfa83db-573a-488c-9a12-f12b4ade90ae' output:
{
  "statusCode": 200,
  "reasonPhrase": "OK",
  "headers": [
    {
      "Key": "Date",
      "Value": [
        "Mon, 30 Jun 2025 04:13:43 GMT"
      ]
    },
    {
      "Key": "Connection",
      "Value": [
        "keep-alive"
      ]
    },
    {
      "Key": "Server",
      "Value": [
        "gunicorn/19.9.0"
      ]
    },
    {
      "Key": "Access-Control-Allow-Origin",
      "Value": [
        "*"
      ]
    },
    {
      "Key": "Access-Control-Allow-Credentials",
      "Value": [
        "true"
      ]
    }
  ],
  "content": {
    "args": {},
    "data": "",
    "files": {},
    "form": {},
    "headers": {
      "Content-Length": "0",
      "Host": "httpbin.org",
      "X-Amzn-Trace-Id": "Root=1-68620ef7-5a317a090cba59ed0b798e4f"
    },
    "json": null,
    "origin": "**************",
    "url": "https://httpbin.org/post"
  }
}
2025-06-30T09:43:38.1701470+05:30 [Information] Resolving path 'performance.executionTimeMs' using global context.
2025-06-30T09:43:38.1702939+05:30 [Information] Resolved JPath 'performance.executionTimeMs' to ''
2025-06-30T09:43:38.1711076+05:30 [Information] Set Value to Step 'ddfa83db-573a-488c-9a12-f12b4ade90ae' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:13:36.8488636Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:43:38.1732346+05:30 [Information] Run Asserter : {"Expression1":{"JPath":"$curStep:performance.executionTimeMs","ErrorMesage":"Database query too slow under load: {{$curStep:performance.executionTimeMs}}ms vs expected {{$var:DatabaseQueryBaseline * $var:LoadFactor + $var:NetworkLatency}}ms"},"Expression2":{"Value":"{{$var:DatabaseQueryBaseline * $var:LoadFactor + $var:NetworkLatency}}","ErrorMesage":"Database query too slow under load: {{$curStep:performance.executionTimeMs}}ms vs expected {{$var:DatabaseQueryBaseline * $var:LoadFactor + $var:NetworkLatency}}ms"},"ErrorMesage":"Database query too slow under load: {{$curStep:performance.executionTimeMs}}ms vs expected {{$var:DatabaseQueryBaseline * $var:LoadFactor + $var:NetworkLatency}}ms"}
2025-06-30T09:43:38.1735836+05:30 [Information] Resolving path 'performance.executionTimeMs' using $curStep:
2025-06-30T09:43:38.1737345+05:30 [Information] Resolved JPath '$curStep:performance.executionTimeMs' to '0'
2025-06-30T09:43:38.1744591+05:30 [Information] Processing $var: path 'DatabaseQueryBaseline * $var:LoadFactor + $var:NetworkLatency'
2025-06-30T09:43:38.1745732+05:30 [Information] Checking if 'DatabaseQueryBaseline * $var:LoadFactor + $var:NetworkLatency' is mathematical expression: hasArithmetic=True, hasArray=False, hasProperty=False, result=True
2025-06-30T09:43:38.1746430+05:30 [Information] Detected mathematical expression in $var: 'DatabaseQueryBaseline * $var:LoadFactor + $var:NetworkLatency'
2025-06-30T09:43:38.1748644+05:30 [Information] Evaluating mathematical expression: 'DatabaseQueryBaseline * $var:LoadFactor + $var:NetworkLatency'
2025-06-30T09:43:38.1751010+05:30 [Information] Resolving variables in expression: 'DatabaseQueryBaseline * $var:LoadFactor + $var:NetworkLatency'
2025-06-30T09:43:38.1754664+05:30 [Information] Resolved variable 'LoadFactor' from global variables: 1.5
2025-06-30T09:43:38.1755943+05:30 [Information] Resolved variable 'NetworkLatency' from global variables: 15
2025-06-30T09:43:38.1758018+05:30 [Information] Resolved variable 'DatabaseQueryBaseline' from global variables: 1
2025-06-30T09:43:38.1759015+05:30 [Information] Expression after variable resolution: '1 * 1.5 + 15'
2025-06-30T09:43:38.1759606+05:30 [Information] Expression after variable resolution: '1 * 1.5 + 15'
2025-06-30T09:43:38.1768265+05:30 [Information] Mathematical expression 'DatabaseQueryBaseline * $var:LoadFactor + $var:NetworkLatency' evaluated to: 16.5
2025-06-30T09:43:38.1769344+05:30 [Information] Mathematical expression result: '16.5'
2025-06-30T09:43:38.1770127+05:30 [Information] Resolved JPath '$var:DatabaseQueryBaseline * $var:LoadFactor + $var:NetworkLatency' to '16.5'
2025-06-30T09:43:38.1770743+05:30 [Information] Resolved template pattern '{{$var:DatabaseQueryBaseline * $var:LoadFactor + $var:NetworkLatency}}' to '16.5' in '{{$var:DatabaseQueryBaseline * $var:LoadFactor + $var:NetworkLatency}}'
2025-06-30T09:43:38.1773072+05:30 [Information] Resolved template '{{$var:DatabaseQueryBaseline * $var:LoadFactor + $var:NetworkLatency}}' to '16.5'
2025-06-30T09:43:38.1789457+05:30 [Error] Asserter error : {"Expression1":{"JPath":"$curStep:performance.executionTimeMs","ErrorMesage":"Database query too slow under load: {{$curStep:performance.executionTimeMs}}ms vs expected {{$var:DatabaseQueryBaseline * $var:LoadFactor + $var:NetworkLatency}}ms"},"Expression2":{"Value":"{{$var:DatabaseQueryBaseline * $var:LoadFactor + $var:NetworkLatency}}","ErrorMesage":"Database query too slow under load: {{$curStep:performance.executionTimeMs}}ms vs expected {{$var:DatabaseQueryBaseline * $var:LoadFactor + $var:NetworkLatency}}ms"},"ErrorMesage":"Database query too slow under load: {{$curStep:performance.executionTimeMs}}ms vs expected {{$var:DatabaseQueryBaseline * $var:LoadFactor + $var:NetworkLatency}}ms"}
2025-06-30T09:43:38.1804375+05:30 [Error] Assert Failure : Less(0, 16.5)
2025-06-30T09:43:38.1808505+05:30 [Information] Failed to execute step 'Database Under Load': Database query too slow under load: {{$curStep:performance.executionTimeMs}}ms vs expected {{$var:DatabaseQueryBaseline * $var:LoadFactor + $var:NetworkLatency}}ms
2025-06-30T09:43:38.1810343+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:43:38.1811306+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:43:38.1812242+05:30 [Information] No performance data found for step 'Database Under Load' (http)
2025-06-30T09:43:38.1818330+05:30 [Error] Database query too slow under load: {{$curStep:performance.executionTimeMs}}ms vs expected {{$var:DatabaseQueryBaseline * $var:LoadFactor + $var:NetworkLatency}}ms
2025-06-30T09:43:38.1820949+05:30 [Error] [ FAILED   ]
2025-06-30T09:43:38.1831214+05:30 [Information] [----------] 7 steps from Advanced Performance Mathematics (1343 ms total)
