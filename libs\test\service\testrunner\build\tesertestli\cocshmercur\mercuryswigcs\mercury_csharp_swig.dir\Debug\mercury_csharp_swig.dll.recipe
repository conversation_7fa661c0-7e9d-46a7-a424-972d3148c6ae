﻿<?xml version="1.0" encoding="utf-8"?>
<Project>
  <ProjectOutputs>
    <ProjectOutput>
      <FullPath>D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\build\x64\Debug\ZERO_CHECK</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\build\tesertestli\cocshmercur\mercuryswigcs\mercury\cocppbbclie\libsxplatfo\x64\Debug\xplatform</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\build\Debug\mercury_csharp_swig.dll</FullPath>
    </ProjectOutput>
  </ProjectOutputs>
  <ContentFiles />
  <SatelliteDlls />
  <NonRecipeFileRefs />
</Project>