2025-06-30T09:38:12.4728690+05:30 [Information] [----------] 1 step from Docker PS Test
2025-06-30T09:38:12.4735702+05:30 [Information] Set Value to Step '892c459e-dfab-481d-8363-202431ece06c' name:
"Step list containers"
2025-06-30T09:38:12.4737245+05:30 [Information] Set Value to Step '892c459e-dfab-481d-8363-202431ece06c' description:
"Run docker ps command"
2025-06-30T09:38:12.4737969+05:30 [Information] [ RUN      ] Docker PS Test > Step list containers
2025-06-30T09:38:12.4740676+05:30 [Information] Executing single instance of step 'Step list containers'.
2025-06-30T09:38:12.4741951+05:30 [Information] Executing step 'Step list containers' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "892c459e-dfab-481d-8363-202431ece06c",
        "tables": {},
        "variables": {},
        "name": "Step list containers",
        "description": "Run docker ps command"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:38:12.4745200+05:30 [Information] Set Value to Step '892c459e-dfab-481d-8363-202431ece06c' input:
{
  "command": "docker",
  "arguments": [
    "ps",
    "--no-trunc",
    "--filter",
    "status=running"
  ],
  "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
  "printOutput": false
}
2025-06-30T09:38:12.6520847+05:30 [Information] Set Value to Step '892c459e-dfab-481d-8363-202431ece06c' output:
{
  "stdout": "CONTAINER ID   IMAGE     COMMAND   CREATED   STATUS    PORTS     NAMES",
  "stderr": "",
  "exitCode": 0
}
2025-06-30T09:38:12.6522981+05:30 [Information] Set Value to Step '892c459e-dfab-481d-8363-202431ece06c' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:08:12.4740649Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "cmd"
}
2025-06-30T09:38:12.6533619+05:30 [Information] Run Asserter : {"Expression1":{"JPath":"$curStep:$.output.stdout","ErrorMesage":null},"Expression2":{"Value":"CONTAINER ID","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:38:12.6535034+05:30 [Information] Resolving path '$.output.stdout' using $curStep:
2025-06-30T09:38:12.6535984+05:30 [Information] Resolved JPath '$curStep:$.output.stdout' to 'CONTAINER ID   IMAGE     COMMAND   CREATED   STATUS    PORTS     NAMES'
2025-06-30T09:38:12.6540199+05:30 [Information] Set Value to Step '892c459e-dfab-481d-8363-202431ece06c' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 179,
  "startTime": "2025-06-30T04:08:12.4740649Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "cmd"
}
2025-06-30T09:38:12.6541578+05:30 [Information] Step 'Step list containers' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "892c459e-dfab-481d-8363-202431ece06c",
        "tables": {},
        "variables": {},
        "name": "Step list containers",
        "description": "Run docker ps command",
        "input": {
          "command": "docker",
          "arguments": [
            "ps",
            "--no-trunc",
            "--filter",
            "status=running"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "CONTAINER ID   IMAGE     COMMAND   CREATED   STATUS    PORTS     NAMES",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 179,
          "startTime": "2025-06-30T04:08:12.4740649Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:38:12.6542556+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:38:12.6543240+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:38:12.6543711+05:30 [Information] No performance data found for step 'Step list containers' (cmd)
2025-06-30T09:38:12.6544147+05:30 [Information] [ OK       ]
2025-06-30T09:38:12.6547501+05:30 [Information] [----------] 1 step from Docker PS Test (181 ms total)
