/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using Newtonsoft.Json.Linq;
using NUnit.Framework;
using System.Collections.Generic;

namespace TestLib.Tests;

[TestFixture]
public class TestContextResolveTemplateTests
{
    private TestContext _testContext;

    [SetUp]
    public void Setup()
    {
        _testContext = new TestContext();

        // Large test dataset with response objects for completed steps
        _testContext.UpsertName("Comprehensive Test");
        _testContext.Variables.Upsert("access_token", "test_access_token");
        _testContext.Variables.Upsert("sample_text", " Test String ");
        _testContext.Variables.Upsert("encoded_text", "VGVzdCBTdHJpbmc=");

        // Add variables for mathematical expression testing
        _testContext.Variables.Upsert("baselineTime", "100");
        _testContext.Variables.Upsert("lightweightTime", "50");
        _testContext.Variables.Upsert("MaxRegressionPercent", "150");
        _testContext.Variables.Upsert("multiplier", "2.5");
        _testContext.Variables.Upsert("GoodResponseTime", "200");
        _testContext.Variables.Upsert("SLAResponseTime", "500");

        // Add variables for the specific APICallBaseline expression testing
        _testContext.Variables.Upsert("APICallBaseline", "120");
        _testContext.Variables.Upsert("ScalingFactor", "2.0");
        _testContext.Variables.Upsert("EfficiencyTarget", "0.8");
        // Add tables (global and step-specific)
        var globalTable = new List<Dictionary<string, string>>
        {
            new() { { "Username", "user1" }, { "Email", "<EMAIL>" } },
            new() { { "Username", "user2" }, { "Email", "<EMAIL>" } }
        };
        _testContext.Tables.Upsert("GlobalTable", JToken.FromObject(globalTable));
        _testContext.Utils.UpsertContext("row", new JObject
        {
            ["Username"] = "user1",
            ["Email"] = "<EMAIL>"
        });

       var stepTable = new List<Dictionary<string, string>>
        {
            new() { { "Field1", "StepValue1" }, { "Field2", "StepValue2" } }
        };

        _testContext.AddStepInput("step1_id", new JObject
        {
            ["key1"] = "value1",
            ["key2"] = "value2"
        });
        _testContext.AddStepOutput("step1_id", new JObject
        {
            ["statusCode"] = 200,
            ["content"] = new JObject
            {
                ["message"] = "OK",
                ["data"] = "sample data"
            }
        });
        _testContext.SetValueToStep("step1_id", "name", JToken.FromObject("step1"));
        _testContext.AddStepInput("step2", new JObject
        {
            ["key1"] = "value3",
            ["key2"] = "value4"
        });
        _testContext.AddStepOutput("step2", new JObject
        {
            ["statusCode"] = 404,
            ["content"] = new JObject
            {
                ["error"] = "Not Found"
            }
        });
        _testContext.AddStepInput("last_step", new JObject
        {
            ["key1"] = "last_value1",
            ["key2"] = "last_value2"
        });
        _testContext.AddStepOutput("last_step", new JObject
        {
            ["statusCode"] = 202,
            ["content"] = new JObject
            {
                ["accessToken"] = "abcdef123456",
                ["expiry"] = "2025-12-31T23:59:59Z"
            }
        });
        _testContext.AddStepInput("current_step", new JObject
        {
            ["key1"] = "current_value1",
            ["key2"] = "current_value2"
        });
        _testContext.Tables.Upsert("StepTable", JToken.FromObject(stepTable), "current_step");
        _testContext.Utils.UpsertContext("row", new JObject
        {
            ["Username"] = "user2",
            ["Email"] = "<EMAIL>"
        }, "current_step");
        _testContext.AddStepInput("step4", new JObject
        {
            ["key1"] = "value5",
            ["key2"] = "value6"
        });

    }

    [TestCase("Static text with no placeholders", 
              "Static text with no placeholders", 
              TestName = "NoPlaceholders")]
    [TestCase("Test name: {{test.name}}", 
              "Test name: Comprehensive Test", 
              TestName = "ResolveTestNamePlaceholder")]
    [TestCase("First step name: {{$.test.steps[0].name}}", 
              "First step name: step1", 
              TestName = "ResolveFirstStepNamePlaceholder")]
    [TestCase("Second step input key1: {{test.steps[1].input.key1}}", 
              "Second step input key1: value3", 
              TestName = "ResolveSecondStepInputKey1Placeholder")]
    [TestCase("Current step input key2: {{$curStep:$.input.key2}}", 
              "Current step input key2: current_value2", 
              TestName = "ResolveCurrentStepInputKey2Placeholder")]
    [TestCase("Last step access token: {{$lastStep:$.output.content.accessToken}}", 
              "Last step access token: abcdef123456", 
              TestName = "ResolveLastStepAccessTokenPlaceholder")]
    [TestCase("Access token variable: {{$var:access_token}}", 
              "Access token variable: test_access_token", 
              TestName = "ResolveVariablePlaceholder")]
    [TestCase("Non-existent key: {{test.steps[0].input.nonexistent}}", 
              "Non-existent key: {{test.steps[0].input.nonexistent}}", 
              TestName = "ResolveNonExistentKeyPlaceholder")]
    [TestCase("Mixed content: Start {{$.test.name}} middle {{$curStep:$.input.key1}} end {{$var:access_token}}", 
              "Mixed content: Start Comprehensive Test middle current_value1 end test_access_token", 
              TestName = "ResolveMixedPlaceholders")]
    [TestCase("Unresolved template: {{unresolved}}", 
              "Unresolved template: {{unresolved}}", 
              TestName = "UnresolvedTemplatePlaceholder")]
    [TestCase("Current step input key2: {{input.key2}}", 
              "Current step input key2: current_value2", 
              TestName = "ResolvedTemplatePlaceholderCurrentInput")]
    [TestCase("URLEncode variable: {{URLEncode($var:access_token)}}", 
              "URLEncode variable: test_access_token", 
              TestName = "URLEncodeVariable")]
    [TestCase("URLEncode content: {{URLEncode($.test.steps[0].input.key1)}}", 
              "URLEncode content: value1", 
              TestName = "URLEncodeContent")]
    [TestCase("String conversion: {{String($var:access_token)}}", 
              "String conversion: test_access_token", 
              TestName = "StringConversionVariable")]
    [TestCase("Unresolved function: {{UnknownFunction($var:access_token)}}", 
              "Unresolved function: test_access_token", 
              TestName = "UnresolvedFunction")]
    [TestCase("ToUpper: {{ToUpper($var:sample_text)}}", 
              "ToUpper:  TEST STRING ", 
              TestName = "ToUpperFunction")]
    [TestCase("ToLower: {{ToLower($var:sample_text)}}", 
              "ToLower:  test string ", 
              TestName = "ToLowerFunction")]
    [TestCase("Trim: {{Trim($var:sample_text)}}", 
              "Trim: Test String", 
              TestName = "TrimFunction")]
    [TestCase("Base64Encode: {{Base64Encode($var:sample_text)}}", 
              "Base64Encode: IFRlc3QgU3RyaW5nIA==", 
              TestName = "Base64EncodeFunction")]
    [TestCase("Base64Decode: {{Base64Decode($var:encoded_text)}}", 
              "Base64Decode: Test String", 
              TestName = "Base64DecodeFunction")]
    [TestCase("Reverse: {{Reverse($var:sample_text)}}", 
              "Reverse:  gnirtS tseT ", 
              TestName = "ReverseFunction")]
    [TestCase("Mixed functions: Start {{URLEncode($.test.name)}} middle {{string($curStep:$.input.key1)}} end {{$var:access_token}}", 
              "Mixed functions: Start Comprehensive%20Test middle current_value1 end test_access_token", 
              TestName = "MixedFunctions")]
    [TestCase("Access global table username: {{$table:GlobalTable[0].Username}}",
              "Access global table username: user1",
              TestName = "ResolveGlobalTableUsername")]
    [TestCase("Access global table email: {{$table:GlobalTable[1].Email}}",
              "Access global table email: <EMAIL>",
              TestName = "ResolveGlobalTableEmail")]
    [TestCase("Access step-specific table field1: {{$table:StepTable[0].Field1}}",
              "Access step-specific table field1: StepValue1",
              TestName = "ResolveStepSpecificTableField1")]
    [TestCase("Access non-existent table: {{$table:NonExistentTable[0].Key}}",
              "Access non-existent table: {{$table:NonExistentTable[0].Key}}",
              TestName = "ResolveNonExistentTable")]
    [TestCase("Mixed template: User {{$table:GlobalTable[0].Username}}, Email {{$table:GlobalTable[0].Email}}",
              "Mixed template: User user1, Email <EMAIL>",
              TestName = "ResolveMixedTemplateWithGlobalTable")]
    [TestCase("Access test context variable: {{$test:row.Username}}/{{$test:row.Email}}",
              "Access test context variable: user1/<EMAIL>",
              TestName = "ResolveTestContextRow")]
    [TestCase("Access step context variable: {{$step:row.Username}}/{{$step:row.Email}}",
              "Access step context variable: user2/<EMAIL>",
              TestName = "ResolveTestStepContextRow")]
    // Mathematical expression test cases
    [TestCase("Simple multiplication: {{$var:baselineTime * 1.5}}",
              "Simple multiplication: 150",
              TestName = "SimpleMathematicalMultiplication")]
    [TestCase("Variable multiplication: {{$var:lightweightTime * 10}}",
              "Variable multiplication: 500",
              TestName = "VariableMultiplication")]
    [TestCase("Complex expression: {{$var:baselineTime * $var:MaxRegressionPercent / 100}}",
              "Complex expression: 150",
              TestName = "ComplexMathematicalExpression")]
    [TestCase("Addition expression: {{$var:baselineTime + 50}}",
              "Addition expression: 150",
              TestName = "MathematicalAddition")]
    [TestCase("Subtraction expression: {{$var:GoodResponseTime - 50}}",
              "Subtraction expression: 150",
              TestName = "MathematicalSubtraction")]
    [TestCase("Division expression: {{$var:SLAResponseTime / 2}}",
              "Division expression: 250",
              TestName = "MathematicalDivision")]
    [TestCase("Parentheses expression: {{$var:(baselineTime + lightweightTime) * 2}}",
              "Parentheses expression: 300",
              TestName = "MathematicalParentheses")]
    [TestCase("Decimal multiplication: {{$var:baselineTime * 2.5}}",
              "Decimal multiplication: 250",
              TestName = "DecimalMultiplication")]
    [TestCase("Multiple variables: {{$var:baselineTime + lightweightTime}}",
              "Multiple variables: 150",
              TestName = "MultipleVariableAddition")]
    [TestCase("Nested expression: {{$var:baselineTime * (1 + $var:MaxRegressionPercent / 100)}}",
              "Nested expression: 250",
              TestName = "NestedMathematicalExpression")]
    // Test the specific APICallBaseline expression from the user's question
    [TestCase("API Scaling: {{$var:APICallBaseline * $var:ScalingFactor / $var:EfficiencyTarget}}",
              "API Scaling: 300",
              TestName = "APICallBaselineScalingExpression")]
    [TestCase("Performance threshold: {{$var:APICallBaseline * $var:ScalingFactor / $var:EfficiencyTarget}}ms",
              "Performance threshold: 300ms",
              TestName = "APICallBaselineWithUnits")]
    [TestCase("Complex scaling: baseline={{$var:APICallBaseline}}, factor={{$var:ScalingFactor}}, efficiency={{$var:EfficiencyTarget}}, result={{$var:APICallBaseline * $var:ScalingFactor / $var:EfficiencyTarget}}",
              "Complex scaling: baseline=120, factor=2, efficiency=0.8, result=300",
              TestName = "MultipleTemplatesWithAPICallBaseline")]
    public void TestResolveTemplate(string input, string expected)
    {
        // Act
        var result = _testContext.Resolve<string>(input);

        // Assert
        Assert.AreEqual(expected, result);
    }
}
