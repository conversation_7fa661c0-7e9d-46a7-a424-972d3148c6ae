2025-06-30T09:21:45.0234996+05:30 [Information] Global variables set to: baseUrl: https://httpbin.org, serverIp: 127.0.0.1, serverPort: 60606
2025-06-30T09:21:45.0384712+05:30 [Information] Loading tests from D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc with pattern *math-expressions-simple.yaml
2025-06-30T09:21:45.0426853+05:30 [Information] Loading test from D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc\math-expressions-simple.yaml
2025-06-30T09:21:45.0545078+05:30 [Information] Global variables set to: 
2025-06-30T09:21:45.0556108+05:30 [Information] [==========] Running 1 test from Yaml files
2025-06-30T09:21:45.0567153+05:30 [Information] [-------------------------------------    Startup     -------------------------------------]
2025-06-30T09:21:45.0772371+05:30 [Information] [----------] 1 step from Startup
2025-06-30T09:21:45.0798402+05:30 [Information] [ RUN      ] Startup > Initialize Database
2025-06-30T09:21:46.4080400+05:30 [Information] [ OK       ]
2025-06-30T09:21:46.4093205+05:30 [Information] [----------] 1 step from Startup (1330 ms total)
2025-06-30T09:21:46.4123357+05:30 [Information] Global variables set to: curDir: D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc, baseUrl: https://httpbin.org, serverIp: 127.0.0.1, serverPort: 60606, dbInitStatus: initialize_db
2025-06-30T09:21:46.4128801+05:30 [Information] [------------------------------------- Test Execution -------------------------------------]
2025-06-30T09:21:46.4143496+05:30 [Information] [----------] 6 steps from Simple Mathematical Expressions Demo
2025-06-30T09:21:46.4157439+05:30 [Information] [ RUN      ] Simple Mathematical Expressions Demo > Fast Baseline Operation
2025-06-30T09:21:46.5122404+05:30 [Information] [ OK       ]
2025-06-30T09:21:46.5126552+05:30 [Information] [ RUN      ] Simple Mathematical Expressions Demo > Regression Threshold Test
2025-06-30T09:21:47.1931707+05:30 [Error] Assert Failure : Less(150, 0)
2025-06-30T09:21:47.1939792+05:30 [Error] Performance regression detected: {{$curStep:performance.executionTimeMs}}ms vs allowed {{$var:MathBaseline * $var:MaxRegressionPercent / 100}}ms ({{$var:MathBaseline}}ms * {{$var:MaxRegressionPercent}}% / 100)
2025-06-30T09:21:47.1940909+05:30 [Error] [ FAILED   ]
2025-06-30T09:21:47.1946783+05:30 [Information] [----------] 6 steps from Simple Mathematical Expressions Demo (779 ms total)
2025-06-30T09:21:47.1952252+05:30 [Information] [-------------------------------------    Teardown    -------------------------------------]
2025-06-30T09:21:47.1963212+05:30 [Information] [----------] 1 step from Teardown
2025-06-30T09:21:47.1968613+05:30 [Information] [ RUN      ] Teardown > Clean up Database
2025-06-30T09:21:48.0591254+05:30 [Information] [ OK       ]
2025-06-30T09:21:48.0613796+05:30 [Information] [----------] 1 step from Teardown (863 ms total)
2025-06-30T09:21:48.0628952+05:30 [Information] Global variables set to: curDir: D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc, baseUrl: https://httpbin.org, serverIp: 127.0.0.1, serverPort: 60606, dbInitStatus: initialize_db
2025-06-30T09:21:48.0631299+05:30 [Information] [-------------------------------------  Test Results  -------------------------------------]
2025-06-30T09:21:48.0640380+05:30 [Information] [==========] 1 test from Yaml files ran. (3009 ms total)
2025-06-30T09:21:48.0655549+05:30 [Information] [  PASSED  ] 0 tests.
2025-06-30T09:21:48.0658586+05:30 [Error] [  FAILED  ] 1 test, listed below:
2025-06-30T09:21:48.0664954+05:30 [Error] [  FAILED  ] Simple Mathematical Expressions Demo
