/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

#pragma once

#include "RandomUtils.h"
#include "ProtoUtils.h"
#include "PatientDataMaker.h"
#include "RefractionDataMaker.h"
#include "VisualAcuityDataMaker.h"
#include "../generated/SubjectiveExaminationReportDataMaker.pb.h"

namespace testdatamaker {

namespace type = alcon::interop::type;
namespace serdmp = testdatamaker::subjectiveexaminationreportdatamaker;

using ctu = alcon::interop::type::CoreTypesUtil;
using pddm = testdatamaker::PatientDataMaker;
using vadm = testdatamaker::VisualAcuityDataMaker;
using rdm = testdatamaker::RefractionDataMaker;

class SubjectiveExaminationReportDataMaker {
public:
    using subjectiveexaminationreportdatamaker_config_t =
        dmb::datamaker_config_t<type::SubjectiveExaminationReport, serdmp::SubjectiveExaminationReportParams>;
    using subjectiveexaminationreportdatamaker_config_Builder =
        dmb::datamaker_config_Builder<type::SubjectiveExaminationReport, serdmp::SubjectiveExaminationReportParams>;

    static std::optional<type::SubjectiveExaminationReport> make(const subjectiveexaminationreportdatamaker_config_t& config);
    static std::optional<type::SubjectiveExaminationReport> make(const serdmp::SubjectiveExaminationReportConfig& config);
    static std::vector<type::SubjectiveExaminationReport> extractSubjectiveExaminationReportFromDb(const mpdb::InMultiProtobufDb& data);

private:

    static type::SubjectiveExaminationReport makeRandom(const serdmp::SubjectiveExaminationReportParams& params);
    static type::SubjectiveExaminationReport makeBasedOn(
        const std::optional<type::SubjectiveExaminationReport>& seed,
        const serdmp::SubjectiveExaminationReportParams& params);

    static std::vector <type::VisualAcuityAdditionalAssessment> makeVisualAcuitiesAdditionalAssessments(const int& count, const vadmp::VisualAcuityParams& visualAcuityArgs);
    static std::vector <type::Refraction> makeRefractions(const int& count, const rdmp::RefractionConfig& refractionConfig);
};
} // testdatamaker
