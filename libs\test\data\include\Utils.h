/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

#pragma once

#include "ProtoUtils.h"
#include <google/protobuf/util/json_util.h>

namespace testdatamaker {

class Utils {
public:
    static std::filesystem::path etcPath;
    static std::string toString(uint64_t n, int fixedlength = 20);
    static std::string protoToString(const google::protobuf::Message& message);

    static std::random_device g_RDev;
    static std::default_random_engine g_REng;

    template<typename Item>
    using csv_item_parser_t =
        std::function <std::optional<Item>(const std::unordered_map<std::string, uint32_t>& columns,
                                           const char* line, int lineIndx)>;

    template<typename Item>
    static std::vector<Item>
        readCsv(std::vector <std::filesystem::path> dataSets, csv_item_parser_t<Item> lineParser, bool header = true) {
        std::vector<Item> items;

        for (auto& dataSet : dataSets) {
            std::ifstream csv(dataSet);
            if (!csv.is_open()) {
                continue;
            }

            std::unordered_map<std::string, uint32_t> columns;
            auto csvIndex = [&columns](const std::string& name) {
                return columns.at(name);
            };
            const int maxLineSize = 4096;
            char buffer[maxLineSize];
            int lineIndex = 0;

            // setup the data header info
            if (header) {
                csv.getline(buffer, maxLineSize);
                lineIndex++;
                std::vector<std::string> headers = xplat::xSplit(buffer, ',');
                for (uint32_t i = 0; i < headers.size(); i++) {
                    columns[headers[i]] = i;
                }
            }

            for (; !csv.getline(buffer, maxLineSize).eof();) {
                lineIndex++;
                auto item = lineParser(columns, buffer, lineIndex);
                if (item) {
                    items.push_back(*item);
                }
            }
        }

        return items;
    }

    template <typename Type> 
    static std::optional<Type> 
        getMessageFromJson(const std::filesystem::path& jsonPath) {
        std::ifstream file(jsonPath);
        if (!file.is_open()) {
            return std::nullopt;
        }

        std::string jsonStr((std::istreambuf_iterator<char>(file)), std::istreambuf_iterator<char>());

        Type message;
        google::protobuf::util::JsonParseOptions options;
        options.ignore_unknown_fields = true;

        auto status = google::protobuf::util::JsonStringToMessage(jsonStr, &message, options);
        if (!status.ok()) {
            return std::nullopt;
        }

        return message;
    }
};

} // testdatamaker
