/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Text.RegularExpressions;

namespace TestLib;

/// <summary>
/// Provides functionality for resolving JPaths, templates, and placeholders within a JSON structure.
/// </summary>
public class JPathResolver
{
    // Private Fields
    private readonly TestContext _context;
    private const string PrefixCurStep = "$curStep:";
    private const string PrefixLastStep = "$lastStep:";
    private const string PrefixVar = "$var:";
    private const string PrefixTable = "$table:";
    private const string StepOutput = "output.";
    private const string StepInput = "input.";
    private const string PrefixTestContext = "$test:";
    private const string PrefixStepContext = "$step:";

    // Constructor

    /// <summary>
    /// Initializes a new instance of the <see cref="JPathResolver"/> class.
    /// </summary>
    /// <param name="context">The test context instance.</param>
    public JPathResolver(TestContext context)
    {
        _context = context;
    }

    // Public Methods

    /// <summary>
    /// Resolves and retrieves a JToken based on the input value.
    /// </summary>
    /// <param name="value">The value to resolve.</param>
    /// <returns>The resolved JToken.</returns>
    public JToken Resolve(object value)
    {
        if (value == null)
        {
            return JValue.CreateNull();
        }

        if (value is JToken token)
        {
            var resolvedValue = ResolveTemplate(token.ToString());
            return TestUtils.IsJson(resolvedValue) ? JToken.Parse(resolvedValue) : TestUtils.ParseLiteral(resolvedValue);
        }

        if (value is string strValue)
        {
            var resolvedValue = ResolveTemplate(strValue);
            if (resolvedValue.StartsWith("JPath:"))
            {
                string jPathExpression = resolvedValue.Replace("JPath:", "").Trim();
                return ResolveJPath(jPathExpression);
            }

            return TestUtils.IsJson(resolvedValue) ? JToken.Parse(resolvedValue) : TestUtils.ParseLiteral(resolvedValue);
        }

        if (value is int or float or bool or double or decimal)
        {
            return new JValue(value);
        }

        return JToken.FromObject(value);
    }

    /// <summary>
    /// Resolves and retrieves a JToken based on the provided JSON path.
    /// </summary>
    /// <param name="jPath">The JSON path to resolve.</param>
    /// <returns>The resolved JToken, or null if the path cannot be resolved.</returns>
    public JToken ResolveJPath(string jPath)
    {
        if (string.IsNullOrEmpty(jPath))
        {
            return null;
        }

        var (resolvedPath, context) = ResolvePath(jPath);
        var result = context?.SelectToken(resolvedPath);
        TestLogger.Trace($"Resolved JPath '{jPath}' to '{result}'");
        return result;
    }

    /// <summary>
    /// Resolves a resource to a specified type by processing placeholders and JSON paths.
    /// </summary>
    /// <typeparam name="T">The target type to resolve to.</typeparam>
    /// <param name="resource">The resource to resolve.</param>
    /// <returns>The resolved object of the target type.</returns>
    public T Resolve<T>(object resource) where T : class
    {
        switch (resource)
        {
            case string str:
                {
                    var resolvedValue = ResolveTemplate(str);
                    if (resolvedValue.StartsWith("JPath:"))
                    {
                        string jPathExpression = resolvedValue.Replace("JPath:", "").Trim();
                        return ResolveJPath(jPathExpression) as T;
                    }
                    return resolvedValue as T;
                }

            case JToken jToken:
                string resolvedJson = ResolveTemplate(jToken.ToString());
                return JToken.Parse(resolvedJson).ToObject<T>();

            default:
                string serializedObject = JsonConvert.SerializeObject(resource);
                string resolvedObject = ResolveTemplate(serializedObject);
                return JsonConvert.DeserializeObject<T>(resolvedObject);
        }
    }

    // Private Methods

    /// <summary>
    /// Resolves placeholders in the format {{JsonPath}} within a given string.
    /// </summary>
    /// <param name="input">The input string containing placeholders.</param>
    /// <returns>The resolved string with placeholders replaced.</returns>
    private string ResolveTemplate(string input)
    {
        if (string.IsNullOrEmpty(input))
        {
            return input;
        }

        var jsonPathPattern = @"\{\{(?:(\w+)\()?([^\}]+)(?(1)\))\}\}";
        var result = Regex.Replace(input, jsonPathPattern, match =>
        {
            var conversionFunction = match.Groups[1].Success ? match.Groups[1].Value.Trim() : null;
            var path = match.Groups[2].Value.Trim();

            var token = ResolveJPath(path);
            var resolvedToken = token != null ? token.ToString() : match.Value;

            if (!string.IsNullOrEmpty(conversionFunction))
            {
                resolvedToken = ContextUtils.ApplyConversionFunction(conversionFunction, resolvedToken);
            }

            TestLogger.Trace($"Resolved template pattern '{match.Value}' to '{resolvedToken}' in '{input}'");
            return resolvedToken;
        });

        if (input != result)
        {
            TestLogger.Trace($"Resolved template '{input}' to '{result}'");
        }

        return result;
    }

    /// <summary>
    /// Resolves the JSON path, handling special prefixes like $curStep: for the current step context,
    /// $lastStep: for the previous step context, $var: for variables, and $table: for tables.
    /// </summary>
    /// <param name="path">The JSON path to resolve.</param>
    /// <returns>A tuple containing the resolved path and the associated context.</returns>
    private (string path, JToken context) ResolvePath(string path)
    {
        if (path.StartsWith(PrefixCurStep))
        {
            path = path[PrefixCurStep.Length..];
            TestLogger.Trace($"Resolving path '{path}' using $curStep:");
            return (path, _context.Data.GetCurrentStep());
        }

        if (path.StartsWith(PrefixLastStep))
        {
            path = path[PrefixLastStep.Length..];
            TestLogger.Trace($"Resolving path '{path}' using $lastStep:");
            return (path, _context.Data.GetLastStep());
        }

        if (path.StartsWith(PrefixVar))
        {
            path = path[PrefixVar.Length..];
            TestLogger.Trace($"Processing $var: path '{path}'");

            // Check if this is a mathematical expression (contains operators)
            if (IsMathematicalExpression(path))
            {
                TestLogger.Trace($"Detected mathematical expression in $var: '{path}'");
                var result = ContextUtils.EvaluateMathematicalExpression(path, _context);
                TestLogger.Trace($"Mathematical expression result: '{result}'");

                // Create a temporary JObject to return the result
                var tempResult = new JObject { ["result"] = result };
                return ("result", tempResult);
            }

            var currentStep = _context.Data.GetCurrentStep();
            if (currentStep?["variables"] is JObject stepVariables)
            {
                var result = stepVariables?.SelectToken(path);
                if (result != null)
                {
                    TestLogger.Trace($"Resolved path '{path}' in step-specific variables.");
                    return (path, stepVariables);
                }
            }

            TestLogger.Trace($"Resolving path '{path}' using global variables.");
            return (path, _context.Data.VariablesNode);
        }

        if (path.StartsWith(PrefixTable))
        {
            path = path[PrefixTable.Length..];

            var currentStep = _context.Data.GetCurrentStep();
            if (currentStep?["tables"] is JObject stepTables)
            {
                var result = stepTables?.SelectToken(path);
                if (result != null)
                {
                    TestLogger.Trace($"Resolved path '{path}' in step-specific tables.");
                    return (path, stepTables);
                }
            }

            TestLogger.Trace($"Resolving path '{path}' using global tables.");
            return (path, _context.Data.TablesNode);
        }

        if (path.StartsWith(StepOutput) || path.StartsWith(StepInput))
        {
            TestLogger.Trace($"Resolving path '{path}' using step input/output.");
            return (path, _context.Data.GetCurrentStep());
        }

        if (path.StartsWith(PrefixTestContext))
        {
            path = path[PrefixTestContext.Length..];

            TestLogger.Trace($"Resolving path '{path}' using test context.");
            return (path, _context.Data.ContextNode);
        }

        if (path.StartsWith(PrefixStepContext))
        {
            path = path[PrefixStepContext.Length..];

            var currentStep = _context.Data.GetCurrentStep();
            if (currentStep?["context"] is JObject stepContextVariables)
            {
                var result = stepContextVariables?.SelectToken(path);
                if (result != null)
                {
                    TestLogger.Trace($"Resolved path '{path}' in step-specific context.");
                    return (path, stepContextVariables);
                }
            }

            TestLogger.Trace($"Resolving path '{path}' using test context.");
            return (path, _context.Data.ContextNode);
        }

        TestLogger.Trace($"Resolving path '{path}' using global context.");
        return (path, _context.Data.RootNode);
    }

    /// <summary>
    /// Determines if a path contains mathematical expressions by checking for arithmetic operators.
    /// </summary>
    /// <param name="path">The path to check.</param>
    /// <returns>True if the path contains mathematical operators, false otherwise.</returns>
    private static bool IsMathematicalExpression(string path)
    {
        // Check for arithmetic operators that indicate a mathematical expression
        bool hasArithmeticOperators = Regex.IsMatch(path, @"[+\-*/()]");

        // Exclude JPath array syntax like [0] or [1], but allow decimal numbers like 1.5
        bool hasJPathArraySyntax = Regex.IsMatch(path, @"\[[0-9]+\]");

        // Exclude JPath property access like .property, but allow decimal numbers
        bool hasJPathPropertyAccess = Regex.IsMatch(path, @"\.[a-zA-Z_][a-zA-Z0-9_]*");

        bool isExpression = hasArithmeticOperators && !hasJPathArraySyntax && !hasJPathPropertyAccess;

        TestLogger.Trace($"Checking if '{path}' is mathematical expression: hasArithmetic={hasArithmeticOperators}, hasArray={hasJPathArraySyntax}, hasProperty={hasJPathPropertyAccess}, result={isExpression}");

        return isExpression;
    }

}
