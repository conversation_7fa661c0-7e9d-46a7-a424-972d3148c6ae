/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using Newtonsoft.Json;
using System;

namespace TestLib.Expressions;

public abstract class BooleanBinaryExpression(IExpression expr1, IExpression expr2, string errorMessage = null) : IExpression
{
    [JsonProperty(PropertyName = "Expression1")]
    protected readonly IExpression _expr1 = expr1; 
    [JsonProperty(PropertyName = "Expression2")]
    protected readonly IExpression _expr2 = expr2;
    [JsonProperty(PropertyName = "ErrorMesage")]
    protected readonly string _errorMessage = errorMessage;

    protected object _lastEvaluatedValue1;
    protected object _lastEvaluatedValue2;
   
    public object Evaluate(TestContext context)
    {
        if (_expr1 == null || _expr2 == null)
        {
            throw new InvalidOperationException("Binary expression must have two operands.");
        }
        _lastEvaluatedValue1 = _expr1.Evaluate(context);
        _lastEvaluatedValue2 = _expr2.Evaluate(context);
        return DoEvaluate(_lastEvaluatedValue1, _lastEvaluatedValue2);
    }

    internal abstract bool DoEvaluate(object val1, object val2);
    public abstract string GetErrorMessage();
}