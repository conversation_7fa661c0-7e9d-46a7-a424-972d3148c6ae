syntax = "proto3";

package testdatamaker.surgeryreportdatamaker;

import "alcon/interop/type/Ophthalmic.proto";
import "alcon/interop/type/CoreTypes.proto";
import "alcon/interop/type/Reports.proto";
import "google/protobuf/timestamp.proto";
import "ProtoUtils.proto";
import "PatientDataMaker.proto";
import "RefractiveTreatmentDataMaker.proto";
import "CataractTreatmentDataMaker.proto";
import "CoreTypesDataMaker.proto";

message OtherTreatmentArgs {
    optional alcon.interop.type.OtherTreatment.OtherTreatmentType type = 1;
    optional alcon.interop.type.Identifier surgeonId = 2;
}

message SurgeryReportParams {
   enum TreatmentType {
        REFRACTIVE = 0;
        CATARACT = 1;
        OTHER = 2;
    }

    optional protoutils.RevisionArgs revisionArgs = 1;
    optional alcon.interop.type.OdOs eye = 2;
    optional string description = 3;
    optional protoutils.NotesArgs notesArgs = 4;
    optional google.protobuf.Timestamp treatmentDate = 5;
    optional alcon.interop.type.Identifier surgeryCenterId = 6;
    optional alcon.interop.type.Identifier surgeonId = 7;
    optional alcon.interop.type.Identifier replacesSurgeryId = 8;
    optional alcon.interop.type.Identifier basedOnPlanId = 9;
    optional TreatmentType treatmentType = 10;
    optional patientdatamaker.PatientParams patientArgs = 11;
    optional refractivetreatmentdatamaker.RefractiveTreatmentConfig refractiveTreatmentConfig = 12;
    optional cataracttreatmentdatamaker.CataractTreatmentConfig cataractTreatmentConfig = 13;
    optional OtherTreatmentArgs otherArgs = 14;
    optional alcon.interop.type.FileResource pdfDocument = 15;
}

message SurgeryReportConfig {
    coretypes.GeneratingPolicy policy = 1;
    optional bool exists = 2;
    SurgeryReportParams params = 3;
    alcon.interop.type.SurgeryReport seed = 4;
}