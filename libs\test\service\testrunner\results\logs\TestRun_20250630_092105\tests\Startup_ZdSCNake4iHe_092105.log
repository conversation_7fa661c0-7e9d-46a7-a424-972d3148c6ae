2025-06-30T09:21:05.3269379+05:30 [Information] [----------] 1 step from Startup
2025-06-30T09:21:05.3296656+05:30 [Information] Set Value to Step '1bf12445-89f7-48df-a869-69b6abd06e4b' name:
"Initialize Database"
2025-06-30T09:21:05.3299556+05:30 [Information] Set Value to Step '1bf12445-89f7-48df-a869-69b6abd06e4b' description:
"Setup initial database state"
2025-06-30T09:21:05.3302399+05:30 [Information] [ RUN      ] Startup > Initialize Database
2025-06-30T09:21:05.3314193+05:30 [Information] Executing single instance of step 'Initialize Database'.
2025-06-30T09:21:05.3326000+05:30 [Information] Executing step 'Initialize Database' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "1bf12445-89f7-48df-a869-69b6abd06e4b",
        "tables": {},
        "variables": {},
        "name": "Initialize Database",
        "description": "Setup initial database state"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:21:05.3343214+05:30 [Information] Processing $var: path 'baseUrl'
2025-06-30T09:21:05.3357944+05:30 [Information] Checking if 'baseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:21:05.3373140+05:30 [Information] Resolving path 'baseUrl' using global variables.
2025-06-30T09:21:05.3375236+05:30 [Information] Resolved JPath '$var:baseUrl' to 'https://httpbin.org'
2025-06-30T09:21:05.3376121+05:30 [Information] Resolved template pattern '{{$var:baseUrl}}' to 'https://httpbin.org' in '{{$var:baseUrl}}/post'
2025-06-30T09:21:05.3378530+05:30 [Information] Resolved template '{{$var:baseUrl}}/post' to 'https://httpbin.org/post'
2025-06-30T09:21:05.3400335+05:30 [Information] Set Value to Step '1bf12445-89f7-48df-a869-69b6abd06e4b' input:
{
  "Method": "POST",
  "RequestUri": "https://httpbin.org/post",
  "Headers": {
    "Content-Type": "application/json"
  },
  "Content": {
    "action": "initialize_db"
  }
}
2025-06-30T09:21:06.5224290+05:30 [Information] Set Value to Step '1bf12445-89f7-48df-a869-69b6abd06e4b' output:
{
  "statusCode": 200,
  "reasonPhrase": "OK",
  "headers": [
    {
      "Key": "Date",
      "Value": [
        "Mon, 30 Jun 2025 03:51:11 GMT"
      ]
    },
    {
      "Key": "Connection",
      "Value": [
        "keep-alive"
      ]
    },
    {
      "Key": "Server",
      "Value": [
        "gunicorn/19.9.0"
      ]
    },
    {
      "Key": "Access-Control-Allow-Origin",
      "Value": [
        "*"
      ]
    },
    {
      "Key": "Access-Control-Allow-Credentials",
      "Value": [
        "true"
      ]
    }
  ],
  "content": {
    "args": {},
    "data": "{\r\n  \"action\": \"initialize_db\"\r\n}",
    "files": {},
    "form": {},
    "headers": {
      "Content-Length": "33",
      "Content-Type": "application/json; charset=utf-8",
      "Host": "httpbin.org",
      "X-Amzn-Trace-Id": "Root=1-686209af-5c39d3c6513ad089194c3b73"
    },
    "json": {
      "action": "initialize_db"
    },
    "origin": "**************",
    "url": "https://httpbin.org/post"
  }
}
2025-06-30T09:21:06.5229706+05:30 [Information] Resolving path 'output.content.json.action' using step input/output.
2025-06-30T09:21:06.5231453+05:30 [Information] Resolved JPath 'output.content.json.action' to 'initialize_db'
2025-06-30T09:21:06.5241693+05:30 [Information] Set Value to Step '1bf12445-89f7-48df-a869-69b6abd06e4b' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T03:51:05.3313917Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:21:06.5267285+05:30 [Information] Run Asserter : {"Expression1":{"JPath":"$curStep:output.content.json.action","ErrorMesage":null},"Expression2":{"Value":null,"ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:21:06.5271242+05:30 [Information] Resolving path 'output.content.json.action' using $curStep:
2025-06-30T09:21:06.5272579+05:30 [Information] Resolved JPath '$curStep:output.content.json.action' to 'initialize_db'
2025-06-30T09:21:06.5286711+05:30 [Information] Error in EqualExpression: The method or operation is not implemented.
2025-06-30T09:21:06.5296250+05:30 [Information] Set Value to Step '1bf12445-89f7-48df-a869-69b6abd06e4b' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 1188,
  "startTime": "2025-06-30T03:51:05.3313917Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:21:06.5298136+05:30 [Information] Step 'Initialize Database' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "1bf12445-89f7-48df-a869-69b6abd06e4b",
        "tables": {},
        "variables": {},
        "name": "Initialize Database",
        "description": "Setup initial database state",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "action": "initialize_db"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 03:51:11 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"action\": \"initialize_db\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "33",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-686209af-5c39d3c6513ad089194c3b73"
            },
            "json": {
              "action": "initialize_db"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 1188,
          "startTime": "2025-06-30T03:51:05.3313917Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:21:06.5299816+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:21:06.5300874+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:21:06.5301495+05:30 [Information] No performance data found for step 'Initialize Database' (http)
2025-06-30T09:21:06.5304830+05:30 [Information] [ OK       ]
2025-06-30T09:21:06.5317151+05:30 [Information] [----------] 1 step from Startup (1203 ms total)
