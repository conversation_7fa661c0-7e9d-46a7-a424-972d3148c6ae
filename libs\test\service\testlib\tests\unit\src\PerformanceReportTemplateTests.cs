/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using TestLib.Performance;
using static TestLib.Test;

namespace TestLib.Tests;

[TestFixture]
public class PerformanceReportTemplateTests
{
    private PerformanceReportTemplate _template;

    [SetUp]
    public void Setup()
    {
        _template = new PerformanceReportTemplate();
    }

    [Test]
    public void Generate_WithValidReport_ReturnsHtmlString()
    {
        // Arrange
        var report = CreateSampleTestReport();
        
        // Act
        var html = _template.Generate(report);
        
        // Assert
        Assert.IsNotNull(html);
        Assert.IsTrue(html.Length > 0);
        StringAssert.Contains("<!DOCTYPE html>", html);
        StringAssert.Contains("<html", html);
        StringAssert.Contains("</html>", html);
    }

    [Test]
    public void Generate_ContainsRequiredHtmlStructure()
    {
        // Arrange
        var report = CreateSampleTestReport();
        
        // Act
        var html = _template.Generate(report);
        
        // Assert
        StringAssert.Contains("<head>", html);
        StringAssert.Contains("<body>", html);
        StringAssert.Contains("<title>Test Performance Report</title>", html);
        StringAssert.Contains("container-fluid", html);
    }

    [Test]
    public void Generate_IncludesBootstrapAndChartJs()
    {
        // Arrange
        var report = CreateSampleTestReport();
        
        // Act
        var html = _template.Generate(report);
        
        // Assert
        StringAssert.Contains("bootstrap", html);
        StringAssert.Contains("chart.js", html);
        StringAssert.Contains("cdn.jsdelivr.net", html);
    }

    [Test]
    public void Generate_IncludesSuiteInformation()
    {
        // Arrange
        var report = CreateSampleTestReport();
        report.SuitePath = "TestSuite.yaml";

        // Act
        var html = _template.Generate(report);

        // Assert
        StringAssert.Contains("Test Suite Overview", html);
        StringAssert.Contains("Total Tests", html);
        StringAssert.Contains("Passed Tests", html);
        StringAssert.Contains("Failed Tests", html);
        StringAssert.Contains("Test Performance Report", html);
    }

    [Test]
    public void Generate_IncludesTestResults()
    {
        // Arrange
        var report = CreateSampleTestReport();
        
        // Act
        var html = _template.Generate(report);
        
        // Assert
        StringAssert.Contains("Sample Test 1", html);
        StringAssert.Contains("Sample Test 2", html);
    }

    [Test]
    public void Generate_IncludesExecutionTimes()
    {
        // Arrange
        var report = CreateSampleTestReport();
        
        // Act
        var html = _template.Generate(report);
        
        // Assert
        StringAssert.Contains("1000", html); // Test execution time
        StringAssert.Contains("500", html);  // Step execution time
    }

    [Test]
    public void Generate_IncludesPassFailStatus()
    {
        // Arrange
        var report = CreateSampleTestReport();
        
        // Act
        var html = _template.Generate(report);
        
        // Assert
        StringAssert.Contains("Passed", html);
        StringAssert.Contains("Failed", html);
    }

    [Test]
    public void Generate_WithPerformanceData_IncludesMetrics()
    {
        // Arrange
        var report = CreateSampleTestReportWithPerformanceData();
        
        // Act
        var html = _template.Generate(report);
        
        // Assert
        StringAssert.Contains("RPC", html); // Step type
        // Should include performance metrics in the report
        Assert.IsTrue(html.Contains("performance") || html.Contains("metrics"));
    }

    [Test]
    public void Generate_WithFailedTest_IncludesFailureMessage()
    {
        // Arrange
        var report = CreateSampleTestReportWithFailures();
        
        // Act
        var html = _template.Generate(report);
        
        // Assert
        StringAssert.Contains("Test assertion failed", html);
        StringAssert.Contains("failure-message", html);
    }

    [Test]
    public void Generate_IncludesChartScripts()
    {
        // Arrange
        var report = CreateSampleTestReport();
        
        // Act
        var html = _template.Generate(report);
        
        // Assert
        StringAssert.Contains("<script>", html);
        StringAssert.Contains("Chart", html);
    }

    [Test]
    public void Generate_WithEmptyReport_HandlesGracefully()
    {
        // Arrange
        var report = new TestReport
        {
            SuitePath = "Empty.yaml",
            ExecutionTimestamp = DateTime.Now,
            TimeTakenMs = 0,
            Results = new List<TestResult>()
        };
        
        // Act
        var html = _template.Generate(report);
        
        // Assert
        Assert.IsNotNull(html);
        StringAssert.Contains("<!DOCTYPE html>", html);
        StringAssert.Contains("Test Suite Overview", html);
        StringAssert.Contains("Total Tests", html);
        StringAssert.Contains("0", html); // Should show 0 tests
    }

    [Test]
    public void Generate_WithLongTestNames_HandlesCorrectly()
    {
        // Arrange
        var longTestName = "This is a very long test name that should be handled properly in the HTML report without breaking the layout or causing issues";
        var result = new TestResult(longTestName);
        result.ExecutionTimeMs = 1000;
        result.StartTime = DateTime.Now.AddSeconds(-2);
        result.EndTime = DateTime.Now.AddSeconds(-1);

        var report = new TestReport
        {
            SuitePath = "LongNameSuite.yaml",
            ExecutionTimestamp = DateTime.Now,
            TimeTakenMs = 1000,
            Results = new List<TestResult> { result }
        };

        // Act
        var html = _template.Generate(report);

        // Assert
        Assert.IsNotNull(html);
        StringAssert.Contains(longTestName, html);
    }

    [Test]
    public void Generate_WithSpecialCharacters_EscapesCorrectly()
    {
        // Arrange
        var specialTestName = "Test with <special> & \"characters\"";
        var result = new TestResult(specialTestName);
        result.ExecutionTimeMs = 1000;
        result.StartTime = DateTime.Now.AddSeconds(-2);
        result.EndTime = DateTime.Now.AddSeconds(-1);

        var report = new TestReport
        {
            SuitePath = "SpecialCharsSuite.yaml",
            ExecutionTimestamp = DateTime.Now,
            TimeTakenMs = 1000,
            Results = new List<TestResult> { result }
        };

        // Act
        var html = _template.Generate(report);

        // Assert
        Assert.IsNotNull(html);
        // Should contain the test name in some form (may be escaped)
        Assert.IsTrue(html.Contains("Test with") || html.Contains("special") || html.Contains("characters"));
    }

    [Test]
    public void Generate_IncludesCssStyles()
    {
        // Arrange
        var report = CreateSampleTestReport();

        // Act
        var html = _template.Generate(report);

        // Assert
        StringAssert.Contains("<style>", html);
        StringAssert.Contains("chart-container", html);
        StringAssert.Contains("card", html);
        StringAssert.Contains("test-details", html);
    }

    [Test]
    public void Generate_WithMultipleSteps_IncludesAllSteps()
    {
        // Arrange
        var report = CreateSampleTestReportWithMultipleSteps();

        // Act
        var html = _template.Generate(report);

        // Assert
        StringAssert.Contains("Step 1", html);
        StringAssert.Contains("Step 2", html);
        StringAssert.Contains("Step 3", html);
    }

    private TestReport CreateSampleTestReport()
    {
        var result1 = new TestResult("Sample Test 1");
        result1.ExecutionTimeMs = 1000;
        result1.StartTime = DateTime.Now.AddSeconds(-2);
        result1.EndTime = DateTime.Now.AddSeconds(-1);

        var step1 = new TestStep.ExecutionResult("step1")
        {
            StepName = "Step 1",
            StepType = "HTTP",
            ExecutionTimeMs = 500,
            AssertionTimeMs = 50,
            StartTime = DateTime.Now.AddSeconds(-2),
            EndTime = DateTime.Now.AddSeconds(-1.5)
        };
        result1.ExecutionResults.Add(step1);

        var result2 = new TestResult("Sample Test 2");
        result2.ExecutionTimeMs = 800;
        result2.StartTime = DateTime.Now.AddSeconds(-1);
        result2.EndTime = DateTime.Now;

        var step2 = new TestStep.ExecutionResult("step2")
        {
            StepName = "Step 1",
            StepType = "CMD",
            ExecutionTimeMs = 400,
            AssertionTimeMs = 30,
            StartTime = DateTime.Now.AddSeconds(-1),
            EndTime = DateTime.Now.AddSeconds(-0.6)
        };
        step2.AddAssertionFailure("Test assertion failed");
        result2.ExecutionResults.Add(step2);

        return new TestReport
        {
            SuitePath = "SampleSuite.yaml",
            ExecutionTimestamp = DateTime.Now,
            TimeTakenMs = 2000,
            Results = new List<TestResult> { result1, result2 }
        };
    }

    private TestReport CreateSampleTestReportWithPerformanceData()
    {
        var report = CreateSampleTestReport();
        // Note: RpcPerformanceData might not be accessible, so we'll create a simple mock
        // or skip this test if the class is not available
        return report;
    }

    private TestReport CreateSampleTestReportWithFailures()
    {
        var report = CreateSampleTestReport();
        // The second result already has failures, so we can use that
        // or add more failures to the existing one
        report.Results[0].ExecutionResults[0].AddAssertionFailure("Additional test assertion failed");
        report.Results[0].ExecutionResults[0].AddAssertionFailure("Expected value did not match actual value");
        return report;
    }

    private TestReport CreateSampleTestReportWithMultipleSteps()
    {
        var report = CreateSampleTestReport();

        var step2 = new TestStep.ExecutionResult("step2")
        {
            StepName = "Step 2",
            StepType = "RPC",
            ExecutionTimeMs = 300,
            AssertionTimeMs = 25,
            StartTime = DateTime.Now.AddSeconds(-1.5),
            EndTime = DateTime.Now.AddSeconds(-1.2)
        };

        var step3 = new TestStep.ExecutionResult("step3")
        {
            StepName = "Step 3",
            StepType = "HTTP",
            ExecutionTimeMs = 200,
            AssertionTimeMs = 15,
            StartTime = DateTime.Now.AddSeconds(-1.2),
            EndTime = DateTime.Now.AddSeconds(-1)
        };

        report.Results[0].ExecutionResults.Add(step2);
        report.Results[0].ExecutionResults.Add(step3);
        return report;
    }
}
