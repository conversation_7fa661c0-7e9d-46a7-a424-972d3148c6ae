/*
 * Copyright 2021-present <PERSON><PERSON>. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

#include "PatientChartDataMaker.h"

namespace testdatamaker {
// -------------------------------------------------------------------------------------------------

std::optional<type::PatientChart> 
PatientChartDataMaker::make(const pcdm::PatientChartConfig& config) {
    return make(ProtoUtils::fromProtoConfig(config));
}

// -------------------------------------------------------------------------------------------------

std::optional<type::PatientChart>
testdatamaker::PatientChartDataMaker::make(const patientchartdatamaker_config_t& config) {
    if (!config.exists) {
        return std::nullopt;
    }

    switch (config.policy) {
    case coretypesdm::GeneratingPolicy::RANDOM:
        return makeRandom(config.params);
    case coretypesdm::GeneratingPolicy::BASED_ON:
        return makeBasedOn(config.seed, config.params, config.policy);
    case coretypesdm::GeneratingPolicy::PARTIAL:
        return makeBasedOn(std::nullopt, config.params, config.policy);
    default:
        return std::nullopt;
    }
}

// -------------------------------------------------------------------------------------------------

type::PatientChart
PatientChartDataMaker::makeRandom(const pcdm::PatientChartParams& params) {
    type::PatientChart patientChart;

    if (params.has_patientref()) {
        *patientChart.mutable_patientref() = params.patientref();
    } else {
        throw std::runtime_error("Patient reference is required to create a PatientChart.");
    }

    if (params.examinationreportconfigs().size() > 0) {
        for (const auto& reportConfig : params.examinationreportconfigs()) {
            auto maybeReport = ExaminationReportDataMaker::make(reportConfig);
            if (maybeReport.has_value()) {
                auto& report = maybeReport.value();

                type::PatientChart::ReportBrief reportBrief;
                reportBrief.set_eye(report.eyecontext().eye());
                type::Revision reportBriefRevision;
                *reportBriefRevision.mutable_id() = report.revision().id();
                *reportBrief.mutable_revision() = reportBriefRevision;

                *patientChart.add_examinationreports() = reportBrief;
            }
        }
    }

    if (params.subjectiveexaminationreports().size() > 0) {
        for (const auto& reportConfig : params.subjectiveexaminationreports()) {
            auto maybeReport = SubjectiveExaminationReportDataMaker::make(reportConfig);
            if (maybeReport.has_value()) {
                auto& report = maybeReport.value();

                type::PatientChart::ReportBrief reportBrief;
                reportBrief.set_eye(report.eye());
                type::Revision reportBriefRevision;
                *reportBriefRevision.mutable_id() = report.revision().id();
                *reportBrief.mutable_revision() = reportBriefRevision;

                *patientChart.add_subjectiverefractionreports() = reportBrief;
            }
        }
    }

    if (params.surgeryreports().size() > 0) {
        for (const auto& reportConfig : params.surgeryreports()) {
            auto maybeReport = SurgeryReportDataMaker::make(reportConfig);
            if (maybeReport.has_value()) {
                auto& report = maybeReport.value();

                type::PatientChart::ReportBrief reportBrief;
                reportBrief.set_eye(report.eye());
                type::Revision reportBriefRevision;
                *reportBriefRevision.mutable_id() = report.revision().id();
                *reportBrief.mutable_revision() = reportBriefRevision;

                *patientChart.add_surgeryreports() = reportBrief;
            }
        }
    }

    // TODO: Add the others

    return patientChart;
}

// -------------------------------------------------------------------------------------------------

type::PatientChart
PatientChartDataMaker::makeBasedOn(
    const std::optional<type::PatientChart>& seed,
    const pcdm::PatientChartParams& params,
    const coretypesdm::GeneratingPolicy mode) {

    type::PatientChart patientChart = seed.value_or(type::PatientChart{});

    if (params.has_patientref()) {
        *patientChart.mutable_patientref() = params.patientref();
    }

    if (params.examinationreportconfigs().size() > 0) {
        for (const auto& reportConfig : params.examinationreportconfigs()) {
            auto maybeReport = ExaminationReportDataMaker::make(reportConfig);
            if (maybeReport.has_value()) {
                auto& report = maybeReport.value();

                type::PatientChart::ReportBrief reportBrief;
                reportBrief.set_eye(report.eyecontext().eye());
                type::Revision reportBriefRevision;
                *reportBriefRevision.mutable_id() = report.revision().id();
                *reportBrief.mutable_revision() = reportBriefRevision;

                *patientChart.add_examinationreports() = reportBrief;
            }
        }
    }
    return patientChart;
}

// -------------------------------------------------------------------------------------------------

} // servicetestdata
