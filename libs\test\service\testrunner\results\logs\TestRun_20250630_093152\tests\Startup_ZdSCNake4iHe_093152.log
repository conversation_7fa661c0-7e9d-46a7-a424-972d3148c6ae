2025-06-30T09:31:52.7349359+05:30 [Information] [----------] 1 step from Startup
2025-06-30T09:31:52.7373504+05:30 [Information] Set Value to Step 'dabd7465-1831-4f98-baa5-7f1974158e33' name:
"Initialize Database"
2025-06-30T09:31:52.7375915+05:30 [Information] Set Value to Step 'dabd7465-1831-4f98-baa5-7f1974158e33' description:
"Setup initial database state"
2025-06-30T09:31:52.7378321+05:30 [Information] [ RUN      ] Startup > Initialize Database
2025-06-30T09:31:52.7388857+05:30 [Information] Executing single instance of step 'Initialize Database'.
2025-06-30T09:31:52.7400033+05:30 [Information] Executing step 'Initialize Database' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "dabd7465-1831-4f98-baa5-7f1974158e33",
        "tables": {},
        "variables": {},
        "name": "Initialize Database",
        "description": "Setup initial database state"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:31:52.7417007+05:30 [Information] Processing $var: path 'baseUrl'
2025-06-30T09:31:52.7435011+05:30 [Information] Checking if 'baseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:31:52.7452506+05:30 [Information] Resolving path 'baseUrl' using global variables.
2025-06-30T09:31:52.7455845+05:30 [Information] Resolved JPath '$var:baseUrl' to 'https://httpbin.org'
2025-06-30T09:31:52.7457543+05:30 [Information] Resolved template pattern '{{$var:baseUrl}}' to 'https://httpbin.org' in '{{$var:baseUrl}}/post'
2025-06-30T09:31:52.7461859+05:30 [Information] Resolved template '{{$var:baseUrl}}/post' to 'https://httpbin.org/post'
2025-06-30T09:31:52.7486382+05:30 [Information] Set Value to Step 'dabd7465-1831-4f98-baa5-7f1974158e33' input:
{
  "Method": "POST",
  "RequestUri": "https://httpbin.org/post",
  "Headers": {
    "Content-Type": "application/json"
  },
  "Content": {
    "action": "initialize_db"
  }
}
2025-06-30T09:31:53.9811520+05:30 [Information] Set Value to Step 'dabd7465-1831-4f98-baa5-7f1974158e33' output:
{
  "statusCode": 200,
  "reasonPhrase": "OK",
  "headers": [
    {
      "Key": "Date",
      "Value": [
        "Mon, 30 Jun 2025 04:01:59 GMT"
      ]
    },
    {
      "Key": "Connection",
      "Value": [
        "keep-alive"
      ]
    },
    {
      "Key": "Server",
      "Value": [
        "gunicorn/19.9.0"
      ]
    },
    {
      "Key": "Access-Control-Allow-Origin",
      "Value": [
        "*"
      ]
    },
    {
      "Key": "Access-Control-Allow-Credentials",
      "Value": [
        "true"
      ]
    }
  ],
  "content": {
    "args": {},
    "data": "{\r\n  \"action\": \"initialize_db\"\r\n}",
    "files": {},
    "form": {},
    "headers": {
      "Content-Length": "33",
      "Content-Type": "application/json; charset=utf-8",
      "Host": "httpbin.org",
      "X-Amzn-Trace-Id": "Root=1-68620c37-37d7cdc93e65b6ff486ec2b2"
    },
    "json": {
      "action": "initialize_db"
    },
    "origin": "**************",
    "url": "https://httpbin.org/post"
  }
}
2025-06-30T09:31:53.9816716+05:30 [Information] Resolving path 'output.content.json.action' using step input/output.
2025-06-30T09:31:53.9818369+05:30 [Information] Resolved JPath 'output.content.json.action' to 'initialize_db'
2025-06-30T09:31:53.9828827+05:30 [Information] Set Value to Step 'dabd7465-1831-4f98-baa5-7f1974158e33' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:01:52.7388599Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:31:53.9854915+05:30 [Information] Run Asserter : {"Expression1":{"JPath":"$curStep:output.content.json.action","ErrorMesage":null},"Expression2":{"Value":null,"ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:31:53.9858668+05:30 [Information] Resolving path 'output.content.json.action' using $curStep:
2025-06-30T09:31:53.9859904+05:30 [Information] Resolved JPath '$curStep:output.content.json.action' to 'initialize_db'
2025-06-30T09:31:53.9873842+05:30 [Information] Error in EqualExpression: The method or operation is not implemented.
2025-06-30T09:31:53.9883725+05:30 [Information] Set Value to Step 'dabd7465-1831-4f98-baa5-7f1974158e33' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 1238,
  "startTime": "2025-06-30T04:01:52.7388599Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:31:53.9885699+05:30 [Information] Step 'Initialize Database' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "dabd7465-1831-4f98-baa5-7f1974158e33",
        "tables": {},
        "variables": {},
        "name": "Initialize Database",
        "description": "Setup initial database state",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "action": "initialize_db"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:01:59 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"action\": \"initialize_db\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "33",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620c37-37d7cdc93e65b6ff486ec2b2"
            },
            "json": {
              "action": "initialize_db"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 1238,
          "startTime": "2025-06-30T04:01:52.7388599Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:31:53.9887418+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:31:53.9888400+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:31:53.9888995+05:30 [Information] No performance data found for step 'Initialize Database' (http)
2025-06-30T09:31:53.9891960+05:30 [Information] [ OK       ]
2025-06-30T09:31:53.9901913+05:30 [Information] [----------] 1 step from Startup (1253 ms total)
