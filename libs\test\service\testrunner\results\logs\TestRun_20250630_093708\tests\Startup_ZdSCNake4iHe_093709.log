2025-06-30T09:37:09.1598023+05:30 [Information] [----------] 1 step from Startup
2025-06-30T09:37:09.1619910+05:30 [Information] Set Value to Step 'f4f972be-dc82-454c-b376-20156f0e9b25' name:
"Initialize Database"
2025-06-30T09:37:09.1622309+05:30 [Information] Set Value to Step 'f4f972be-dc82-454c-b376-20156f0e9b25' description:
"Setup initial database state"
2025-06-30T09:37:09.1624593+05:30 [Information] [ RUN      ] Startup > Initialize Database
2025-06-30T09:37:09.1634903+05:30 [Information] Executing single instance of step 'Initialize Database'.
2025-06-30T09:37:09.1645713+05:30 [Information] Executing step 'Initialize Database' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "f4f972be-dc82-454c-b376-20156f0e9b25",
        "tables": {},
        "variables": {},
        "name": "Initialize Database",
        "description": "Setup initial database state"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:37:09.1661414+05:30 [Information] Processing $var: path 'baseUrl'
2025-06-30T09:37:09.1675232+05:30 [Information] Checking if 'baseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:09.1690445+05:30 [Information] Resolving path 'baseUrl' using global variables.
2025-06-30T09:37:09.1692542+05:30 [Information] Resolved JPath '$var:baseUrl' to 'https://httpbin.org'
2025-06-30T09:37:09.1693443+05:30 [Information] Resolved template pattern '{{$var:baseUrl}}' to 'https://httpbin.org' in '{{$var:baseUrl}}/post'
2025-06-30T09:37:09.1695860+05:30 [Information] Resolved template '{{$var:baseUrl}}/post' to 'https://httpbin.org/post'
2025-06-30T09:37:09.1718066+05:30 [Information] Set Value to Step 'f4f972be-dc82-454c-b376-20156f0e9b25' input:
{
  "Method": "POST",
  "RequestUri": "https://httpbin.org/post",
  "Headers": {
    "Content-Type": "application/json"
  },
  "Content": {
    "action": "initialize_db"
  }
}
2025-06-30T09:37:11.2836048+05:30 [Information] Set Value to Step 'f4f972be-dc82-454c-b376-20156f0e9b25' output:
{
  "statusCode": 200,
  "reasonPhrase": "OK",
  "headers": [
    {
      "Key": "Date",
      "Value": [
        "Mon, 30 Jun 2025 04:07:16 GMT"
      ]
    },
    {
      "Key": "Connection",
      "Value": [
        "keep-alive"
      ]
    },
    {
      "Key": "Server",
      "Value": [
        "gunicorn/19.9.0"
      ]
    },
    {
      "Key": "Access-Control-Allow-Origin",
      "Value": [
        "*"
      ]
    },
    {
      "Key": "Access-Control-Allow-Credentials",
      "Value": [
        "true"
      ]
    }
  ],
  "content": {
    "args": {},
    "data": "{\r\n  \"action\": \"initialize_db\"\r\n}",
    "files": {},
    "form": {},
    "headers": {
      "Content-Length": "33",
      "Content-Type": "application/json; charset=utf-8",
      "Host": "httpbin.org",
      "X-Amzn-Trace-Id": "Root=1-68620d74-0ed83392730787cf645817b6"
    },
    "json": {
      "action": "initialize_db"
    },
    "origin": "**************",
    "url": "https://httpbin.org/post"
  }
}
2025-06-30T09:37:11.2841493+05:30 [Information] Resolving path 'output.content.json.action' using step input/output.
2025-06-30T09:37:11.2843151+05:30 [Information] Resolved JPath 'output.content.json.action' to 'initialize_db'
2025-06-30T09:37:11.2852834+05:30 [Information] Set Value to Step 'f4f972be-dc82-454c-b376-20156f0e9b25' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:07:09.1634632Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:37:11.2877373+05:30 [Information] Run Asserter : {"Expression1":{"JPath":"$curStep:output.content.json.action","ErrorMesage":null},"Expression2":{"Value":null,"ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:37:11.2880706+05:30 [Information] Resolving path 'output.content.json.action' using $curStep:
2025-06-30T09:37:11.2881846+05:30 [Information] Resolved JPath '$curStep:output.content.json.action' to 'initialize_db'
2025-06-30T09:37:11.2895128+05:30 [Information] Error in EqualExpression: The method or operation is not implemented.
2025-06-30T09:37:11.2903972+05:30 [Information] Set Value to Step 'f4f972be-dc82-454c-b376-20156f0e9b25' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 2117,
  "startTime": "2025-06-30T04:07:09.1634632Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:37:11.2905709+05:30 [Information] Step 'Initialize Database' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "f4f972be-dc82-454c-b376-20156f0e9b25",
        "tables": {},
        "variables": {},
        "name": "Initialize Database",
        "description": "Setup initial database state",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "action": "initialize_db"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:07:16 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"action\": \"initialize_db\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "33",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620d74-0ed83392730787cf645817b6"
            },
            "json": {
              "action": "initialize_db"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 2117,
          "startTime": "2025-06-30T04:07:09.1634632Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:37:11.2907407+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:37:11.2908467+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:37:11.2909074+05:30 [Information] No performance data found for step 'Initialize Database' (http)
2025-06-30T09:37:11.2911883+05:30 [Information] [ OK       ]
2025-06-30T09:37:11.2922645+05:30 [Information] [----------] 1 step from Startup (2131 ms total)
