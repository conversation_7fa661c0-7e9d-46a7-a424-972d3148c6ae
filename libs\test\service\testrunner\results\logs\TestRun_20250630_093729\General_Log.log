2025-06-30T09:37:29.9851571+05:30 [Information] Global variables set to: baseUrl: https://httpbin.org, serverIp: 127.0.0.1, serverPort: 60606
2025-06-30T09:37:29.9991644+05:30 [Information] Loading tests from D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc with pattern *-test.yaml
2025-06-30T09:37:30.0023489+05:30 [Information] Loading test from D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc\cmd\chained-cmd-test.yaml
2025-06-30T09:37:30.0114070+05:30 [Information] Loading test from D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc\cmd\docker-list-test.yaml
2025-06-30T09:37:30.0118685+05:30 [Information] Loading test from D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc\cmd\eco-test.yaml
2025-06-30T09:37:30.0124290+05:30 [Information] Loading test from D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc\cmd\long-output-test.yaml
2025-06-30T09:37:30.0127680+05:30 [Information] Loading test from D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc\cmd\none-zero-exit-test.yaml
2025-06-30T09:37:30.0131339+05:30 [Information] Loading test from D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc\http\get-test.yaml
2025-06-30T09:37:30.0142739+05:30 [Information] Loading test from D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc\http\multistep-test.yaml
2025-06-30T09:37:30.0148441+05:30 [Information] Loading test from D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc\http\table-step-iteration-sample-test.yaml
2025-06-30T09:37:30.0155143+05:30 [Information] Loading test from D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc\http\table-test-iteration-sample-test.yaml
2025-06-30T09:37:30.0161865+05:30 [Information] Loading test from D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc\http\table-test-step-iteration-sample-test.yaml
2025-06-30T09:37:30.0170511+05:30 [Information] Loading test from D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc\http\with-include-test.yaml
2025-06-30T09:37:30.0181758+05:30 [Information] Loading test from D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc\rpc\protoFieldSetters-test.yaml
2025-06-30T09:37:30.5857108+05:30 [Information] Loading test from D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc\rpc\protoReference-test.yaml
2025-06-30T09:37:30.5890876+05:30 [Information] Global variables set to: 
2025-06-30T09:37:30.5900284+05:30 [Information] [==========] Running 13 tests from Yaml files
2025-06-30T09:37:30.5914570+05:30 [Information] [-------------------------------------    Startup     -------------------------------------]
2025-06-30T09:37:30.6101088+05:30 [Information] [----------] 1 step from Startup
2025-06-30T09:37:30.6133067+05:30 [Information] [ RUN      ] Startup > Initialize Database
2025-06-30T09:37:31.6994350+05:30 [Information] [ OK       ]
2025-06-30T09:37:31.7005419+05:30 [Information] [----------] 1 step from Startup (1089 ms total)
2025-06-30T09:37:31.7023696+05:30 [Information] Global variables set to: curDir: D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc, baseUrl: https://httpbin.org, serverIp: 127.0.0.1, serverPort: 60606, dbInitStatus: initialize_db
2025-06-30T09:37:31.7027738+05:30 [Information] [------------------------------------- Test Execution -------------------------------------]
2025-06-30T09:37:31.7036109+05:30 [Information] Processing $var: path 'ProductID'
2025-06-30T09:37:31.7037266+05:30 [Information] Checking if 'ProductID' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:31.7038071+05:30 [Information] Resolving path 'ProductID' using global variables.
2025-06-30T09:37:31.7038731+05:30 [Information] Resolved JPath '$var:ProductID' to '123'
2025-06-30T09:37:31.7039212+05:30 [Information] Resolved template pattern '{{$var:ProductID}}' to '123' in '[
  {
    "ProductID": "{{$var:ProductID}}",
    "Name": "Laptop",
    "Price": "1500",
    "Specifications": {
      "Processor": "Intel i7",
      "RAM": "16GB",
      "Storage": "512GB SSD"
    }
  },
  {
    "ProductID": "102",
    "Name": "Smartphone",
    "Price": "800",
    "Specifications": {
      "Processor": "Snapdragon 888",
      "RAM": "8GB",
      "Storage": "128GB"
    }
  }
]'
2025-06-30T09:37:31.7040216+05:30 [Information] Resolved template '[
  {
    "ProductID": "{{$var:ProductID}}",
    "Name": "Laptop",
    "Price": "1500",
    "Specifications": {
      "Processor": "Intel i7",
      "RAM": "16GB",
      "Storage": "512GB SSD"
    }
  },
  {
    "ProductID": "102",
    "Name": "Smartphone",
    "Price": "800",
    "Specifications": {
      "Processor": "Snapdragon 888",
      "RAM": "8GB",
      "Storage": "128GB"
    }
  }
]' to '[
  {
    "ProductID": "123",
    "Name": "Laptop",
    "Price": "1500",
    "Specifications": {
      "Processor": "Intel i7",
      "RAM": "16GB",
      "Storage": "512GB SSD"
    }
  },
  {
    "ProductID": "102",
    "Name": "Smartphone",
    "Price": "800",
    "Specifications": {
      "Processor": "Snapdragon 888",
      "RAM": "8GB",
      "Storage": "128GB"
    }
  }
]'
2025-06-30T09:37:31.7048004+05:30 [Information] [----------] 1 step from Simple Get Request
2025-06-30T09:37:31.7055740+05:30 [Information] [ RUN      ] Simple Get Request > Step get request
2025-06-30T09:37:32.4695914+05:30 [Information] [ OK       ]
2025-06-30T09:37:32.4703014+05:30 [Information] [----------] 1 step from Simple Get Request (764 ms total)
2025-06-30T09:37:32.4712879+05:30 [Information] [----------] 1 step from Chained Commands with Piping
2025-06-30T09:37:32.4717312+05:30 [Information] [ RUN      ] Chained Commands with Piping > Step pipe
2025-06-30T09:37:32.5106028+05:30 [Information] [ OK       ]
2025-06-30T09:37:32.5108704+05:30 [Information] [----------] 1 step from Chained Commands with Piping (39 ms total)
2025-06-30T09:37:32.5119175+05:30 [Information] [----------] 1 step from Docker PS Test
2025-06-30T09:37:32.5125819+05:30 [Information] [ RUN      ] Docker PS Test > Step list containers
2025-06-30T09:37:32.6895082+05:30 [Error] Assert Failure :  Contains CONTAINER ID
2025-06-30T09:37:32.6902670+05:30 [Error] Contain assertion failed: Expression1 should contain Expression2.
Expression1: JsonPath('$curStep:$.output.stdout') resolved to 
Expression2: Constant('CONTAINER ID') resolved to CONTAINER ID
2025-06-30T09:37:32.6903857+05:30 [Error] [ FAILED   ]
2025-06-30T09:37:32.6909020+05:30 [Information] [----------] 1 step from Docker PS Test (178 ms total)
2025-06-30T09:37:32.6919646+05:30 [Information] [----------] 1 step from Simple Echo Command
2025-06-30T09:37:32.6925330+05:30 [Information] [ RUN      ] Simple Echo Command > Step echo
2025-06-30T09:37:32.7118334+05:30 [Information] [ OK       ]
2025-06-30T09:37:32.7121888+05:30 [Information] [----------] 1 step from Simple Echo Command (19 ms total)
2025-06-30T09:37:32.7134199+05:30 [Information] [----------] 1 step from Long Output Command
2025-06-30T09:37:32.7141047+05:30 [Information] [ RUN      ] Long Output Command > Step long output
2025-06-30T09:37:32.7441812+05:30 [Information] [ OK       ]
2025-06-30T09:37:32.7445147+05:30 [Information] [----------] 1 step from Long Output Command (30 ms total)
2025-06-30T09:37:32.7456488+05:30 [Information] [----------] 1 step from Non-Zero Exit Code Test
2025-06-30T09:37:32.7462455+05:30 [Information] [ RUN      ] Non-Zero Exit Code Test > Step exit failure
2025-06-30T09:37:32.7749419+05:30 [Information] [ OK       ]
2025-06-30T09:37:32.7752188+05:30 [Information] [----------] 1 step from Non-Zero Exit Code Test (29 ms total)
2025-06-30T09:37:32.7762878+05:30 [Information] [----------] 2 steps from Testing MultiStep with JOSNPath
2025-06-30T09:37:32.7769964+05:30 [Information] [ RUN      ] Testing MultiStep with JOSNPath > GET Token
2025-06-30T09:37:33.5322404+05:30 [Information] [ OK       ]
2025-06-30T09:37:33.5343234+05:30 [Information] [ RUN      ] Testing MultiStep with JOSNPath > Step to test POST request with token
2025-06-30T09:37:34.3003626+05:30 [Information] [ OK       ]
2025-06-30T09:37:34.3010304+05:30 [Information] [----------] 2 steps from Testing MultiStep with JOSNPath (1524 ms total)
2025-06-30T09:37:34.3026627+05:30 [Information] [----------] 1 step from Demo HTTP Table Step Iteration
2025-06-30T09:37:34.3033692+05:30 [Information] [ RUN      ] Demo HTTP Table Step Iteration > Run for Each User
2025-06-30T09:37:34.3116893+05:30 [Information] Step Iteration 1/2: Fetching user Tom details
2025-06-30T09:37:35.1127304+05:30 [Information] Step Iteration 2/2: Fetching user John details
2025-06-30T09:37:35.5160358+05:30 [Information] [ OK       ]
2025-06-30T09:37:35.5169541+05:30 [Information] [----------] 1 step from Demo HTTP Table Step Iteration (1213 ms total)
2025-06-30T09:37:35.5217125+05:30 [Information] [----------] 1 step from Demo HTTP Table Test Iteration
2025-06-30T09:37:35.5244172+05:30 [Information] Test Iteration 1/2: Demo HTTP Table Test Iteration
2025-06-30T09:37:35.5250262+05:30 [Information] [ RUN      ] Demo HTTP Table Test Iteration > Fetching user Tom details
2025-06-30T09:37:36.8621975+05:30 [Information] [ OK       ]
2025-06-30T09:37:36.8640955+05:30 [Information] Test Iteration 2/2: Demo HTTP Table Test Iteration
2025-06-30T09:37:36.8647108+05:30 [Information] [ RUN      ] Demo HTTP Table Test Iteration > Fetching user John details
2025-06-30T09:37:37.1337888+05:30 [Information] [ OK       ]
2025-06-30T09:37:37.1340618+05:30 [Information] [----------] 1 step from Demo HTTP Table Test Iteration (1611 ms total)
2025-06-30T09:37:37.1352716+05:30 [Information] [----------] 2 steps from Itterative Test and Step
2025-06-30T09:37:37.1369084+05:30 [Information] Test Iteration 1/2: Running Test for Tom
2025-06-30T09:37:37.1375922+05:30 [Information] [ RUN      ] Itterative Test and Step > Step to get token for Tom
2025-06-30T09:37:37.8983076+05:30 [Information] [ OK       ]
2025-06-30T09:37:37.8990504+05:30 [Information] [ RUN      ] Itterative Test and Step > Itterative Step for user Tom
2025-06-30T09:37:37.9003189+05:30 [Information] Step Iteration 1/2: Running for Tom and role Admin
2025-06-30T09:37:38.6773360+05:30 [Information] Step Iteration 2/2: Running for Tom and role User
2025-06-30T09:37:38.9472201+05:30 [Information] [ OK       ]
2025-06-30T09:37:38.9489076+05:30 [Information] Test Iteration 2/2: Running Test for John
2025-06-30T09:37:38.9493062+05:30 [Information] [ RUN      ] Itterative Test and Step > Step to get token for John
2025-06-30T09:37:39.2084020+05:30 [Information] [ OK       ]
2025-06-30T09:37:39.2094201+05:30 [Information] [ RUN      ] Itterative Test and Step > Itterative Step for user John
2025-06-30T09:37:39.2107404+05:30 [Information] Step Iteration 1/2: Running for John and role Admin
2025-06-30T09:37:39.4821834+05:30 [Information] Step Iteration 2/2: Running for John and role User
2025-06-30T09:37:40.1427441+05:30 [Information] [ OK       ]
2025-06-30T09:37:40.1431560+05:30 [Information] [----------] 2 steps from Itterative Test and Step (3007 ms total)
2025-06-30T09:37:40.1441394+05:30 [Information] [----------] 2 steps from Test with include
2025-06-30T09:37:40.1446657+05:30 [Information] [ RUN      ] Test with include > GET Token
2025-06-30T09:37:40.9519579+05:30 [Information] [ OK       ]
2025-06-30T09:37:40.9528314+05:30 [Information] [ RUN      ] Test with include > Step to test POST request with token
2025-06-30T09:37:41.9163649+05:30 [Information] [ OK       ]
2025-06-30T09:37:41.9175116+05:30 [Information] [----------] 2 steps from Test with include (1772 ms total)
2025-06-30T09:37:41.9197303+05:30 [Information] [----------] 5 steps from ProtoFieldSetters test
2025-06-30T09:37:41.9218473+05:30 [Information] [ RUN      ] ProtoFieldSetters test > Step 1: Connect to the MBusClient with admin credentials
2025-06-30T09:37:43.9503433+05:30 [Error] Failed to execute step : Cannot connect to 127.0.0.1:60606.
2025-06-30T09:37:43.9504577+05:30 [Error] [ FAILED   ]
2025-06-30T09:37:43.9509158+05:30 [Information] [----------] 5 steps from ProtoFieldSetters test (2029 ms total)
2025-06-30T09:37:43.9521628+05:30 [Information] [----------] 4 steps from ProtoReference test
2025-06-30T09:37:43.9528839+05:30 [Information] [ RUN      ] ProtoReference test > Step 1: Connect to the MBusClient with admin credentials
2025-06-30T09:37:45.9740364+05:30 [Error] Failed to execute step : Cannot connect to 127.0.0.1:60606.
2025-06-30T09:37:45.9741597+05:30 [Error] [ FAILED   ]
2025-06-30T09:37:45.9744655+05:30 [Information] [----------] 4 steps from ProtoReference test (2021 ms total)
2025-06-30T09:37:45.9757005+05:30 [Information] [-------------------------------------    Teardown    -------------------------------------]
2025-06-30T09:37:45.9773491+05:30 [Information] [----------] 1 step from Teardown
2025-06-30T09:37:45.9780992+05:30 [Information] [ RUN      ] Teardown > Clean up Database
2025-06-30T09:37:46.7618714+05:30 [Information] [ OK       ]
2025-06-30T09:37:46.7629928+05:30 [Information] [----------] 1 step from Teardown (784 ms total)
2025-06-30T09:37:46.7643623+05:30 [Information] Global variables set to: curDir: D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc, baseUrl: https://httpbin.org, serverIp: 127.0.0.1, serverPort: 60606, dbInitStatus: initialize_db
2025-06-30T09:37:46.7646110+05:30 [Information] [-------------------------------------  Test Results  -------------------------------------]
2025-06-30T09:37:46.7659476+05:30 [Information] [==========] 13 tests from Yaml files ran. (16176 ms total)
2025-06-30T09:37:46.7671517+05:30 [Information] [  PASSED  ] 10 tests.
2025-06-30T09:37:46.7675630+05:30 [Error] [  FAILED  ] 3 tests, listed below:
2025-06-30T09:37:46.7680466+05:30 [Error] [  FAILED  ] Docker PS Test
2025-06-30T09:37:46.7685571+05:30 [Error] [  FAILED  ] ProtoFieldSetters test
2025-06-30T09:37:46.7689743+05:30 [Error] [  FAILED  ] ProtoReference test
