
/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using System;
using System.Threading;

namespace TestLib;
public abstract class BaseTestStepExecutor<TOutputSettings>(
    TOutputSettings outputSettings, 
    TestStepExecutionSettings executionSettings) : ITestStepExecutor
    where TOutputSettings : TestStepOutputSettings
{
    protected TOutputSettings OutputSettings { get; } = outputSettings;
    protected TestStepExecutionSettings ExecutionSettings { get; } = executionSettings;

    public ITestStepOutput Execute(ITestStepInput input, TestContext context)
    {
        // Apply step delay before execution if specified
        if (ExecutionSettings.DelayMs > 0)
        {
            TestLogger.Trace($"Delaying step execution for {ExecutionSettings.DelayMs}ms");
            Thread.Sleep(ExecutionSettings.DelayMs);
        }

        // Extract condition and other settings
        var retryCondition = ExecutionSettings.RetryUntil;
        int maxRetries = ExecutionSettings.MaxRetries;
        int retryDelayMs = ExecutionSettings.RetryDelayMs;

        ITestStepOutput finalOutput = null;

        for (int attempt = 0; attempt < maxRetries; attempt++)
        {
            var currentAttempt = attempt + 1;
            try
            {
                var output = ExecuteAttempt(input, context);

                // Check the retry condition if any
                if (retryCondition == null || retryCondition.IsConditionMet(output.ToJson()))
                {
                    // Condition met (or no condition) - done
                    finalOutput = output;
                    break;
                }
                else
                {
                    // Condition not met
                    TestLogger.LogInfo($"Condition not met on attempt {currentAttempt}/{maxRetries}");
                    if (currentAttempt == maxRetries)
                    {
                        // Last attempt, still not met
                        TestLogger.LogError($"Max retries reached ({maxRetries}) and condition not met.");
                        finalOutput = output;
                    }
                    else
                    {
                        // Wait and try again
                        Thread.Sleep(retryDelayMs);
                    }
                }
            }
            catch (Exception ex) when (currentAttempt < maxRetries)
            {
                // Exception occurred, but we can still retry
                TestLogger.LogInfo($"Exception on attempt {currentAttempt}/{maxRetries}: {ex.Message}. Retrying...");
                Thread.Sleep(retryDelayMs);
                continue;
            }
            catch (Exception ex)
            {
                // On last attempt, rethrow the exception
                var retryMessage = maxRetries > 1 ? $" after {maxRetries} attempts" : "";
                throw new Exception($"Failed to execute step{retryMessage} : {ex.Message}", ex);
            }
        }

        return finalOutput;
    }

    /// <summary>
    /// Perform a single attempt of the step execution without any retry logic.
    /// Derived classes must implement this.
    /// </summary>
    protected abstract ITestStepOutput ExecuteAttempt(ITestStepInput input, TestContext context);
}
