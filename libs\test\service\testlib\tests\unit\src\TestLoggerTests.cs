/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using Newtonsoft.Json.Linq;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;

namespace TestLib.Tests;

[TestFixture]
public class TestLoggerTests
{
    private string _testDirectory;
    private TestOutputSettings _outputSettings;

    [SetUp]
    public void Setup()
    {
        // Create a unique directory for each test to avoid interference
        _testDirectory = Path.Combine(Path.GetTempPath(), Guid.NewGuid().ToString());
        Directory.CreateDirectory(_testDirectory);

        _outputSettings = new TestOutputSettings
        {
            ConsoleOutput = false, // Disable console output for testing
            Trace = true,
            OutputReport = true // Enable output report to create directories
        };

        // Clean up any existing TestLogger state
        TestLogger.EndTestLogging();
    }

    [TearDown]
    public void TearDown()
    {
        if (Directory.Exists(_testDirectory))
        {
            Directory.Delete(_testDirectory, true);
        }
    }

    [Test]
    public void ConfigureLogging_CreatesLoggingManager()
    {
        // Act
        TestLogger.ConfigureLogging(_outputSettings, _testDirectory);
        
        // Assert - Should not throw and should be able to log
        Assert.DoesNotThrow(() => TestLogger.LogInfo("Test message"));
    }

    [Test]
    public void StartTestLogging_CreatesTestSpecificLog()
    {
        // Arrange
        TestLogger.ConfigureLogging(_outputSettings, _testDirectory);
        var testName = "TestLoggerTest";

        // Act
        TestLogger.StartTestLogging(testName);
        TestLogger.LogInfo("Test message");

        // Assert
        var tracePath = _outputSettings.GetTracePath(_testDirectory);
        var runDirectories = Directory.GetDirectories(tracePath, "TestRun_*");
        Assert.IsTrue(runDirectories.Length > 0);

        var testsDirectory = Path.Combine(runDirectories[0], "tests");
        var logFiles = Directory.GetFiles(testsDirectory, "*.log");
        Assert.IsTrue(logFiles.Length > 0);
    }

    [Test]
    public void EndTestLogging_CompletesTestLogging()
    {
        // Arrange
        TestLogger.ConfigureLogging(_outputSettings, _testDirectory);
        TestLogger.StartTestLogging("TestLoggerTest");
        
        // Act & Assert
        Assert.DoesNotThrow(() => TestLogger.EndTestLogging());
    }

    [Test]
    public void LogInfo_WritesToLog()
    {
        // Arrange
        TestLogger.ConfigureLogging(_outputSettings, _testDirectory);
        TestLogger.StartTestLogging("InfoTest");
        var message = "Information message";

        // Act
        TestLogger.LogInfo(message);

        // Assert
        var logContent = GetLatestLogContent();
        StringAssert.Contains(message, logContent);
    }

    [Test]
    public void LogError_WritesToLog()
    {
        // Arrange
        TestLogger.ConfigureLogging(_outputSettings, _testDirectory);
        TestLogger.StartTestLogging("ErrorTest");
        var message = "Error message";

        // Act
        TestLogger.LogError(message);

        // Assert
        var logContent = GetLatestLogContent();
        StringAssert.Contains(message, logContent);
    }

    [Test]
    public void Trace_WritesToLog()
    {
        // Arrange
        TestLogger.ConfigureLogging(_outputSettings, _testDirectory);
        TestLogger.StartTestLogging("TraceTest");
        var message = "Trace message";

        // Act
        TestLogger.Trace(message);

        // Assert
        var logContent = GetLatestLogContent();
        StringAssert.Contains(message, logContent);
    }

    [Test]
    public void Trace_WithErrorLevel_WritesToLog()
    {
        // Arrange
        TestLogger.ConfigureLogging(_outputSettings, _testDirectory);
        TestLogger.StartTestLogging("TraceErrorTest");
        var message = "Trace error message";

        // Act
        TestLogger.Trace(message, "ERROR");

        // Assert
        var logContent = GetLatestLogContent();
        StringAssert.Contains(message, logContent);
    }

    [Test]
    public void LogJObject_WritesToLog()
    {
        // Arrange
        TestLogger.ConfigureLogging(_outputSettings, _testDirectory);
        TestLogger.StartTestLogging("JsonTest");
        var description = "Test JSON object";
        var jsonObject = JObject.Parse("{\"key\": \"value\", \"number\": 42}");

        // Act
        TestLogger.LogJObject(description, jsonObject);

        // Assert
        var logContent = GetLatestLogContent();
        StringAssert.Contains(description, logContent);
        StringAssert.Contains("key", logContent);
        StringAssert.Contains("value", logContent);
    }

    [Test]
    public void TraceContext_WritesToLog()
    {
        // Arrange
        TestLogger.ConfigureLogging(_outputSettings, _testDirectory);
        TestLogger.StartTestLogging("ContextTest");
        var context = new TestContext();
        context.Variables.Upsert("testVar", "testValue");
        var message = "Context trace";

        // Act
        TestLogger.TraceContext(message, context);

        // Assert
        var logContent = GetLatestLogContent();
        StringAssert.Contains(message, logContent);
    }

    [Test]
    public void BeginRun_WithTest_WritesToLog()
    {
        // Arrange
        TestLogger.ConfigureLogging(_outputSettings, _testDirectory);
        var test = CreateTestWithSteps("BeginRunTest", 3);

        // Act
        TestLogger.BeginRun(test);

        // Assert
        var tracePath = _outputSettings.GetTracePath(_testDirectory);
        var runDirectories = Directory.GetDirectories(tracePath, "TestRun_*");
        Assert.IsTrue(runDirectories.Length > 0);

        var generalLogFile = Path.Combine(runDirectories[0], "General_Log.log");
        Assert.IsTrue(File.Exists(generalLogFile));

        var logContent = File.ReadAllText(generalLogFile);
        StringAssert.Contains("BeginRunTest", logContent);
        StringAssert.Contains("3 steps", logContent);
    }

    [Test]
    public void BeginRun_WithTestAndStep_WritesToLog()
    {
        // Arrange
        // Create a completely unique directory for this test
        var uniqueTestDir = Path.Combine(Path.GetTempPath(), "TestLoggerStepTest", Guid.NewGuid().ToString());
        Directory.CreateDirectory(uniqueTestDir);

        TestLogger.ConfigureLogging(_outputSettings, uniqueTestDir);
        var test = CreateTestWithSteps("BeginRunStepTest", 1);
        var step = test.Steps[0];

        // Act
        TestLogger.BeginRun(test, step);

        // Assert
        // Wait for log to be written
        Thread.Sleep(200);

        var tracePath = _outputSettings.GetTracePath(uniqueTestDir);
        var runDirectories = Directory.GetDirectories(tracePath, "TestRun_*");
        Assert.IsTrue(runDirectories.Length > 0, "No test run directories found");

        var generalLogFile = Path.Combine(runDirectories[0], "General_Log.log");
        Assert.IsTrue(File.Exists(generalLogFile), "General log file not found");

        var logContent = File.ReadAllText(generalLogFile);
        StringAssert.Contains("BeginRunStepTest", logContent);
        StringAssert.Contains(step.DisplayName, logContent);

        // Cleanup
        Directory.Delete(uniqueTestDir, true);
    }

    [Test]
    public void BeginRun_WithTestSuite_WritesToLog()
    {
        // Arrange
        TestLogger.ConfigureLogging(_outputSettings, _testDirectory);
        var testSuite = new TestSuite(); // Create empty test suite

        // Act
        TestLogger.BeginRun(testSuite);

        // Assert
        var tracePath = _outputSettings.GetTracePath(_testDirectory);
        var runDirectories = Directory.GetDirectories(tracePath, "TestRun_*");
        Assert.IsTrue(runDirectories.Length > 0);

        var generalLogFile = Path.Combine(runDirectories[0], "General_Log.log");
        Assert.IsTrue(File.Exists(generalLogFile));

        var logContent = File.ReadAllText(generalLogFile);
        StringAssert.Contains("Running", logContent);
        StringAssert.Contains("tests", logContent);
    }

    [Test]
    public void EndRun_WithTestSuite_WritesToLog()
    {
        // Arrange
        TestLogger.ConfigureLogging(_outputSettings, _testDirectory);
        var testSuite = new TestSuite(); // Create empty test suite

        // Act
        TestLogger.EndRun(testSuite, 1000);

        // Assert
        var tracePath = _outputSettings.GetTracePath(_testDirectory);
        var runDirectories = Directory.GetDirectories(tracePath, "TestRun_*");
        Assert.IsTrue(runDirectories.Length > 0);

        var generalLogFile = Path.Combine(runDirectories[0], "General_Log.log");
        Assert.IsTrue(File.Exists(generalLogFile));

        var logContent = File.ReadAllText(generalLogFile);
        StringAssert.Contains("tests from Yaml files ran", logContent);
        StringAssert.Contains("1000 ms total", logContent);
    }

    [Test]
    public void EndRun_WithPassedExecutionResult_WritesToLog()
    {
        // Arrange
        TestLogger.ConfigureLogging(_outputSettings, _testDirectory);
        TestLogger.StartTestLogging("PassedTest");
        var result = new TestStep.ExecutionResult("test-step-1");

        // Act
        TestLogger.EndRun(result);

        // Assert
        var logContent = GetLatestLogContent();
        StringAssert.Contains("OK", logContent);
    }

    [Test]
    public void EndRun_WithFailedExecutionResult_WritesToLog()
    {
        // Arrange
        TestLogger.ConfigureLogging(_outputSettings, _testDirectory);
        TestLogger.StartTestLogging("FailedTest");
        var result = new TestStep.ExecutionResult("test-step-2");
        result.AddAssertionFailure("Test assertion failed");

        // Act
        TestLogger.EndRun(result);

        // Assert
        var logContent = GetLatestLogContent();
        StringAssert.Contains("FAILED", logContent);
        StringAssert.Contains("Test assertion failed", logContent);
    }

    [Test]
    public void Section_WritesToLog()
    {
        // Arrange
        TestLogger.ConfigureLogging(_outputSettings, _testDirectory);
        TestLogger.StartTestLogging("SectionTest");
        var title = "Test Section";

        // Act
        TestLogger.Section(title);

        // Assert
        var logContent = GetLatestLogContent();
        StringAssert.Contains(title, logContent);
        StringAssert.Contains("-----", logContent);
    }

    private Test CreateTestWithSteps(string testName, int stepCount)
    {
        var test = new Test { Name = testName };
        for (int i = 0; i < stepCount; i++)
        {
            var step = new TestStep
            {
                DisplayName = $"Step {i + 1}",
                Id = $"step-{i + 1}",
                Type = "TEST"
            };
            test.Steps.Add(step);
        }

        return test;
    }

    private string GetLatestLogContent()
    {
        // Wait a bit for log files to be flushed
        Thread.Sleep(100);

        var tracePath = _outputSettings.GetTracePath(_testDirectory);
        var runDirectories = Directory.GetDirectories(tracePath, "TestRun_*");
        if (runDirectories.Length == 0) return "";

        var latestRunDir = runDirectories.OrderByDescending(d => Directory.GetCreationTime(d)).First();

        // Check test-specific logs first
        var testsDirectory = Path.Combine(latestRunDir, "tests");
        if (Directory.Exists(testsDirectory))
        {
            var testLogFiles = Directory.GetFiles(testsDirectory, "*.log");
            if (testLogFiles.Length > 0)
            {
                var latestTestLog = testLogFiles.OrderByDescending(f => File.GetLastWriteTime(f)).First();
                return File.ReadAllText(latestTestLog);
            }
        }

        // Fall back to general log
        var generalLogFile = Path.Combine(latestRunDir, "General_Log.log");
        if (File.Exists(generalLogFile))
        {
            return File.ReadAllText(generalLogFile);
        }

        return "";
    }


}
