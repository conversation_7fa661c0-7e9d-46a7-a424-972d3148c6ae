2025-06-30T09:03:41.8049378+05:30 [Information] [----------] 1 step from Teardown
2025-06-30T09:03:41.8055407+05:30 [Information] Set Value to Step '7126987c-0459-42ea-bcd5-25fa7b1047a4' name:
"Clean up Database"
2025-06-30T09:03:41.8056529+05:30 [Information] Set Value to Step '7126987c-0459-42ea-bcd5-25fa7b1047a4' description:
"Tear down the database state"
2025-06-30T09:03:41.8057527+05:30 [Information] [ RUN      ] Teardown > Clean up Database
2025-06-30T09:03:41.8060642+05:30 [Information] Executing single instance of step 'Clean up Database'.
2025-06-30T09:03:41.8061867+05:30 [Information] Executing step 'Clean up Database' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "7126987c-0459-42ea-bcd5-25fa7b1047a4",
        "tables": {},
        "variables": {},
        "name": "Clean up Database",
        "description": "Tear down the database state"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:03:41.8063800+05:30 [Information] Processing $var: path 'baseUrl'
2025-06-30T09:03:41.8064722+05:30 [Information] Checking if 'baseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:03:41.8065343+05:30 [Information] Resolving path 'baseUrl' using global variables.
2025-06-30T09:03:41.8065838+05:30 [Information] Resolved JPath '$var:baseUrl' to 'https://httpbin.org'
2025-06-30T09:03:41.8066259+05:30 [Information] Resolved template pattern '{{$var:baseUrl}}' to 'https://httpbin.org' in '{{$var:baseUrl}}/post'
2025-06-30T09:03:41.8066727+05:30 [Information] Resolved template '{{$var:baseUrl}}/post' to 'https://httpbin.org/post'
2025-06-30T09:03:41.8068331+05:30 [Information] Set Value to Step '7126987c-0459-42ea-bcd5-25fa7b1047a4' input:
{
  "Method": "POST",
  "RequestUri": "https://httpbin.org/post",
  "Headers": {
    "Content-Type": "application/json"
  },
  "Content": {
    "action": "cleanup_db"
  }
}
2025-06-30T09:03:42.9280169+05:30 [Information] Set Value to Step '7126987c-0459-42ea-bcd5-25fa7b1047a4' output:
{
  "statusCode": 200,
  "reasonPhrase": "OK",
  "headers": [
    {
      "Key": "Date",
      "Value": [
        "Mon, 30 Jun 2025 03:33:48 GMT"
      ]
    },
    {
      "Key": "Connection",
      "Value": [
        "keep-alive"
      ]
    },
    {
      "Key": "Server",
      "Value": [
        "gunicorn/19.9.0"
      ]
    },
    {
      "Key": "Access-Control-Allow-Origin",
      "Value": [
        "*"
      ]
    },
    {
      "Key": "Access-Control-Allow-Credentials",
      "Value": [
        "true"
      ]
    }
  ],
  "content": {
    "args": {},
    "data": "{\r\n  \"action\": \"cleanup_db\"\r\n}",
    "files": {},
    "form": {},
    "headers": {
      "Content-Length": "30",
      "Content-Type": "application/json; charset=utf-8",
      "Host": "httpbin.org",
      "X-Amzn-Trace-Id": "Root=1-6862059b-48a4727e2e3abbb14957abd2"
    },
    "json": {
      "action": "cleanup_db"
    },
    "origin": "**************",
    "url": "https://httpbin.org/post"
  }
}
2025-06-30T09:03:42.9288256+05:30 [Information] Set Value to Step '7126987c-0459-42ea-bcd5-25fa7b1047a4' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T03:33:41.8060614Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:03:42.9292424+05:30 [Information] Set Value to Step '7126987c-0459-42ea-bcd5-25fa7b1047a4' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 1122,
  "startTime": "2025-06-30T03:33:41.8060614Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:03:42.9296271+05:30 [Information] Step 'Clean up Database' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "7126987c-0459-42ea-bcd5-25fa7b1047a4",
        "tables": {},
        "variables": {},
        "name": "Clean up Database",
        "description": "Tear down the database state",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "action": "cleanup_db"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 03:33:48 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"action\": \"cleanup_db\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "30",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-6862059b-48a4727e2e3abbb14957abd2"
            },
            "json": {
              "action": "cleanup_db"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 1122,
          "startTime": "2025-06-30T03:33:41.8060614Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:03:42.9298222+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:03:42.9300015+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:03:42.9301158+05:30 [Information] No performance data found for step 'Clean up Database' (http)
2025-06-30T09:03:42.9302252+05:30 [Information] [ OK       ]
2025-06-30T09:03:42.9309983+05:30 [Information] [----------] 1 step from Teardown (1125 ms total)
