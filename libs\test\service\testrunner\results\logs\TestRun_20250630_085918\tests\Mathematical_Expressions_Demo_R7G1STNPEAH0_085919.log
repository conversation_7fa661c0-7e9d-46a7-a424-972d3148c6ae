2025-06-30T08:59:19.8292514+05:30 [Information] [----------] 6 steps from Mathematical Expressions Demo
2025-06-30T08:59:19.8307623+05:30 [Information] Set Value to Step 'e716963e-edbb-4a0d-9d51-11123ff312e4' name:
"Baseline Performance Test"
2025-06-30T08:59:19.8310129+05:30 [Information] [ RUN      ] Mathematical Expressions Demo > Baseline Performance Test
2025-06-30T08:59:19.8315489+05:30 [Information] Executing single instance of step 'Baseline Performance Test'.
2025-06-30T08:59:19.8317709+05:30 [Information] Executing step 'Baseline Performance Test' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "e716963e-edbb-4a0d-9d51-11123ff312e4",
        "tables": {},
        "variables": {},
        "name": "Baseline Performance Test"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaseUrl": "https://httpbin.org",
      "BaselineResponseTime": 120,
      "MaxRegressionPercent": 150,
      "BufferTime": 10,
      "LightweightThreshold": 50,
      "ComplexThreshold": 200,
      "SLALimit": 500
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T08:59:19.8353345+05:30 [Information] Processing $var: path 'BaseUrl'
2025-06-30T08:59:19.8355347+05:30 [Information] Checking if 'BaseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T08:59:19.8356306+05:30 [Information] Resolving path 'BaseUrl' using global variables.
2025-06-30T08:59:19.8358266+05:30 [Information] Resolved JPath '$var:BaseUrl' to 'https://httpbin.org'
2025-06-30T08:59:19.8359472+05:30 [Information] Resolved template pattern '{{$var:BaseUrl}}' to 'https://httpbin.org' in '{{$var:BaseUrl}}/delay/1'
2025-06-30T08:59:19.8360464+05:30 [Information] Resolved template '{{$var:BaseUrl}}/delay/1' to 'https://httpbin.org/delay/1'
2025-06-30T08:59:19.8363746+05:30 [Information] Set Value to Step 'e716963e-edbb-4a0d-9d51-11123ff312e4' input:
{
  "Method": "GET",
  "RequestUri": "https://httpbin.org/delay/1",
  "Headers": {}
}
2025-06-30T08:59:19.8365058+05:30 [Information] Delaying step execution for 100ms
2025-06-30T08:59:22.3035849+05:30 [Information] Set Value to Step 'e716963e-edbb-4a0d-9d51-11123ff312e4' output:
{
  "statusCode": 200,
  "reasonPhrase": "OK",
  "headers": [
    {
      "Key": "Date",
      "Value": [
        "Mon, 30 Jun 2025 03:29:27 GMT"
      ]
    },
    {
      "Key": "Connection",
      "Value": [
        "keep-alive"
      ]
    },
    {
      "Key": "Server",
      "Value": [
        "gunicorn/19.9.0"
      ]
    },
    {
      "Key": "Access-Control-Allow-Origin",
      "Value": [
        "*"
      ]
    },
    {
      "Key": "Access-Control-Allow-Credentials",
      "Value": [
        "true"
      ]
    }
  ],
  "content": {
    "args": {},
    "data": "",
    "files": {},
    "form": {},
    "headers": {
      "Host": "httpbin.org",
      "X-Amzn-Trace-Id": "Root=1-68620496-7ffd604914637ead2a705514"
    },
    "origin": "**************",
    "url": "https://httpbin.org/delay/1"
  }
}
2025-06-30T08:59:22.3040843+05:30 [Information] Resolving path 'performance.executionTimeMs' using global context.
2025-06-30T08:59:22.3042093+05:30 [Information] Resolved JPath 'performance.executionTimeMs' to ''
2025-06-30T08:59:22.3043616+05:30 [Information] Set Value to Step 'e716963e-edbb-4a0d-9d51-11123ff312e4' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T03:29:19.8315428Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T08:59:22.3054418+05:30 [Information] Run Asserter : {"Expression1":{"Value":"2000","ErrorMesage":"Baseline too slow: {{$curStep:performance.executionTimeMs}}ms > 2000ms"},"Expression2":{"JPath":"$curStep:performance.executionTimeMs","ErrorMesage":"Baseline too slow: {{$curStep:performance.executionTimeMs}}ms > 2000ms"},"ErrorMesage":"Baseline too slow: {{$curStep:performance.executionTimeMs}}ms > 2000ms"}
2025-06-30T08:59:22.3055846+05:30 [Information] Resolving path 'performance.executionTimeMs' using $curStep:
2025-06-30T08:59:22.3056847+05:30 [Information] Resolved JPath '$curStep:performance.executionTimeMs' to '0'
2025-06-30T08:59:22.3061593+05:30 [Error] Asserter error : {"Expression1":{"Value":"2000","ErrorMesage":"Baseline too slow: {{$curStep:performance.executionTimeMs}}ms > 2000ms"},"Expression2":{"JPath":"$curStep:performance.executionTimeMs","ErrorMesage":"Baseline too slow: {{$curStep:performance.executionTimeMs}}ms > 2000ms"},"ErrorMesage":"Baseline too slow: {{$curStep:performance.executionTimeMs}}ms > 2000ms"}
2025-06-30T08:59:22.3067781+05:30 [Error] Assert Failure : Less(2000, 0)
2025-06-30T08:59:22.3070314+05:30 [Information] Failed to execute step 'Baseline Performance Test': Baseline too slow: {{$curStep:performance.executionTimeMs}}ms > 2000ms
2025-06-30T08:59:22.3071383+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T08:59:22.3072065+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T08:59:22.3072878+05:30 [Information] No performance data found for step 'Baseline Performance Test' (http)
2025-06-30T08:59:22.3075362+05:30 [Error] Baseline too slow: {{$curStep:performance.executionTimeMs}}ms > 2000ms
2025-06-30T08:59:22.3079680+05:30 [Error] [ FAILED   ]
2025-06-30T08:59:22.3086274+05:30 [Information] [----------] 6 steps from Mathematical Expressions Demo (2478 ms total)
