2025-06-30T09:37:32.7761147+05:30 [Information] [----------] 2 steps from Testing MultiStep with JOSNPath
2025-06-30T09:37:32.7766470+05:30 [Information] Set Value to Step 'a1490b6c-e29b-4608-a304-785387712b03' name:
"GET Token"
2025-06-30T09:37:32.7767991+05:30 [Information] Set Value to Step 'a1490b6c-e29b-4608-a304-785387712b03' description:
"Step to get token"
2025-06-30T09:37:32.7769116+05:30 [Information] [ RUN      ] Testing MultiStep with JOSNPath > GET Token
2025-06-30T09:37:32.7771993+05:30 [Information] Executing single instance of step 'GET Token'.
2025-06-30T09:37:32.7772909+05:30 [Information] Executing step 'GET Token' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "a1490b6c-e29b-4608-a304-785387712b03",
        "tables": {},
        "variables": {},
        "name": "GET Token",
        "description": "Step to get token"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:37:32.7774870+05:30 [Information] Processing $var: path 'baseUrl'
2025-06-30T09:37:32.7775715+05:30 [Information] Checking if 'baseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:32.7776284+05:30 [Information] Resolving path 'baseUrl' using global variables.
2025-06-30T09:37:32.7776785+05:30 [Information] Resolved JPath '$var:baseUrl' to 'https://httpbin.org'
2025-06-30T09:37:32.7777229+05:30 [Information] Resolved template pattern '{{$var:baseUrl}}' to 'https://httpbin.org' in '{{$var:baseUrl}}/post'
2025-06-30T09:37:32.7777726+05:30 [Information] Resolved template '{{$var:baseUrl}}/post' to 'https://httpbin.org/post'
2025-06-30T09:37:32.7778969+05:30 [Information] Set Value to Step 'a1490b6c-e29b-4608-a304-785387712b03' input:
{
  "Method": "POST",
  "RequestUri": "https://httpbin.org/post",
  "Headers": {
    "Content-Type": "application/json"
  },
  "Content": {
    "key": "key_value"
  }
}
2025-06-30T09:37:33.5270528+05:30 [Information] Set Value to Step 'a1490b6c-e29b-4608-a304-785387712b03' output:
{
  "statusCode": 200,
  "reasonPhrase": "OK",
  "headers": [
    {
      "Key": "Date",
      "Value": [
        "Mon, 30 Jun 2025 04:07:38 GMT"
      ]
    },
    {
      "Key": "Connection",
      "Value": [
        "keep-alive"
      ]
    },
    {
      "Key": "Server",
      "Value": [
        "gunicorn/19.9.0"
      ]
    },
    {
      "Key": "Access-Control-Allow-Origin",
      "Value": [
        "*"
      ]
    },
    {
      "Key": "Access-Control-Allow-Credentials",
      "Value": [
        "true"
      ]
    }
  ],
  "content": {
    "args": {},
    "data": "{\r\n  \"key\": \"key_value\"\r\n}",
    "files": {},
    "form": {},
    "headers": {
      "Content-Length": "26",
      "Content-Type": "application/json; charset=utf-8",
      "Host": "httpbin.org",
      "X-Amzn-Trace-Id": "Root=1-68620d8a-445a778d4e24ee5c0fb0c679"
    },
    "json": {
      "key": "key_value"
    },
    "origin": "**************",
    "url": "https://httpbin.org/post"
  }
}
2025-06-30T09:37:33.5278036+05:30 [Information] Resolving path 'output.content.json.key' using step input/output.
2025-06-30T09:37:33.5282850+05:30 [Information] Resolved JPath 'output.content.json.key' to 'key_value'
2025-06-30T09:37:33.5289091+05:30 [Information] Set Value to Step 'a1490b6c-e29b-4608-a304-785387712b03' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:07:32.7771975Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:37:33.5292684+05:30 [Information] Run Asserter : {"Expression1":{"Value":"200","ErrorMesage":null},"Expression2":{"JPath":"$curStep:$.output.statusCode","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:37:33.5295214+05:30 [Information] Resolving path '$.output.statusCode' using $curStep:
2025-06-30T09:37:33.5297943+05:30 [Information] Resolved JPath '$curStep:$.output.statusCode' to '200'
2025-06-30T09:37:33.5302134+05:30 [Information] Set Value to Step 'a1490b6c-e29b-4608-a304-785387712b03' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 752,
  "startTime": "2025-06-30T04:07:32.7771975Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:37:33.5307964+05:30 [Information] Step 'GET Token' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "a1490b6c-e29b-4608-a304-785387712b03",
        "tables": {},
        "variables": {},
        "name": "GET Token",
        "description": "Step to get token",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "key": "key_value"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:07:38 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"key\": \"key_value\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "26",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620d8a-445a778d4e24ee5c0fb0c679"
            },
            "json": {
              "key": "key_value"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 752,
          "startTime": "2025-06-30T04:07:32.7771975Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "token": "key_value"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:37:33.5313560+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:37:33.5316471+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:37:33.5318450+05:30 [Information] No performance data found for step 'GET Token' (http)
2025-06-30T09:37:33.5320454+05:30 [Information] [ OK       ]
2025-06-30T09:37:33.5338663+05:30 [Information] Set Value to Step '992ed2c1-88d2-4322-9a15-1b6b51eb5ba4' name:
"Step to test POST request with token"
2025-06-30T09:37:33.5341794+05:30 [Information] Set Value to Step '992ed2c1-88d2-4322-9a15-1b6b51eb5ba4' description:
"POST request with variable in body"
2025-06-30T09:37:33.5342700+05:30 [Information] [ RUN      ] Testing MultiStep with JOSNPath > Step to test POST request with token
2025-06-30T09:37:33.5348475+05:30 [Information] Executing single instance of step 'Step to test POST request with token'.
2025-06-30T09:37:33.5349862+05:30 [Information] Executing step 'Step to test POST request with token' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "a1490b6c-e29b-4608-a304-785387712b03",
        "tables": {},
        "variables": {},
        "name": "GET Token",
        "description": "Step to get token",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "key": "key_value"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:07:38 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"key\": \"key_value\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "26",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620d8a-445a778d4e24ee5c0fb0c679"
            },
            "json": {
              "key": "key_value"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 752,
          "startTime": "2025-06-30T04:07:32.7771975Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      },
      {
        "id": "992ed2c1-88d2-4322-9a15-1b6b51eb5ba4",
        "tables": {},
        "variables": {},
        "name": "Step to test POST request with token",
        "description": "POST request with variable in body"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "token": "key_value"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:37:33.5350730+05:30 [Information] Processing $var: path 'baseUrl'
2025-06-30T09:37:33.5351437+05:30 [Information] Checking if 'baseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:33.5352003+05:30 [Information] Resolving path 'baseUrl' using global variables.
2025-06-30T09:37:33.5352430+05:30 [Information] Resolved JPath '$var:baseUrl' to 'https://httpbin.org'
2025-06-30T09:37:33.5352911+05:30 [Information] Resolved template pattern '{{$var:baseUrl}}' to 'https://httpbin.org' in '{{$var:baseUrl}}/post'
2025-06-30T09:37:33.5353323+05:30 [Information] Resolved template '{{$var:baseUrl}}/post' to 'https://httpbin.org/post'
2025-06-30T09:37:33.5353839+05:30 [Information] Processing $var: path 'token'
2025-06-30T09:37:33.5354258+05:30 [Information] Checking if 'token' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:33.5354666+05:30 [Information] Resolving path 'token' using global variables.
2025-06-30T09:37:33.5355052+05:30 [Information] Resolved JPath '$var:token' to 'key_value'
2025-06-30T09:37:33.5355457+05:30 [Information] Resolved template pattern '{{$var:token}}' to 'key_value' in 'Bearer {{$var:token}}'
2025-06-30T09:37:33.5355842+05:30 [Information] Resolved template 'Bearer {{$var:token}}' to 'Bearer key_value'
2025-06-30T09:37:33.5356387+05:30 [Information] Processing $var: path 'baseUrl'
2025-06-30T09:37:33.5356866+05:30 [Information] Checking if 'baseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:33.5357263+05:30 [Information] Resolving path 'baseUrl' using global variables.
2025-06-30T09:37:33.5357647+05:30 [Information] Resolved JPath '$var:baseUrl' to 'https://httpbin.org'
2025-06-30T09:37:33.5358013+05:30 [Information] Resolved template pattern '{{$var:baseUrl}}' to 'https://httpbin.org' in '{
  "keyFromVar": "{{$var:baseUrl}}",
  "keyFromJPath": "{{$var:token}}"
}'
2025-06-30T09:37:33.5358414+05:30 [Information] Processing $var: path 'token'
2025-06-30T09:37:33.5358800+05:30 [Information] Checking if 'token' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:33.5359188+05:30 [Information] Resolving path 'token' using global variables.
2025-06-30T09:37:33.5359576+05:30 [Information] Resolved JPath '$var:token' to 'key_value'
2025-06-30T09:37:33.5359964+05:30 [Information] Resolved template pattern '{{$var:token}}' to 'key_value' in '{
  "keyFromVar": "{{$var:baseUrl}}",
  "keyFromJPath": "{{$var:token}}"
}'
2025-06-30T09:37:33.5360354+05:30 [Information] Resolved template '{
  "keyFromVar": "{{$var:baseUrl}}",
  "keyFromJPath": "{{$var:token}}"
}' to '{
  "keyFromVar": "https://httpbin.org",
  "keyFromJPath": "key_value"
}'
2025-06-30T09:37:33.5361034+05:30 [Information] Set Value to Step '992ed2c1-88d2-4322-9a15-1b6b51eb5ba4' input:
{
  "Method": "POST",
  "RequestUri": "https://httpbin.org/post",
  "Headers": {
    "Content-Type": "application/json",
    "Authorization": "Bearer key_value"
  },
  "Content": {
    "keyFromVar": "https://httpbin.org",
    "keyFromJPath": "key_value"
  }
}
2025-06-30T09:37:34.2985366+05:30 [Information] Set Value to Step '992ed2c1-88d2-4322-9a15-1b6b51eb5ba4' output:
{
  "statusCode": 200,
  "reasonPhrase": "OK",
  "headers": [
    {
      "Key": "Date",
      "Value": [
        "Mon, 30 Jun 2025 04:07:39 GMT"
      ]
    },
    {
      "Key": "Connection",
      "Value": [
        "keep-alive"
      ]
    },
    {
      "Key": "Server",
      "Value": [
        "gunicorn/19.9.0"
      ]
    },
    {
      "Key": "Access-Control-Allow-Origin",
      "Value": [
        "*"
      ]
    },
    {
      "Key": "Access-Control-Allow-Credentials",
      "Value": [
        "true"
      ]
    }
  ],
  "content": {
    "args": {},
    "data": "{\r\n  \"keyFromVar\": \"https://httpbin.org\",\r\n  \"keyFromJPath\": \"key_value\"\r\n}",
    "files": {},
    "form": {},
    "headers": {
      "Authorization": "Bearer key_value",
      "Content-Length": "75",
      "Content-Type": "application/json; charset=utf-8",
      "Host": "httpbin.org",
      "X-Amzn-Trace-Id": "Root=1-68620d8b-2fa3e8e6076d807f2d7ffd8c"
    },
    "json": {
      "keyFromJPath": "key_value",
      "keyFromVar": "https://httpbin.org"
    },
    "origin": "**************",
    "url": "https://httpbin.org/post"
  }
}
2025-06-30T09:37:34.2990077+05:30 [Information] Set Value to Step '992ed2c1-88d2-4322-9a15-1b6b51eb5ba4' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:07:33.5348455Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:37:34.2992137+05:30 [Information] Run Asserter : {"Expression1":{"Value":"200","ErrorMesage":null},"Expression2":{"JPath":"output.statusCode","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:37:34.2993725+05:30 [Information] Resolving path 'output.statusCode' using step input/output.
2025-06-30T09:37:34.2995405+05:30 [Information] Resolved JPath 'output.statusCode' to '200'
2025-06-30T09:37:34.2996949+05:30 [Information] Set Value to Step '992ed2c1-88d2-4322-9a15-1b6b51eb5ba4' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 763,
  "startTime": "2025-06-30T04:07:33.5348455Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:37:34.2998438+05:30 [Information] Step 'Step to test POST request with token' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "a1490b6c-e29b-4608-a304-785387712b03",
        "tables": {},
        "variables": {},
        "name": "GET Token",
        "description": "Step to get token",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "key": "key_value"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:07:38 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"key\": \"key_value\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "26",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620d8a-445a778d4e24ee5c0fb0c679"
            },
            "json": {
              "key": "key_value"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 752,
          "startTime": "2025-06-30T04:07:32.7771975Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      },
      {
        "id": "992ed2c1-88d2-4322-9a15-1b6b51eb5ba4",
        "tables": {},
        "variables": {},
        "name": "Step to test POST request with token",
        "description": "POST request with variable in body",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json",
            "Authorization": "Bearer key_value"
          },
          "Content": {
            "keyFromVar": "https://httpbin.org",
            "keyFromJPath": "key_value"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:07:39 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"keyFromVar\": \"https://httpbin.org\",\r\n  \"keyFromJPath\": \"key_value\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Authorization": "Bearer key_value",
              "Content-Length": "75",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620d8b-2fa3e8e6076d807f2d7ffd8c"
            },
            "json": {
              "keyFromJPath": "key_value",
              "keyFromVar": "https://httpbin.org"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 763,
          "startTime": "2025-06-30T04:07:33.5348455Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "token": "key_value"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:37:34.2999795+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:37:34.3001076+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:37:34.3001906+05:30 [Information] No performance data found for step 'Step to test POST request with token' (http)
2025-06-30T09:37:34.3002861+05:30 [Information] [ OK       ]
2025-06-30T09:37:34.3008190+05:30 [Information] [----------] 2 steps from Testing MultiStep with JOSNPath (1524 ms total)
