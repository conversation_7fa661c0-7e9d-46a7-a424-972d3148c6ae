2025-06-30T08:57:33.0425096+05:30 [Information] [----------] 6 steps from Mathematical Expressions Demo
2025-06-30T08:57:33.0440067+05:30 [Information] Set Value to Step 'f10bca26-6950-4962-ba28-8cb7cd66549f' name:
"Baseline Performance Test"
2025-06-30T08:57:33.0441669+05:30 [Information] [ RUN      ] Mathematical Expressions Demo > Baseline Performance Test
2025-06-30T08:57:33.0446423+05:30 [Information] Executing single instance of step 'Baseline Performance Test'.
2025-06-30T08:57:33.0448147+05:30 [Information] Executing step 'Baseline Performance Test' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "f10bca26-6950-4962-ba28-8cb7cd66549f",
        "tables": {},
        "variables": {},
        "name": "Baseline Performance Test"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaseUrl": "https://api.example.com",
      "BaselineResponseTime": 120,
      "MaxRegressionPercent": 150,
      "BufferTime": 10,
      "LightweightThreshold": 50,
      "ComplexThreshold": 200,
      "SLALimit": 500
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T08:57:33.0450519+05:30 [Information] Processing $var: path 'BaseUrl'
2025-06-30T08:57:33.0452253+05:30 [Information] Checking if 'BaseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T08:57:33.0453924+05:30 [Information] Resolving path 'BaseUrl' using global variables.
2025-06-30T08:57:33.0454816+05:30 [Information] Resolved JPath '$var:BaseUrl' to 'https://api.example.com'
2025-06-30T08:57:33.0455364+05:30 [Information] Resolved template pattern '{{$var:BaseUrl}}' to 'https://api.example.com' in '{{$var:BaseUrl}}/lightweight-endpoint'
2025-06-30T08:57:33.0456155+05:30 [Information] Resolved template '{{$var:BaseUrl}}/lightweight-endpoint' to 'https://api.example.com/lightweight-endpoint'
2025-06-30T08:57:33.0458575+05:30 [Information] Set Value to Step 'f10bca26-6950-4962-ba28-8cb7cd66549f' input:
{
  "Method": "GET",
  "RequestUri": "https://api.example.com/lightweight-endpoint",
  "Headers": {}
}
2025-06-30T08:57:33.0459630+05:30 [Information] Delaying step execution for 100ms
2025-06-30T08:57:33.4409407+05:30 [Information] Failed to execute step 'Baseline Performance Test': Failed to execute step : No such host is known. (api.example.com:443)
2025-06-30T08:57:33.4413816+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T08:57:33.4415311+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T08:57:33.4416064+05:30 [Information] No performance data found for step 'Baseline Performance Test' (http)
2025-06-30T08:57:33.4419500+05:30 [Error] Failed to execute step : No such host is known. (api.example.com:443)
2025-06-30T08:57:33.4421496+05:30 [Error] [ FAILED   ]
2025-06-30T08:57:33.4425553+05:30 [Information] [----------] 6 steps from Mathematical Expressions Demo (399 ms total)
