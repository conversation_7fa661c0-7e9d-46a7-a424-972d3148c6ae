2025-06-30T09:22:32.1372148+05:30 [Information] Global variables set to: baseUrl: https://httpbin.org, serverIp: 127.0.0.1, serverPort: 60606
2025-06-30T09:22:32.1515209+05:30 [Information] Loading tests from D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc with pattern *math-expressions-simple.yaml
2025-06-30T09:22:32.1555324+05:30 [Information] Loading test from D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc\math-expressions-simple.yaml
2025-06-30T09:22:32.1679146+05:30 [Information] Global variables set to: 
2025-06-30T09:22:32.1690333+05:30 [Information] [==========] Running 1 test from Yaml files
2025-06-30T09:22:32.1701875+05:30 [Information] [-------------------------------------    Startup     -------------------------------------]
2025-06-30T09:22:32.1907994+05:30 [Information] [----------] 1 step from Startup
2025-06-30T09:22:32.1936064+05:30 [Information] [ RUN      ] Startup > Initialize Database
2025-06-30T09:22:33.3225564+05:30 [Information] [ OK       ]
2025-06-30T09:22:33.3239629+05:30 [Information] [----------] 1 step from Startup (1131 ms total)
2025-06-30T09:22:33.3275686+05:30 [Information] Global variables set to: curDir: D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc, baseUrl: https://httpbin.org, serverIp: 127.0.0.1, serverPort: 60606, dbInitStatus: initialize_db
2025-06-30T09:22:33.3282264+05:30 [Information] [------------------------------------- Test Execution -------------------------------------]
2025-06-30T09:22:33.3295303+05:30 [Information] [----------] 6 steps from Simple Mathematical Expressions Demo
2025-06-30T09:22:33.3308826+05:30 [Information] [ RUN      ] Simple Mathematical Expressions Demo > Fast Baseline Operation
2025-06-30T09:22:33.4325528+05:30 [Information] [ OK       ]
2025-06-30T09:22:33.4331249+05:30 [Information] [ RUN      ] Simple Mathematical Expressions Demo > Regression Threshold Test
2025-06-30T09:22:34.2262611+05:30 [Error] Assert Failure : Less(1.5, 0)
2025-06-30T09:22:34.2275755+05:30 [Error] Performance regression detected: {{$curStep:performance.executionTimeMs}}ms vs allowed {{$var:MathBaseline * $var:MaxRegressionPercent / 100}}ms ({{$var:MathBaseline}}ms * {{$var:MaxRegressionPercent}}% / 100)
2025-06-30T09:22:34.2276888+05:30 [Error] [ FAILED   ]
2025-06-30T09:22:34.2281895+05:30 [Information] [----------] 6 steps from Simple Mathematical Expressions Demo (898 ms total)
2025-06-30T09:22:34.2286282+05:30 [Information] [-------------------------------------    Teardown    -------------------------------------]
2025-06-30T09:22:34.2298009+05:30 [Information] [----------] 1 step from Teardown
2025-06-30T09:22:34.2305573+05:30 [Information] [ RUN      ] Teardown > Clean up Database
2025-06-30T09:22:35.0438352+05:30 [Information] [ OK       ]
2025-06-30T09:22:35.0442251+05:30 [Information] [----------] 1 step from Teardown (813 ms total)
2025-06-30T09:22:35.0447489+05:30 [Information] Global variables set to: curDir: D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc, baseUrl: https://httpbin.org, serverIp: 127.0.0.1, serverPort: 60606, dbInitStatus: initialize_db
2025-06-30T09:22:35.0448630+05:30 [Information] [-------------------------------------  Test Results  -------------------------------------]
2025-06-30T09:22:35.0453927+05:30 [Information] [==========] 1 test from Yaml files ran. (2876 ms total)
2025-06-30T09:22:35.0467421+05:30 [Information] [  PASSED  ] 0 tests.
2025-06-30T09:22:35.0470090+05:30 [Error] [  FAILED  ] 1 test, listed below:
2025-06-30T09:22:35.0473354+05:30 [Error] [  FAILED  ] Simple Mathematical Expressions Demo
