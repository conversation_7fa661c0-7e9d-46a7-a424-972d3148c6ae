# This is the CMakeCache file.
# For build in directory: d:/kibisoft/workarea/git/interop/libs/test/service/testlib/tests/unit/build
# It was generated by CMake: C:/Program Files/CMake/bin/cmake.exe
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//Value Computed by CMake
ALL_BUILD_REQUIRES_VS_PACKAGE_RESTORE:STATIC=ON

//Project version
AZURE_PIPELINE_PROJECT_VERSION:STRING=1.0.0.3186

//Path to a program.
CMAKE_AR:FILEPATH=C:/Program Files/Microsoft Visual Studio/2022/Professional/VC/Tools/MSVC/14.41.34120/bin/Hostx64/x64/lib.exe

//Select the build type.
CMAKE_BUILD_TYPE:STRING=Debug

CMAKE_CONFIGURATION_TYPES:STRING=Debug;Release

//Flags used by the C# compiler during all build types.
CMAKE_CSharp_FLAGS:STRING=/platform:x64 /define:TRACE

//Flags used by the C# compiler during DEBUG builds.
CMAKE_CSharp_FLAGS_DEBUG:STRING=/debug:full /optimize- /warn:3 /errorreport:prompt /define:DEBUG

//Flags used by the C# compiler during MINSIZEREL builds.
CMAKE_CSharp_FLAGS_MINSIZEREL:STRING=/debug:none /optimize

//Flags used by the C# compiler during RELEASE builds.
CMAKE_CSharp_FLAGS_RELEASE:STRING=/debug:none /optimize  /warn:1  /errorreport:queue

//Flags used by the C# compiler during RELWITHDEBINFO builds.
CMAKE_CSharp_FLAGS_RELWITHDEBINFO:STRING=/debug:full /optimize-

//Libraries linked by default with all C# applications.
CMAKE_CSharp_STANDARD_LIBRARIES:STRING=System

//Flags used by the CXX compiler during all build types.
CMAKE_CXX_FLAGS:STRING=/DWIN32 /D_WINDOWS /EHsc

//Flags used by the CXX compiler during DEBUG builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=/Zi /Ob0 /Od /RTC1

//Flags used by the CXX compiler during MINSIZEREL builds.
CMAKE_CXX_FLAGS_MINSIZEREL:STRING=/O1 /Ob1 /DNDEBUG

//Flags used by the CXX compiler during RELEASE builds.
CMAKE_CXX_FLAGS_RELEASE:STRING=/O2 /Ob2 /DNDEBUG

//Flags used by the CXX compiler during RELWITHDEBINFO builds.
CMAKE_CXX_FLAGS_RELWITHDEBINFO:STRING=/Zi /O2 /Ob1 /DNDEBUG

//Libraries linked by default with all C++ applications.
CMAKE_CXX_STANDARD_LIBRARIES:STRING=kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib

//Flags used by the C compiler during all build types.
CMAKE_C_FLAGS:STRING=/DWIN32 /D_WINDOWS

//Flags used by the C compiler during DEBUG builds.
CMAKE_C_FLAGS_DEBUG:STRING=/Zi /Ob0 /Od /RTC1

//Flags used by the C compiler during MINSIZEREL builds.
CMAKE_C_FLAGS_MINSIZEREL:STRING=/O1 /Ob1 /DNDEBUG

//Flags used by the C compiler during RELEASE builds.
CMAKE_C_FLAGS_RELEASE:STRING=/O2 /Ob2 /DNDEBUG

//Flags used by the C compiler during RELWITHDEBINFO builds.
CMAKE_C_FLAGS_RELWITHDEBINFO:STRING=/Zi /O2 /Ob1 /DNDEBUG

//Libraries linked by default with all C applications.
CMAKE_C_STANDARD_LIBRARIES:STRING=kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib

//Flags used by the linker during all build types.
CMAKE_EXE_LINKER_FLAGS:STRING=

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Value Computed by CMake.
CMAKE_FIND_PACKAGE_REDIRECTS_DIR:STATIC=D:/kibisoft/workarea/git/interop/libs/test/service/testlib/tests/unit/build/CMakeFiles/pkgRedirects

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=C:/Program Files (x86)/testlib_unit_tests

//Path to a program.
CMAKE_LINKER:FILEPATH=C:/Program Files/Microsoft Visual Studio/2022/Professional/VC/Tools/MSVC/14.41.34120/bin/Hostx64/x64/link.exe

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=/machine:x64

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=/debug /INCREMENTAL

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=/INCREMENTAL:NO

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=/INCREMENTAL:NO

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=/debug /INCREMENTAL

//Path to a program.
CMAKE_MT:FILEPATH=CMAKE_MT-NOTFOUND

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=testlib_unit_tests

//Value Computed by CMake
CMAKE_PROJECT_VERSION:STATIC=1.0

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MAJOR:STATIC=1

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MINOR:STATIC=0

//Value Computed by CMake
CMAKE_PROJECT_VERSION_PATCH:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_VERSION_TWEAK:STATIC=

//RC compiler
CMAKE_RC_COMPILER:FILEPATH=rc

//Flags for Windows Resource Compiler during all build types.
CMAKE_RC_FLAGS:STRING=-DWIN32

//Flags for Windows Resource Compiler during DEBUG builds.
CMAKE_RC_FLAGS_DEBUG:STRING=-D_DEBUG

//Flags for Windows Resource Compiler during MINSIZEREL builds.
CMAKE_RC_FLAGS_MINSIZEREL:STRING=

//Flags for Windows Resource Compiler during RELEASE builds.
CMAKE_RC_FLAGS_RELEASE:STRING=

//Flags for Windows Resource Compiler during RELWITHDEBINFO builds.
CMAKE_RC_FLAGS_RELWITHDEBINFO:STRING=

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the linker during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=/machine:x64

//Flags used by the linker during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//Git commit count
GIT_COMMIT_COUNT:STRING=3186

//Git command line client
GIT_EXECUTABLE:FILEPATH=C:/Program Files/Git/cmd/git.exe

//Flag to indicate if the generator is multi-config
MULTI_CONFIG_GENERATOR_FLAG:STRING=OFF

//Path to a file.
SWIG_DIR:PATH=D:/kibisoft/Instllers/tools/swigwin-4.2.1/Lib

//Path to a program.
SWIG_EXECUTABLE:FILEPATH=D:/kibisoft/Instllers/tools/swigwin-4.2.1/swig.exe

//Swig version
SWIG_VERSION:STRING=4.2.1

//Value Computed by CMake
bbclient_BINARY_DIR:STATIC=D:/kibisoft/workarea/git/interop/libs/test/service/testlib/tests/unit/build/testlib/cocshmercur/mercuryswigcs/mercury/cocppbbclie

//Value Computed by CMake
bbclient_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
bbclient_SOURCE_DIR:STATIC=D:/kibisoft/workarea/git/interop/libs/comm/cpp/bbclient

//Value Computed by CMake
boarder_BINARY_DIR:STATIC=D:/kibisoft/workarea/git/interop/libs/test/service/testlib/tests/unit/build/testlib/cocshmercur/mercuryswigcs/mercury/cocppbbclie/cocppboarde

//Value Computed by CMake
boarder_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
boarder_SOURCE_DIR:STATIC=D:/kibisoft/workarea/git/interop/libs/comm/cpp/boarder

//Value Computed by CMake
interop_api_csharp_BINARY_DIR:STATIC=D:/kibisoft/workarea/git/interop/libs/test/service/testlib/tests/unit/build/testlib/apicsharp

//Value Computed by CMake
interop_api_csharp_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
interop_api_csharp_REQUIRES_VS_PACKAGE_RESTORE:STATIC=ON

//Value Computed by CMake
interop_api_csharp_SOURCE_DIR:STATIC=D:/kibisoft/workarea/git/interop/api/csharp

//Value Computed by CMake
mercury_BINARY_DIR:STATIC=D:/kibisoft/workarea/git/interop/libs/test/service/testlib/tests/unit/build/testlib/cocshmercur/mercuryswigcs/mercury

//Value Computed by CMake
mercury_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
mercury_SOURCE_DIR:STATIC=D:/kibisoft/workarea/git/interop/libs/comm/cpp/mercury

//Value Computed by CMake
mercury_csharp_BINARY_DIR:STATIC=D:/kibisoft/workarea/git/interop/libs/test/service/testlib/tests/unit/build/testlib/cocshmercur

//Value Computed by CMake
mercury_csharp_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
mercury_csharp_REQUIRES_VS_PACKAGE_RESTORE:STATIC=ON

//Value Computed by CMake
mercury_csharp_SOURCE_DIR:STATIC=D:/kibisoft/workarea/git/interop/libs/comm/csharp/mercury-csharp

//Value Computed by CMake
mercury_csharp_swig_BINARY_DIR:STATIC=D:/kibisoft/workarea/git/interop/libs/test/service/testlib/tests/unit/build/testlib/cocshmercur/mercuryswigcs

//Value Computed by CMake
mercury_csharp_swig_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
mercury_csharp_swig_SOURCE_DIR:STATIC=D:/kibisoft/workarea/git/interop/libs/comm/cpp/mercury/swig/csharp

//Value Computed by CMake
openssl_dist_BINARY_DIR:STATIC=D:/kibisoft/workarea/git/interop/libs/test/service/testlib/tests/unit/build/testlib/cocshmercur/mercuryswigcs/mercury/cocppbbclie/3ropeinterf

//Value Computed by CMake
openssl_dist_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
openssl_dist_SOURCE_DIR:STATIC=D:/kibisoft/workarea/git/interop/libs/3rdparty/openssl-3.0.15/interface

//Value Computed by CMake
testlib_BINARY_DIR:STATIC=D:/kibisoft/workarea/git/interop/libs/test/service/testlib/tests/unit/build/testlib

//Value Computed by CMake
testlib_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
testlib_REQUIRES_VS_PACKAGE_RESTORE:STATIC=ON

//Value Computed by CMake
testlib_SOURCE_DIR:STATIC=D:/kibisoft/workarea/git/interop/libs/test/service/testlib

//Value Computed by CMake
testlib_unit_tests_BINARY_DIR:STATIC=D:/kibisoft/workarea/git/interop/libs/test/service/testlib/tests/unit/build

//Value Computed by CMake
testlib_unit_tests_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
testlib_unit_tests_REQUIRES_VS_PACKAGE_RESTORE:STATIC=ON

//Value Computed by CMake
testlib_unit_tests_SOURCE_DIR:STATIC=D:/kibisoft/workarea/git/interop/libs/test/service/testlib/tests/unit

//Value Computed by CMake
timer_BINARY_DIR:STATIC=D:/kibisoft/workarea/git/interop/libs/test/service/testlib/tests/unit/build/testlib/cocshmercur/mercuryswigcs/mercury/cocpptimer

//Value Computed by CMake
timer_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
timer_SOURCE_DIR:STATIC=D:/kibisoft/workarea/git/interop/libs/comm/cpp/timer

//Value Computed by CMake
tiny_proc_lib_BINARY_DIR:STATIC=D:/kibisoft/workarea/git/interop/libs/test/service/testlib/tests/unit/build/testlib/cocshmercur/mercuryswigcs/mercury/cocppbbclie/libsxplatfo/li3rdtinypr

//Value Computed by CMake
tiny_proc_lib_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
tiny_proc_lib_SOURCE_DIR:STATIC=D:/kibisoft/workarea/git/interop/libs/3rdparty/tiny-process-library

//Value Computed by CMake
xplatform_BINARY_DIR:STATIC=D:/kibisoft/workarea/git/interop/libs/test/service/testlib/tests/unit/build/testlib/cocshmercur/mercuryswigcs/mercury/cocppbbclie/libsxplatfo

//Value Computed by CMake
xplatform_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
xplatform_SOURCE_DIR:STATIC=D:/kibisoft/workarea/git/interop/libs/xplatform


########################
# INTERNAL cache entries
########################

//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=d:/kibisoft/workarea/git/interop/libs/test/service/testlib/tests/unit/build
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=4
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=0
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=0
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=C:/Program Files/CMake/bin/cmake.exe
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=C:/Program Files/CMake/bin/cpack.exe
//ADVANCED property for variable: CMAKE_CSharp_FLAGS
CMAKE_CSharp_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CSharp_FLAGS_DEBUG
CMAKE_CSharp_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CSharp_FLAGS_MINSIZEREL
CMAKE_CSharp_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CSharp_FLAGS_RELEASE
CMAKE_CSharp_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CSharp_FLAGS_RELWITHDEBINFO
CMAKE_CSharp_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CSharp_STANDARD_LIBRARIES
CMAKE_CSharp_STANDARD_LIBRARIES-ADVANCED:INTERNAL=1
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=C:/Program Files/CMake/bin/ctest.exe
//ADVANCED property for variable: CMAKE_CXX_FLAGS
CMAKE_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_DEBUG
CMAKE_CXX_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_MINSIZEREL
CMAKE_CXX_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELEASE
CMAKE_CXX_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELWITHDEBINFO
CMAKE_CXX_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_STANDARD_LIBRARIES
CMAKE_CXX_STANDARD_LIBRARIES-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS
CMAKE_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_DEBUG
CMAKE_C_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_MINSIZEREL
CMAKE_C_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELEASE
CMAKE_C_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELWITHDEBINFO
CMAKE_C_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_STANDARD_LIBRARIES
CMAKE_C_STANDARD_LIBRARIES-ADVANCED:INTERNAL=1
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=Unknown
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Visual Studio 17 2022
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=C:/Program Files/Microsoft Visual Studio/2022/Professional
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=D:/kibisoft/workarea/git/interop/libs/test/service/testlib/tests/unit
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//Name of CMakeLists files to read
CMAKE_LIST_FILE_NAME:INTERNAL=CMakeLists.txt
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MT
CMAKE_MT-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=13
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//noop for ranlib
CMAKE_RANLIB:INTERNAL=:
//ADVANCED property for variable: CMAKE_RC_COMPILER
CMAKE_RC_COMPILER-ADVANCED:INTERNAL=1
CMAKE_RC_COMPILER_WORKS:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS
CMAKE_RC_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_DEBUG
CMAKE_RC_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_MINSIZEREL
CMAKE_RC_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_RELEASE
CMAKE_RC_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_RELWITHDEBINFO
CMAKE_RC_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=C:/Program Files/CMake/share/cmake-4.0
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
//Details about finding Git
FIND_PACKAGE_MESSAGE_DETAILS_Git:INTERNAL=[C:/Program Files/Git/cmd/git.exe][v2.37.1.windows.1()]
//Details about finding SWIG
FIND_PACKAGE_MESSAGE_DETAILS_SWIG:INTERNAL=[D:/kibisoft/Instllers/tools/swigwin-4.2.1/swig.exe][D:/kibisoft/Instllers/tools/swigwin-4.2.1/Lib][ ][v4.2.1()]
//ADVANCED property for variable: GIT_EXECUTABLE
GIT_EXECUTABLE-ADVANCED:INTERNAL=1
//Result of TRY_COMPILE
HAVE_SIZEOF_LONG:INTERNAL=TRUE
//Have include stddef.h
HAVE_STDDEF_H:INTERNAL=1
//Have include stdint.h
HAVE_STDINT_H:INTERNAL=1
//Have include sys/types.h
HAVE_SYS_TYPES_H:INTERNAL=1
//CHECK_TYPE_SIZE: sizeof(long)
SIZEOF_LONG:INTERNAL=4
//ADVANCED property for variable: SWIG_DIR
SWIG_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: SWIG_EXECUTABLE
SWIG_EXECUTABLE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: SWIG_VERSION
SWIG_VERSION-ADVANCED:INTERNAL=1

