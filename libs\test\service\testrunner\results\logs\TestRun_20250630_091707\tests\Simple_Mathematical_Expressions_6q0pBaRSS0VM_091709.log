2025-06-30T09:17:09.5698789+05:30 [Information] [----------] 6 steps from Simple Mathematical Expressions Demo
2025-06-30T09:17:09.5714351+05:30 [Information] Set Value to Step '0cef9cfa-c5d0-445a-95ac-a3ed33f4583f' name:
"Fast Baseline Operation"
2025-06-30T09:17:09.5716061+05:30 [Information] [ RUN      ] Simple Mathematical Expressions Demo > Fast Baseline Operation
2025-06-30T09:17:09.5720402+05:30 [Information] Executing single instance of step 'Fast Baseline Operation'.
2025-06-30T09:17:09.5721938+05:30 [Information] Executing step 'Fast Baseline Operation' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "0cef9cfa-c5d0-445a-95ac-a3ed33f4583f",
        "tables": {},
        "variables": {},
        "name": "Fast Baseline Operation"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaselineThreshold": 100,
      "MaxRegressionPercent": 150,
      "BufferTime": 50,
      "FastThreshold": 5000,
      "SlowThreshold": 10000,
      "MathBaseline": 100
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:17:09.5733488+05:30 [Information] Set Value to Step '0cef9cfa-c5d0-445a-95ac-a3ed33f4583f' input:
{
  "command": "ping",
  "arguments": [
    "127.0.0.1",
    "-n",
    "1"
  ],
  "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
  "printOutput": false
}
2025-06-30T09:17:09.5734918+05:30 [Information] Delaying step execution for 50ms
2025-06-30T09:17:09.6523765+05:30 [Information] Set Value to Step '0cef9cfa-c5d0-445a-95ac-a3ed33f4583f' output:
{
  "stdout": "Pinging 127.0.0.1 with 32 bytes of data:\r\nReply from 127.0.0.1: bytes=32 time<1ms TTL=128\r\nPing statistics for 127.0.0.1:\r\n    Packets: Sent = 1, Received = 1, Lost = 0 (0% loss),\r\nApproximate round trip times in milli-seconds:\r\n    Minimum = 0ms, Maximum = 0ms, Average = 0ms",
  "stderr": "",
  "exitCode": 0
}
2025-06-30T09:17:09.6525972+05:30 [Information] Resolving path 'performance.executionTimeMs' using global context.
2025-06-30T09:17:09.6527106+05:30 [Information] Resolved JPath 'performance.executionTimeMs' to ''
2025-06-30T09:17:09.6528064+05:30 [Information] Set Value to Step '0cef9cfa-c5d0-445a-95ac-a3ed33f4583f' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T03:47:09.5720366Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "cmd"
}
2025-06-30T09:17:09.6535896+05:30 [Information] Run Asserter : {"Expression1":{"Value":"0","ErrorMesage":"Ping command failed"},"Expression2":{"JPath":"$curStep:$.output.exitCode","ErrorMesage":"Ping command failed"},"ErrorMesage":"Ping command failed"}
2025-06-30T09:17:09.6537397+05:30 [Information] Resolving path '$.output.exitCode' using $curStep:
2025-06-30T09:17:09.6538345+05:30 [Information] Resolved JPath '$curStep:$.output.exitCode' to '0'
2025-06-30T09:17:09.6549501+05:30 [Information] Run Asserter : {"_lessExpression":{"Expression1":{"Value":"0","ErrorMesage":"Performance data not available"},"Expression2":{"JPath":"$curStep:performance.executionTimeMs","ErrorMesage":"Performance data not available"},"ErrorMesage":null},"Expression1":{"Value":"0","ErrorMesage":"Performance data not available"},"Expression2":{"JPath":"$curStep:performance.executionTimeMs","ErrorMesage":"Performance data not available"},"ErrorMesage":"Performance data not available"}
2025-06-30T09:17:09.6551022+05:30 [Information] Resolving path 'performance.executionTimeMs' using $curStep:
2025-06-30T09:17:09.6552049+05:30 [Information] Resolved JPath '$curStep:performance.executionTimeMs' to '0'
2025-06-30T09:17:09.6555486+05:30 [Information] Set Value to Step '0cef9cfa-c5d0-445a-95ac-a3ed33f4583f' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 82,
  "startTime": "2025-06-30T03:47:09.5720366Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "cmd"
}
2025-06-30T09:17:09.6557269+05:30 [Information] Step 'Fast Baseline Operation' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "0cef9cfa-c5d0-445a-95ac-a3ed33f4583f",
        "tables": {},
        "variables": {},
        "name": "Fast Baseline Operation",
        "input": {
          "command": "ping",
          "arguments": [
            "127.0.0.1",
            "-n",
            "1"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Pinging 127.0.0.1 with 32 bytes of data:\r\nReply from 127.0.0.1: bytes=32 time<1ms TTL=128\r\nPing statistics for 127.0.0.1:\r\n    Packets: Sent = 1, Received = 1, Lost = 0 (0% loss),\r\nApproximate round trip times in milli-seconds:\r\n    Minimum = 0ms, Maximum = 0ms, Average = 0ms",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 82,
          "startTime": "2025-06-30T03:47:09.5720366Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaselineThreshold": 100,
      "MaxRegressionPercent": 150,
      "BufferTime": 50,
      "FastThreshold": 5000,
      "SlowThreshold": 10000,
      "MathBaseline": 100
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:17:09.6558068+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:17:09.6558730+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:17:09.6559614+05:30 [Information] No performance data found for step 'Fast Baseline Operation' (cmd)
2025-06-30T09:17:09.6560515+05:30 [Information] [ OK       ]
2025-06-30T09:17:09.6563876+05:30 [Information] Set Value to Step '319ea466-3f5b-4201-a0d9-eaace341d2f1' name:
"Regression Threshold Test"
2025-06-30T09:17:09.6564703+05:30 [Information] [ RUN      ] Simple Mathematical Expressions Demo > Regression Threshold Test
2025-06-30T09:17:09.6567347+05:30 [Information] Executing single instance of step 'Regression Threshold Test'.
2025-06-30T09:17:09.6569128+05:30 [Information] Executing step 'Regression Threshold Test' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "0cef9cfa-c5d0-445a-95ac-a3ed33f4583f",
        "tables": {},
        "variables": {},
        "name": "Fast Baseline Operation",
        "input": {
          "command": "ping",
          "arguments": [
            "127.0.0.1",
            "-n",
            "1"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Pinging 127.0.0.1 with 32 bytes of data:\r\nReply from 127.0.0.1: bytes=32 time<1ms TTL=128\r\nPing statistics for 127.0.0.1:\r\n    Packets: Sent = 1, Received = 1, Lost = 0 (0% loss),\r\nApproximate round trip times in milli-seconds:\r\n    Minimum = 0ms, Maximum = 0ms, Average = 0ms",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 82,
          "startTime": "2025-06-30T03:47:09.5720366Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "319ea466-3f5b-4201-a0d9-eaace341d2f1",
        "tables": {},
        "variables": {},
        "name": "Regression Threshold Test"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaselineThreshold": 100,
      "MaxRegressionPercent": 150,
      "BufferTime": 50,
      "FastThreshold": 5000,
      "SlowThreshold": 10000,
      "MathBaseline": 100
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:17:09.6570825+05:30 [Information] Set Value to Step '319ea466-3f5b-4201-a0d9-eaace341d2f1' input:
{
  "command": "ping",
  "arguments": [
    "127.0.0.1",
    "-n",
    "2"
  ],
  "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
  "printOutput": false
}
2025-06-30T09:17:09.6571725+05:30 [Information] Delaying step execution for 100ms
2025-06-30T09:17:10.7984091+05:30 [Information] Set Value to Step '319ea466-3f5b-4201-a0d9-eaace341d2f1' output:
{
  "stdout": "Pinging 127.0.0.1 with 32 bytes of data:\r\nReply from 127.0.0.1: bytes=32 time<1ms TTL=128\r\nReply from 127.0.0.1: bytes=32 time<1ms TTL=128\r\nPing statistics for 127.0.0.1:\r\n    Packets: Sent = 2, Received = 2, Lost = 0 (0% loss),\r\nApproximate round trip times in milli-seconds:\r\n    Minimum = 0ms, Maximum = 0ms, Average = 0ms",
  "stderr": "",
  "exitCode": 0
}
2025-06-30T09:17:10.7991537+05:30 [Information] Set Value to Step '319ea466-3f5b-4201-a0d9-eaace341d2f1' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T03:47:09.6567312Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "cmd"
}
2025-06-30T09:17:10.7993843+05:30 [Information] Run Asserter : {"Expression1":{"Value":"0","ErrorMesage":"Ping command failed"},"Expression2":{"JPath":"$curStep:$.output.exitCode","ErrorMesage":"Ping command failed"},"ErrorMesage":"Ping command failed"}
2025-06-30T09:17:10.7995123+05:30 [Information] Resolving path '$.output.exitCode' using $curStep:
2025-06-30T09:17:10.7996213+05:30 [Information] Resolved JPath '$curStep:$.output.exitCode' to '0'
2025-06-30T09:17:10.7997358+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$var:MathBaseline * $var:MaxRegressionPercent / 100}}","ErrorMesage":"Performance regression detected: {{$curStep:performance.executionTimeMs}}ms vs allowed {{$var:MathBaseline * $var:MaxRegressionPercent / 100}}ms ({{$var:MathBaseline}}ms * {{$var:MaxRegressionPercent}}% / 100)"},"Expression2":{"JPath":"$curStep:performance.executionTimeMs","ErrorMesage":"Performance regression detected: {{$curStep:performance.executionTimeMs}}ms vs allowed {{$var:MathBaseline * $var:MaxRegressionPercent / 100}}ms ({{$var:MathBaseline}}ms * {{$var:MaxRegressionPercent}}% / 100)"},"ErrorMesage":"Performance regression detected: {{$curStep:performance.executionTimeMs}}ms vs allowed {{$var:MathBaseline * $var:MaxRegressionPercent / 100}}ms ({{$var:MathBaseline}}ms * {{$var:MaxRegressionPercent}}% / 100)"}
2025-06-30T09:17:10.7998574+05:30 [Information] Processing $var: path 'MathBaseline * $var:MaxRegressionPercent / 100'
2025-06-30T09:17:10.7999696+05:30 [Information] Checking if 'MathBaseline * $var:MaxRegressionPercent / 100' is mathematical expression: hasArithmetic=True, hasArray=False, hasProperty=False, result=True
2025-06-30T09:17:10.8000351+05:30 [Information] Detected mathematical expression in $var: 'MathBaseline * $var:MaxRegressionPercent / 100'
2025-06-30T09:17:10.8003295+05:30 [Information] Evaluating mathematical expression: 'MathBaseline * $var:MaxRegressionPercent / 100'
2025-06-30T09:17:10.8006175+05:30 [Information] Resolving variables in expression: 'MathBaseline * $var:MaxRegressionPercent / 100'
2025-06-30T09:17:10.8010697+05:30 [Information] Resolved variable 'MaxRegressionPercent' from global variables: 150
2025-06-30T09:17:10.8013088+05:30 [Information] Resolved variable 'MathBaseline' from global variables: 100
2025-06-30T09:17:10.8014077+05:30 [Information] Expression after variable resolution: '100 * 150 / 100'
2025-06-30T09:17:10.8014698+05:30 [Information] Expression after variable resolution: '100 * 150 / 100'
2025-06-30T09:17:10.8043549+05:30 [Information] Mathematical expression 'MathBaseline * $var:MaxRegressionPercent / 100' evaluated to: 150
2025-06-30T09:17:10.8044757+05:30 [Information] Mathematical expression result: '150'
2025-06-30T09:17:10.8045588+05:30 [Information] Resolved JPath '$var:MathBaseline * $var:MaxRegressionPercent / 100' to '150'
2025-06-30T09:17:10.8046088+05:30 [Information] Resolved template pattern '{{$var:MathBaseline * $var:MaxRegressionPercent / 100}}' to '150' in '{{$var:MathBaseline * $var:MaxRegressionPercent / 100}}'
2025-06-30T09:17:10.8048543+05:30 [Information] Resolved template '{{$var:MathBaseline * $var:MaxRegressionPercent / 100}}' to '150'
2025-06-30T09:17:10.8049473+05:30 [Information] Resolving path 'performance.executionTimeMs' using $curStep:
2025-06-30T09:17:10.8051744+05:30 [Information] Resolved JPath '$curStep:performance.executionTimeMs' to '0'
2025-06-30T09:17:10.8053273+05:30 [Error] Asserter error : {"Expression1":{"Value":"{{$var:MathBaseline * $var:MaxRegressionPercent / 100}}","ErrorMesage":"Performance regression detected: {{$curStep:performance.executionTimeMs}}ms vs allowed {{$var:MathBaseline * $var:MaxRegressionPercent / 100}}ms ({{$var:MathBaseline}}ms * {{$var:MaxRegressionPercent}}% / 100)"},"Expression2":{"JPath":"$curStep:performance.executionTimeMs","ErrorMesage":"Performance regression detected: {{$curStep:performance.executionTimeMs}}ms vs allowed {{$var:MathBaseline * $var:MaxRegressionPercent / 100}}ms ({{$var:MathBaseline}}ms * {{$var:MaxRegressionPercent}}% / 100)"},"ErrorMesage":"Performance regression detected: {{$curStep:performance.executionTimeMs}}ms vs allowed {{$var:MathBaseline * $var:MaxRegressionPercent / 100}}ms ({{$var:MathBaseline}}ms * {{$var:MaxRegressionPercent}}% / 100)"}
2025-06-30T09:17:10.8059764+05:30 [Error] Assert Failure : Less(150, 0)
2025-06-30T09:17:10.8062030+05:30 [Information] Failed to execute step 'Regression Threshold Test': Performance regression detected: {{$curStep:performance.executionTimeMs}}ms vs allowed {{$var:MathBaseline * $var:MaxRegressionPercent / 100}}ms ({{$var:MathBaseline}}ms * {{$var:MaxRegressionPercent}}% / 100)
2025-06-30T09:17:10.8062801+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:17:10.8063539+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:17:10.8064002+05:30 [Information] No performance data found for step 'Regression Threshold Test' (cmd)
2025-06-30T09:17:10.8066311+05:30 [Error] Performance regression detected: {{$curStep:performance.executionTimeMs}}ms vs allowed {{$var:MathBaseline * $var:MaxRegressionPercent / 100}}ms ({{$var:MathBaseline}}ms * {{$var:MaxRegressionPercent}}% / 100)
2025-06-30T09:17:10.8068350+05:30 [Error] [ FAILED   ]
2025-06-30T09:17:10.8072244+05:30 [Information] [----------] 6 steps from Simple Mathematical Expressions Demo (1236 ms total)
