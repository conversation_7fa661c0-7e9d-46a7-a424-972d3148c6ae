2025-06-30T09:22:32.1904291+05:30 [Information] [----------] 1 step from Startup
2025-06-30T09:22:32.1929169+05:30 [Information] Set Value to Step '17d7f36a-f238-4afd-ba63-01783cd7f866' name:
"Initialize Database"
2025-06-30T09:22:32.1932039+05:30 [Information] Set Value to Step '17d7f36a-f238-4afd-ba63-01783cd7f866' description:
"Setup initial database state"
2025-06-30T09:22:32.1934904+05:30 [Information] [ RUN      ] Startup > Initialize Database
2025-06-30T09:22:32.1947308+05:30 [Information] Executing single instance of step 'Initialize Database'.
2025-06-30T09:22:32.1958939+05:30 [Information] Executing step 'Initialize Database' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "17d7f36a-f238-4afd-ba63-01783cd7f866",
        "tables": {},
        "variables": {},
        "name": "Initialize Database",
        "description": "Setup initial database state"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:22:32.1975590+05:30 [Information] Processing $var: path 'baseUrl'
2025-06-30T09:22:32.1990010+05:30 [Information] Checking if 'baseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:22:32.2005984+05:30 [Information] Resolving path 'baseUrl' using global variables.
2025-06-30T09:22:32.2008548+05:30 [Information] Resolved JPath '$var:baseUrl' to 'https://httpbin.org'
2025-06-30T09:22:32.2009520+05:30 [Information] Resolved template pattern '{{$var:baseUrl}}' to 'https://httpbin.org' in '{{$var:baseUrl}}/post'
2025-06-30T09:22:32.2012238+05:30 [Information] Resolved template '{{$var:baseUrl}}/post' to 'https://httpbin.org/post'
2025-06-30T09:22:32.2033815+05:30 [Information] Set Value to Step '17d7f36a-f238-4afd-ba63-01783cd7f866' input:
{
  "Method": "POST",
  "RequestUri": "https://httpbin.org/post",
  "Headers": {
    "Content-Type": "application/json"
  },
  "Content": {
    "action": "initialize_db"
  }
}
2025-06-30T09:22:33.3148384+05:30 [Information] Set Value to Step '17d7f36a-f238-4afd-ba63-01783cd7f866' output:
{
  "statusCode": 200,
  "reasonPhrase": "OK",
  "headers": [
    {
      "Key": "Date",
      "Value": [
        "Mon, 30 Jun 2025 03:52:38 GMT"
      ]
    },
    {
      "Key": "Connection",
      "Value": [
        "keep-alive"
      ]
    },
    {
      "Key": "Server",
      "Value": [
        "gunicorn/19.9.0"
      ]
    },
    {
      "Key": "Access-Control-Allow-Origin",
      "Value": [
        "*"
      ]
    },
    {
      "Key": "Access-Control-Allow-Credentials",
      "Value": [
        "true"
      ]
    }
  ],
  "content": {
    "args": {},
    "data": "{\r\n  \"action\": \"initialize_db\"\r\n}",
    "files": {},
    "form": {},
    "headers": {
      "Content-Length": "33",
      "Content-Type": "application/json; charset=utf-8",
      "Host": "httpbin.org",
      "X-Amzn-Trace-Id": "Root=1-68620a06-76d5c21a329bb95d63a3d310"
    },
    "json": {
      "action": "initialize_db"
    },
    "origin": "**************",
    "url": "https://httpbin.org/post"
  }
}
2025-06-30T09:22:33.3153933+05:30 [Information] Resolving path 'output.content.json.action' using step input/output.
2025-06-30T09:22:33.3155988+05:30 [Information] Resolved JPath 'output.content.json.action' to 'initialize_db'
2025-06-30T09:22:33.3165593+05:30 [Information] Set Value to Step '17d7f36a-f238-4afd-ba63-01783cd7f866' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T03:52:32.194702Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:22:33.3190289+05:30 [Information] Run Asserter : {"Expression1":{"JPath":"$curStep:output.content.json.action","ErrorMesage":null},"Expression2":{"Value":null,"ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:22:33.3193596+05:30 [Information] Resolving path 'output.content.json.action' using $curStep:
2025-06-30T09:22:33.3194798+05:30 [Information] Resolved JPath '$curStep:output.content.json.action' to 'initialize_db'
2025-06-30T09:22:33.3207426+05:30 [Information] Error in EqualExpression: The method or operation is not implemented.
2025-06-30T09:22:33.3216501+05:30 [Information] Set Value to Step '17d7f36a-f238-4afd-ba63-01783cd7f866' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 1117,
  "startTime": "2025-06-30T03:52:32.194702Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:22:33.3218206+05:30 [Information] Step 'Initialize Database' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "17d7f36a-f238-4afd-ba63-01783cd7f866",
        "tables": {},
        "variables": {},
        "name": "Initialize Database",
        "description": "Setup initial database state",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "action": "initialize_db"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 03:52:38 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"action\": \"initialize_db\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "33",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620a06-76d5c21a329bb95d63a3d310"
            },
            "json": {
              "action": "initialize_db"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 1117,
          "startTime": "2025-06-30T03:52:32.194702Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:22:33.3220097+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:22:33.3221059+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:22:33.3221633+05:30 [Information] No performance data found for step 'Initialize Database' (http)
2025-06-30T09:22:33.3224579+05:30 [Information] [ OK       ]
2025-06-30T09:22:33.3237747+05:30 [Information] [----------] 1 step from Startup (1131 ms total)
