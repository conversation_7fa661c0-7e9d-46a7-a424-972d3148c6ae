2025-06-30T09:38:12.6851334+05:30 [Information] [----------] 1 step from Long Output Command
2025-06-30T09:38:12.6857651+05:30 [Information] Set Value to Step '70a85495-c0b7-4975-804f-ecb0f011b589' name:
"Step long output"
2025-06-30T09:38:12.6859085+05:30 [Information] Set Value to Step '70a85495-c0b7-4975-804f-ecb0f011b589' description:
"Generate a list of numbers using a loop"
2025-06-30T09:38:12.6859818+05:30 [Information] [ RUN      ] Long Output Command > Step long output
2025-06-30T09:38:12.6862361+05:30 [Information] Executing single instance of step 'Step long output'.
2025-06-30T09:38:12.6863185+05:30 [Information] Executing step 'Step long output' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "70a85495-c0b7-4975-804f-ecb0f011b589",
        "tables": {},
        "variables": {},
        "name": "Step long output",
        "description": "Generate a list of numbers using a loop"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:38:12.6865558+05:30 [Information] Set Value to Step '70a85495-c0b7-4975-804f-ecb0f011b589' input:
{
  "command": "cmd.exe",
  "arguments": [
    "/c",
    "for /L %i in (1,1,10) do @echo Line %i"
  ],
  "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
  "printOutput": false
}
2025-06-30T09:38:12.7180700+05:30 [Information] Set Value to Step '70a85495-c0b7-4975-804f-ecb0f011b589' output:
{
  "stdout": "Line 1\r\nLine 2\r\nLine 3\r\nLine 4\r\nLine 5\r\nLine 6\r\nLine 7\r\nLine 8\r\nLine 9\r\nLine 10",
  "stderr": "",
  "exitCode": 0
}
2025-06-30T09:38:12.7182764+05:30 [Information] Set Value to Step '70a85495-c0b7-4975-804f-ecb0f011b589' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:08:12.6862341Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "cmd"
}
2025-06-30T09:38:12.7183857+05:30 [Information] Run Asserter : {"Expression1":{"JPath":"$curStep:$.output.stdout","ErrorMesage":null},"Expression2":{"Value":"Line 10","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:38:12.7184721+05:30 [Information] Resolving path '$.output.stdout' using $curStep:
2025-06-30T09:38:12.7185345+05:30 [Information] Resolved JPath '$curStep:$.output.stdout' to 'Line 1
Line 2
Line 3
Line 4
Line 5
Line 6
Line 7
Line 8
Line 9
Line 10'
2025-06-30T09:38:12.7186741+05:30 [Information] Set Value to Step '70a85495-c0b7-4975-804f-ecb0f011b589' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 31,
  "startTime": "2025-06-30T04:08:12.6862341Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "cmd"
}
2025-06-30T09:38:12.7188521+05:30 [Information] Step 'Step long output' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "70a85495-c0b7-4975-804f-ecb0f011b589",
        "tables": {},
        "variables": {},
        "name": "Step long output",
        "description": "Generate a list of numbers using a loop",
        "input": {
          "command": "cmd.exe",
          "arguments": [
            "/c",
            "for /L %i in (1,1,10) do @echo Line %i"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Line 1\r\nLine 2\r\nLine 3\r\nLine 4\r\nLine 5\r\nLine 6\r\nLine 7\r\nLine 8\r\nLine 9\r\nLine 10",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 31,
          "startTime": "2025-06-30T04:08:12.6862341Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:38:12.7190146+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:38:12.7191229+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:38:12.7191833+05:30 [Information] No performance data found for step 'Step long output' (cmd)
2025-06-30T09:38:12.7192304+05:30 [Information] [ OK       ]
2025-06-30T09:38:12.7195603+05:30 [Information] [----------] 1 step from Long Output Command (33 ms total)
