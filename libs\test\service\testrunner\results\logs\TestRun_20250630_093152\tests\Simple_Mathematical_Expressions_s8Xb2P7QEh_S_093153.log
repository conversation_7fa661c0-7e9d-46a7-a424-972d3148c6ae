2025-06-30T09:31:53.9955532+05:30 [Information] [----------] 6 steps from Simple Mathematical Expressions Demo - Passing Tests
2025-06-30T09:31:53.9974030+05:30 [Information] Set Value to Step '0f7a7816-cd77-43fa-a30f-cc54877caa45' name:
"Fast Baseline Operation"
2025-06-30T09:31:53.9976206+05:30 [Information] [ RUN      ] Simple Mathematical Expressions Demo - Passing Tests > Fast Baseline Operation
2025-06-30T09:31:53.9981499+05:30 [Information] Executing single instance of step 'Fast Baseline Operation'.
2025-06-30T09:31:53.9983280+05:30 [Information] Executing step 'Fast Baseline Operation' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "0f7a7816-cd77-43fa-a30f-cc54877caa45",
        "tables": {},
        "variables": {},
        "name": "Fast Baseline Operation"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaselineThreshold": 100,
      "MaxRegressionPercent": 1000,
      "BufferTime": 500,
      "FastThreshold": 10000,
      "SlowThreshold": 20000,
      "MathBaseline": 1
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:31:53.9994497+05:30 [Information] Set Value to Step '0f7a7816-cd77-43fa-a30f-cc54877caa45' input:
{
  "command": "echo",
  "arguments": [
    "Establishing baseline performance"
  ],
  "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
  "printOutput": false
}
2025-06-30T09:31:53.9996097+05:30 [Information] Delaying step execution for 50ms
2025-06-30T09:31:54.0811519+05:30 [Information] Set Value to Step '0f7a7816-cd77-43fa-a30f-cc54877caa45' output:
{
  "stdout": "Establishing baseline performance",
  "stderr": "",
  "exitCode": 0
}
2025-06-30T09:31:54.0814370+05:30 [Information] Resolving path 'performance.executionTimeMs' using global context.
2025-06-30T09:31:54.0815728+05:30 [Information] Resolved JPath 'performance.executionTimeMs' to ''
2025-06-30T09:31:54.0816752+05:30 [Information] Set Value to Step '0f7a7816-cd77-43fa-a30f-cc54877caa45' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:01:53.998146Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "cmd"
}
2025-06-30T09:31:54.0825296+05:30 [Information] Run Asserter : {"Expression1":{"Value":"0","ErrorMesage":"Echo command failed"},"Expression2":{"JPath":"$curStep:$.output.exitCode","ErrorMesage":"Echo command failed"},"ErrorMesage":"Echo command failed"}
2025-06-30T09:31:54.0827144+05:30 [Information] Resolving path '$.output.exitCode' using $curStep:
2025-06-30T09:31:54.0828238+05:30 [Information] Resolved JPath '$curStep:$.output.exitCode' to '0'
2025-06-30T09:31:54.0840336+05:30 [Information] Run Asserter : {"_lessExpression":{"Expression1":{"Value":"0","ErrorMesage":"Performance data not available"},"Expression2":{"JPath":"$curStep:performance.executionTimeMs","ErrorMesage":"Performance data not available"},"ErrorMesage":null},"Expression1":{"Value":"0","ErrorMesage":"Performance data not available"},"Expression2":{"JPath":"$curStep:performance.executionTimeMs","ErrorMesage":"Performance data not available"},"ErrorMesage":"Performance data not available"}
2025-06-30T09:31:54.0842029+05:30 [Information] Resolving path 'performance.executionTimeMs' using $curStep:
2025-06-30T09:31:54.0843376+05:30 [Information] Resolved JPath '$curStep:performance.executionTimeMs' to '0'
2025-06-30T09:31:54.0847219+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$var:FastThreshold}}","ErrorMesage":"Baseline too slow: {{$curStep:performance.executionTimeMs}}ms > {{$var:FastThreshold}}ms"},"Expression2":{"JPath":"$curStep:performance.executionTimeMs","ErrorMesage":"Baseline too slow: {{$curStep:performance.executionTimeMs}}ms > {{$var:FastThreshold}}ms"},"ErrorMesage":"Baseline too slow: {{$curStep:performance.executionTimeMs}}ms > {{$var:FastThreshold}}ms"}
2025-06-30T09:31:54.0848676+05:30 [Information] Processing $var: path 'FastThreshold'
2025-06-30T09:31:54.0849743+05:30 [Information] Checking if 'FastThreshold' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:31:54.0850473+05:30 [Information] Resolving path 'FastThreshold' using global variables.
2025-06-30T09:31:54.0851067+05:30 [Information] Resolved JPath '$var:FastThreshold' to '10000'
2025-06-30T09:31:54.0851576+05:30 [Information] Resolved template pattern '{{$var:FastThreshold}}' to '10000' in '{{$var:FastThreshold}}'
2025-06-30T09:31:54.0852281+05:30 [Information] Resolved template '{{$var:FastThreshold}}' to '10000'
2025-06-30T09:31:54.0852839+05:30 [Information] Resolving path 'performance.executionTimeMs' using $curStep:
2025-06-30T09:31:54.0853579+05:30 [Information] Resolved JPath '$curStep:performance.executionTimeMs' to '0'
2025-06-30T09:31:54.0854489+05:30 [Error] Asserter error : {"Expression1":{"Value":"{{$var:FastThreshold}}","ErrorMesage":"Baseline too slow: {{$curStep:performance.executionTimeMs}}ms > {{$var:FastThreshold}}ms"},"Expression2":{"JPath":"$curStep:performance.executionTimeMs","ErrorMesage":"Baseline too slow: {{$curStep:performance.executionTimeMs}}ms > {{$var:FastThreshold}}ms"},"ErrorMesage":"Baseline too slow: {{$curStep:performance.executionTimeMs}}ms > {{$var:FastThreshold}}ms"}
2025-06-30T09:31:54.0860868+05:30 [Error] Assert Failure : Less(10000, 0)
2025-06-30T09:31:54.0863817+05:30 [Information] Failed to execute step 'Fast Baseline Operation': Baseline too slow: {{$curStep:performance.executionTimeMs}}ms > {{$var:FastThreshold}}ms
2025-06-30T09:31:54.0864882+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:31:54.0865634+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:31:54.0866158+05:30 [Information] No performance data found for step 'Fast Baseline Operation' (cmd)
2025-06-30T09:31:54.0868058+05:30 [Error] Baseline too slow: {{$curStep:performance.executionTimeMs}}ms > {{$var:FastThreshold}}ms
2025-06-30T09:31:54.0870024+05:30 [Error] [ FAILED   ]
2025-06-30T09:31:54.0874306+05:30 [Information] [----------] 6 steps from Simple Mathematical Expressions Demo - Passing Tests (91 ms total)
