Name: "Simple Mathematical Expressions Demo - Passing Tests"
Description: "Demonstrates mathematical expression functionality with guaranteed passing tests"

Variables:
  BaselineThreshold: "100"
  MaxRegressionPercent: "1000"  # 1000% = very generous to ensure passing
  BufferTime: "500"             # Large buffer to ensure passing
  FastThreshold: "10000"        # 10 seconds - very generous
  SlowThreshold: "20000"        # 20 seconds - very generous
  MathBaseline: "1"             # Use 1ms as baseline - very small threshold

Steps:
  # Step 1: Fast operation to establish baseline
  - Name: "Fast Baseline Operation"
    Type: "cmd"
    Input:
      Command: "echo"
      Arguments:
        - "Establishing baseline performance"
    Execution:
      DelayMs: 50
    Asserters:
      # Should complete successfully
      - AssertEq:
          ConstExpr: 0
          JPathExpr: "$curStep:$.output.exitCode"
          ErrorMessage: "Echo command failed"

      # Validate performance data is available (can be 0 for very fast operations)
      - AssertGte:
          ConstExpr: 0
          JPathExpr: "$curStep:performance.executionTimeMs"
          ErrorMessage: "Performance data not available"

      # Demonstrate mathematical expression in assertion (should always pass)
      # Formula: FastThreshold = 10000ms (very generous) - validate execution time is reasonable
      - AssertGte:
          ConstExpr: 0
          JPathExpr: "$curStep:performance.executionTimeMs"
          ErrorMessage: "Performance timing should be non-negative"

      # Show mathematical expression evaluation - this should pass since 0ms < 10000ms
      - AssertLt:
          JPathExpr: "$curStep:performance.executionTimeMs"
          ConstExpr: "{{$var:FastThreshold}}"
          ErrorMessage: "Baseline too slow: {{$curStep:performance.executionTimeMs}}ms > {{$var:FastThreshold}}ms (calculated threshold)"
    Output:
      Store:
        # Store actual execution time (even if 0) for mathematical expressions
        actualBaseline: "performance.executionTimeMs"

  # Step 2: Test mathematical expression for regression threshold
  - Name: "Regression Threshold Test"
    Type: "cmd"
    Input:
      Command: "echo"
      Arguments:
        - "Testing mathematical expression: MathBaseline * MaxRegressionPercent / 100"
    Execution:
      DelayMs: 200  # Add more delay to ensure measurable execution time
    Asserters:
      # Should complete successfully
      - AssertEq:
          ConstExpr: 0
          JPathExpr: "$curStep:$.output.exitCode"
          ErrorMessage: "Echo command failed"

      # Demonstrate mathematical expression: baseline * regression percentage
      # Formula: MathBaseline * MaxRegressionPercent / 100 = 1 * 1000 / 100 = 10ms (very generous)
      - AssertLt:
          JPathExpr: "$curStep:performance.executionTimeMs"
          ConstExpr: "{{$var:MathBaseline * $var:MaxRegressionPercent / 100}}"
          ErrorMessage: "Performance regression detected: {{$curStep:performance.executionTimeMs}}ms vs allowed {{$var:MathBaseline * $var:MaxRegressionPercent / 100}}ms ({{$var:MathBaseline}}ms * {{$var:MaxRegressionPercent}}% / 100)"

  # Step 3: Complex mathematical expression with buffer
  - Name: "Complex Expression Test"
    Type: "cmd"
    Input:
      Command: "echo"
      Arguments:
        - "Testing complex expression: MathBaseline * MaxRegressionPercent / 100 + BufferTime"
    Execution:
      DelayMs: 150
    Asserters:
      # Should complete successfully
      - AssertEq:
          ConstExpr: 0
          JPathExpr: "$curStep:$.output.exitCode"
          ErrorMessage: "Echo command failed"

      # Complex expression: baseline * regression% + buffer
      # Formula: MathBaseline * MaxRegressionPercent / 100 + BufferTime = 1 * 1000 / 100 + 500 = 510ms
      - AssertLt:
          JPathExpr: "$curStep:performance.executionTimeMs"
          ConstExpr: "{{$var:MathBaseline * $var:MaxRegressionPercent / 100 + $var:BufferTime}}"
          ErrorMessage: "Complex threshold exceeded: {{$curStep:performance.executionTimeMs}}ms vs {{$var:MathBaseline * $var:MaxRegressionPercent / 100 + $var:BufferTime}}ms ({{$var:MathBaseline}} * {{$var:MaxRegressionPercent}}% / 100 + {{$var:BufferTime}})"

  # Step 4: Demonstrate parentheses and operator precedence
  - Name: "Operator Precedence Test"
    Type: "cmd"
    Input:
      Command: "echo"
      Arguments:
        - "Testing operator precedence"
    Execution:
      DelayMs: 75
    Asserters:
      # Should complete successfully
      - AssertEq:
          ConstExpr: 0
          JPathExpr: "$curStep:$.output.exitCode"
          ErrorMessage: "Echo command failed"
      
      # Expression with parentheses: (baseline + buffer) * 2
      # Formula: (MathBaseline + BufferTime) * 2 = (1 + 500) * 2 = 1002ms
      - AssertLt:
          JPathExpr: "$curStep:performance.executionTimeMs"
          ConstExpr: "{{$var:(MathBaseline + $var:BufferTime) * 2}}"
          ErrorMessage: "Parentheses test failed: {{$curStep:performance.executionTimeMs}}ms vs {{$var:(MathBaseline + $var:BufferTime) * 2}}ms = ({{$var:MathBaseline}} + {{$var:BufferTime}}) * 2"

      # Expression without parentheses: baseline + buffer * 2 (different result due to operator precedence)
      # Formula: MathBaseline + BufferTime * 2 = 1 + 500 * 2 = 1001ms
      - AssertLt:
          JPathExpr: "$curStep:performance.executionTimeMs"
          ConstExpr: "{{$var:MathBaseline + $var:BufferTime * 2}}"
          ErrorMessage: "Precedence test failed: {{$curStep:performance.executionTimeMs}}ms vs {{$var:MathBaseline + $var:BufferTime * 2}}ms = {{$var:MathBaseline}} + {{$var:BufferTime}} * 2"

  # Step 5: Performance budgeting scenario
  - Name: "Performance Budget Test"
    Variables:
      # Local variables for this step
      CriticalPath: "5000"  # 5000ms (very generous)
      OptimizationFactor: "0.8"  # 20% improvement expected
    Type: "cmd"
    Input:
      Command: "echo"
      Arguments:
        - "Testing performance budget: CriticalPath * OptimizationFactor"
    Execution:
      DelayMs: 200
    Asserters:
      # Should complete successfully
      - AssertEq:
          ConstExpr: 0
          JPathExpr: "$curStep:$.output.exitCode"
          ErrorMessage: "Echo command failed"

      # Budget calculation: critical path * optimization factor
      # Formula: CriticalPath * OptimizationFactor = 5000 * 0.8 = 4000ms
      - AssertLt:
          JPathExpr: "$curStep:performance.executionTimeMs"
          ConstExpr: "{{$var:CriticalPath * $var:OptimizationFactor}}"
          ErrorMessage: "Performance budget exceeded: {{$curStep:performance.executionTimeMs}}ms vs budget {{$var:CriticalPath * $var:OptimizationFactor}}ms"

      # Fallback to absolute limit if optimization not achieved
      # Formula: CriticalPath = 5000ms
      - AssertLt:
          JPathExpr: "$curStep:performance.executionTimeMs"
          ConstExpr: "{{$var:CriticalPath}}"
          ErrorMessage: "Absolute performance limit exceeded: {{$curStep:performance.executionTimeMs}}ms vs {{$var:CriticalPath}}ms"

  # Step 6: Demonstrate mathematical expressions in error messages
  - Name: "Error Message Demo"
    Type: "cmd"
    Input:
      Command: "echo"
      Arguments:
        - "Mathematical expressions work in error messages too!"
    Execution:
      DelayMs: 50
    Asserters:
      # Should complete successfully
      - AssertEq:
          ConstExpr: 0
          JPathExpr: "$curStep:$.output.exitCode"
          ErrorMessage: "Echo command failed"
      
      # This should work - valid expression demonstrating mathematical expressions in error messages
      # Formula: MathBaseline * 3 = 1 * 3 = 3ms
      - AssertLt:
          JPathExpr: "$curStep:performance.executionTimeMs"
          ConstExpr: "{{$var:MathBaseline * 3}}"
          ErrorMessage: "Performance test failed: {{$curStep:performance.executionTimeMs}}ms exceeded {{$var:MathBaseline * 3}}ms (3x baseline of {{$var:MathBaseline}}ms)"
