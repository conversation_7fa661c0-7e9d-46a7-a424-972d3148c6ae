2025-06-30T09:01:36.7253682+05:30 [Information] [----------] 6 steps from Simple Mathematical Expressions Demo
2025-06-30T09:01:36.7266511+05:30 [Information] Set Value to Step 'e3f6d0ed-fbdf-4fb0-89e1-22006da17dd4' name:
"Fast Baseline Operation"
2025-06-30T09:01:36.7268100+05:30 [Information] [ RUN      ] Simple Mathematical Expressions Demo > Fast Baseline Operation
2025-06-30T09:01:36.7271973+05:30 [Information] Executing single instance of step 'Fast Baseline Operation'.
2025-06-30T09:01:36.7273481+05:30 [Information] Executing step 'Fast Baseline Operation' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "e3f6d0ed-fbdf-4fb0-89e1-22006da17dd4",
        "tables": {},
        "variables": {},
        "name": "Fast Baseline Operation"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaselineThreshold": 100,
      "MaxRegressionPercent": 150,
      "BufferTime": 50,
      "FastThreshold": 5000,
      "SlowThreshold": 10000
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:01:36.7283879+05:30 [Information] Set Value to Step 'e3f6d0ed-fbdf-4fb0-89e1-22006da17dd4' input:
{
  "command": "ping",
  "arguments": [
    "127.0.0.1",
    "-n",
    "1"
  ],
  "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
  "printOutput": false
}
2025-06-30T09:01:36.7285402+05:30 [Information] Delaying step execution for 50ms
2025-06-30T09:01:36.8194813+05:30 [Information] Set Value to Step 'e3f6d0ed-fbdf-4fb0-89e1-22006da17dd4' output:
{
  "stdout": "Pinging 127.0.0.1 with 32 bytes of data:\r\nReply from 127.0.0.1: bytes=32 time<1ms TTL=128\r\nPing statistics for 127.0.0.1:\r\n    Packets: Sent = 1, Received = 1, Lost = 0 (0% loss),\r\nApproximate round trip times in milli-seconds:\r\n    Minimum = 0ms, Maximum = 0ms, Average = 0ms",
  "stderr": "",
  "exitCode": 0
}
2025-06-30T09:01:36.8197394+05:30 [Information] Resolving path 'performance.executionTimeMs' using global context.
2025-06-30T09:01:36.8198593+05:30 [Information] Resolved JPath 'performance.executionTimeMs' to ''
2025-06-30T09:01:36.8199776+05:30 [Information] Set Value to Step 'e3f6d0ed-fbdf-4fb0-89e1-22006da17dd4' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T03:31:36.7271947Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "cmd"
}
2025-06-30T09:01:36.8208199+05:30 [Information] Run Asserter : {"Expression1":{"Value":"0","ErrorMesage":"Ping command failed"},"Expression2":{"JPath":"$curStep:$.output.exitCode","ErrorMesage":"Ping command failed"},"ErrorMesage":"Ping command failed"}
2025-06-30T09:01:36.8209944+05:30 [Information] Resolving path '$.output.exitCode' using $curStep:
2025-06-30T09:01:36.8210979+05:30 [Information] Resolved JPath '$curStep:$.output.exitCode' to '0'
2025-06-30T09:01:36.8225038+05:30 [Information] Run Asserter : {"_lessExpression":{"Expression1":{"Value":"0","ErrorMesage":"Performance data not available"},"Expression2":{"JPath":"$curStep:performance.executionTimeMs","ErrorMesage":"Performance data not available"},"ErrorMesage":null},"Expression1":{"Value":"0","ErrorMesage":"Performance data not available"},"Expression2":{"JPath":"$curStep:performance.executionTimeMs","ErrorMesage":"Performance data not available"},"ErrorMesage":"Performance data not available"}
2025-06-30T09:01:36.8227324+05:30 [Information] Resolving path 'performance.executionTimeMs' using $curStep:
2025-06-30T09:01:36.8228953+05:30 [Information] Resolved JPath '$curStep:performance.executionTimeMs' to '0'
2025-06-30T09:01:36.8233062+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$var:FastThreshold}}","ErrorMesage":"Baseline too slow: {{$curStep:performance.executionTimeMs}}ms > {{$var:FastThreshold}}ms"},"Expression2":{"JPath":"$curStep:performance.executionTimeMs","ErrorMesage":"Baseline too slow: {{$curStep:performance.executionTimeMs}}ms > {{$var:FastThreshold}}ms"},"ErrorMesage":"Baseline too slow: {{$curStep:performance.executionTimeMs}}ms > {{$var:FastThreshold}}ms"}
2025-06-30T09:01:36.8234824+05:30 [Information] Processing $var: path 'FastThreshold'
2025-06-30T09:01:36.8235892+05:30 [Information] Checking if 'FastThreshold' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:01:36.8236985+05:30 [Information] Resolving path 'FastThreshold' using global variables.
2025-06-30T09:01:36.8237937+05:30 [Information] Resolved JPath '$var:FastThreshold' to '5000'
2025-06-30T09:01:36.8238533+05:30 [Information] Resolved template pattern '{{$var:FastThreshold}}' to '5000' in '{{$var:FastThreshold}}'
2025-06-30T09:01:36.8239238+05:30 [Information] Resolved template '{{$var:FastThreshold}}' to '5000'
2025-06-30T09:01:36.8239806+05:30 [Information] Resolving path 'performance.executionTimeMs' using $curStep:
2025-06-30T09:01:36.8240416+05:30 [Information] Resolved JPath '$curStep:performance.executionTimeMs' to '0'
2025-06-30T09:01:36.8242093+05:30 [Error] Asserter error : {"Expression1":{"Value":"{{$var:FastThreshold}}","ErrorMesage":"Baseline too slow: {{$curStep:performance.executionTimeMs}}ms > {{$var:FastThreshold}}ms"},"Expression2":{"JPath":"$curStep:performance.executionTimeMs","ErrorMesage":"Baseline too slow: {{$curStep:performance.executionTimeMs}}ms > {{$var:FastThreshold}}ms"},"ErrorMesage":"Baseline too slow: {{$curStep:performance.executionTimeMs}}ms > {{$var:FastThreshold}}ms"}
2025-06-30T09:01:36.8248830+05:30 [Error] Assert Failure : Less(5000, 0)
2025-06-30T09:01:36.8251911+05:30 [Information] Failed to execute step 'Fast Baseline Operation': Baseline too slow: {{$curStep:performance.executionTimeMs}}ms > {{$var:FastThreshold}}ms
2025-06-30T09:01:36.8252957+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:01:36.8253749+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:01:36.8254319+05:30 [Information] No performance data found for step 'Fast Baseline Operation' (cmd)
2025-06-30T09:01:36.8256343+05:30 [Error] Baseline too slow: {{$curStep:performance.executionTimeMs}}ms > {{$var:FastThreshold}}ms
2025-06-30T09:01:36.8258322+05:30 [Error] [ FAILED   ]
2025-06-30T09:01:36.8262496+05:30 [Information] [----------] 6 steps from Simple Mathematical Expressions Demo (100 ms total)
