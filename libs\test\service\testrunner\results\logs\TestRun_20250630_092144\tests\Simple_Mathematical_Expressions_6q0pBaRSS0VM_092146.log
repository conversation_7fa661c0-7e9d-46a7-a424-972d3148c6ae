2025-06-30T09:21:46.4141077+05:30 [Information] [----------] 6 steps from Simple Mathematical Expressions Demo
2025-06-30T09:21:46.4154805+05:30 [Information] Set Value to Step '33613697-9816-419d-bab1-1342699f5378' name:
"Fast Baseline Operation"
2025-06-30T09:21:46.4156202+05:30 [Information] [ RUN      ] Simple Mathematical Expressions Demo > Fast Baseline Operation
2025-06-30T09:21:46.4159583+05:30 [Information] Executing single instance of step 'Fast Baseline Operation'.
2025-06-30T09:21:46.4161440+05:30 [Information] Executing step 'Fast Baseline Operation' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "33613697-9816-419d-bab1-1342699f5378",
        "tables": {},
        "variables": {},
        "name": "Fast Baseline Operation"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaselineThreshold": 100,
      "MaxRegressionPercent": 150,
      "BufferTime": 50,
      "FastThreshold": 5000,
      "SlowThreshold": 10000,
      "MathBaseline": 100
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:21:46.4171777+05:30 [Information] Set Value to Step '33613697-9816-419d-bab1-1342699f5378' input:
{
  "command": "ping",
  "arguments": [
    "127.0.0.1",
    "-n",
    "1"
  ],
  "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
  "printOutput": false
}
2025-06-30T09:21:46.4173049+05:30 [Information] Delaying step execution for 50ms
2025-06-30T09:21:46.5083341+05:30 [Information] Set Value to Step '33613697-9816-419d-bab1-1342699f5378' output:
{
  "stdout": "Pinging 127.0.0.1 with 32 bytes of data:\r\nReply from 127.0.0.1: bytes=32 time<1ms TTL=128\r\nPing statistics for 127.0.0.1:\r\n    Packets: Sent = 1, Received = 1, Lost = 0 (0% loss),\r\nApproximate round trip times in milli-seconds:\r\n    Minimum = 0ms, Maximum = 0ms, Average = 0ms",
  "stderr": "",
  "exitCode": 0
}
2025-06-30T09:21:46.5087606+05:30 [Information] Resolving path 'performance.executionTimeMs' using global context.
2025-06-30T09:21:46.5089073+05:30 [Information] Resolved JPath 'performance.executionTimeMs' to ''
2025-06-30T09:21:46.5090777+05:30 [Information] Set Value to Step '33613697-9816-419d-bab1-1342699f5378' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T03:51:46.4159555Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "cmd"
}
2025-06-30T09:21:46.5098606+05:30 [Information] Run Asserter : {"Expression1":{"Value":"0","ErrorMesage":"Ping command failed"},"Expression2":{"JPath":"$curStep:$.output.exitCode","ErrorMesage":"Ping command failed"},"ErrorMesage":"Ping command failed"}
2025-06-30T09:21:46.5100134+05:30 [Information] Resolving path '$.output.exitCode' using $curStep:
2025-06-30T09:21:46.5101222+05:30 [Information] Resolved JPath '$curStep:$.output.exitCode' to '0'
2025-06-30T09:21:46.5111535+05:30 [Information] Run Asserter : {"_lessExpression":{"Expression1":{"Value":"0","ErrorMesage":"Performance data not available"},"Expression2":{"JPath":"$curStep:performance.executionTimeMs","ErrorMesage":"Performance data not available"},"ErrorMesage":null},"Expression1":{"Value":"0","ErrorMesage":"Performance data not available"},"Expression2":{"JPath":"$curStep:performance.executionTimeMs","ErrorMesage":"Performance data not available"},"ErrorMesage":"Performance data not available"}
2025-06-30T09:21:46.5113016+05:30 [Information] Resolving path 'performance.executionTimeMs' using $curStep:
2025-06-30T09:21:46.5113951+05:30 [Information] Resolved JPath '$curStep:performance.executionTimeMs' to '0'
2025-06-30T09:21:46.5117743+05:30 [Information] Set Value to Step '33613697-9816-419d-bab1-1342699f5378' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 94,
  "startTime": "2025-06-30T03:51:46.4159555Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "cmd"
}
2025-06-30T09:21:46.5119452+05:30 [Information] Step 'Fast Baseline Operation' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "33613697-9816-419d-bab1-1342699f5378",
        "tables": {},
        "variables": {},
        "name": "Fast Baseline Operation",
        "input": {
          "command": "ping",
          "arguments": [
            "127.0.0.1",
            "-n",
            "1"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Pinging 127.0.0.1 with 32 bytes of data:\r\nReply from 127.0.0.1: bytes=32 time<1ms TTL=128\r\nPing statistics for 127.0.0.1:\r\n    Packets: Sent = 1, Received = 1, Lost = 0 (0% loss),\r\nApproximate round trip times in milli-seconds:\r\n    Minimum = 0ms, Maximum = 0ms, Average = 0ms",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 94,
          "startTime": "2025-06-30T03:51:46.4159555Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaselineThreshold": 100,
      "MaxRegressionPercent": 150,
      "BufferTime": 50,
      "FastThreshold": 5000,
      "SlowThreshold": 10000,
      "MathBaseline": 100
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:21:46.5120280+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:21:46.5120994+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:21:46.5121501+05:30 [Information] No performance data found for step 'Fast Baseline Operation' (cmd)
2025-06-30T09:21:46.5121958+05:30 [Information] [ OK       ]
2025-06-30T09:21:46.5124818+05:30 [Information] Set Value to Step '46300a28-afb4-42b6-9c92-10ec7325c38b' name:
"Regression Threshold Test"
2025-06-30T09:21:46.5125902+05:30 [Information] [ RUN      ] Simple Mathematical Expressions Demo > Regression Threshold Test
2025-06-30T09:21:46.5128624+05:30 [Information] Executing single instance of step 'Regression Threshold Test'.
2025-06-30T09:21:46.5130263+05:30 [Information] Executing step 'Regression Threshold Test' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "33613697-9816-419d-bab1-1342699f5378",
        "tables": {},
        "variables": {},
        "name": "Fast Baseline Operation",
        "input": {
          "command": "ping",
          "arguments": [
            "127.0.0.1",
            "-n",
            "1"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Pinging 127.0.0.1 with 32 bytes of data:\r\nReply from 127.0.0.1: bytes=32 time<1ms TTL=128\r\nPing statistics for 127.0.0.1:\r\n    Packets: Sent = 1, Received = 1, Lost = 0 (0% loss),\r\nApproximate round trip times in milli-seconds:\r\n    Minimum = 0ms, Maximum = 0ms, Average = 0ms",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 94,
          "startTime": "2025-06-30T03:51:46.4159555Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "46300a28-afb4-42b6-9c92-10ec7325c38b",
        "tables": {},
        "variables": {},
        "name": "Regression Threshold Test"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaselineThreshold": 100,
      "MaxRegressionPercent": 150,
      "BufferTime": 50,
      "FastThreshold": 5000,
      "SlowThreshold": 10000,
      "MathBaseline": 100
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:21:46.5131891+05:30 [Information] Set Value to Step '46300a28-afb4-42b6-9c92-10ec7325c38b' input:
{
  "command": "timeout",
  "arguments": [
    "/t",
    "1",
    "/nobreak"
  ],
  "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
  "printOutput": false
}
2025-06-30T09:21:46.5132772+05:30 [Information] Delaying step execution for 200ms
2025-06-30T09:21:47.1844288+05:30 [Information] Set Value to Step '46300a28-afb4-42b6-9c92-10ec7325c38b' output:
{
  "stdout": "Waiting for 1 seconds, press CTRL+C to quit ...\b0",
  "stderr": "",
  "exitCode": 0
}
2025-06-30T09:21:47.1856364+05:30 [Information] Set Value to Step '46300a28-afb4-42b6-9c92-10ec7325c38b' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T03:51:46.5128589Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "cmd"
}
2025-06-30T09:21:47.1858746+05:30 [Information] Run Asserter : {"Expression1":{"Value":"0","ErrorMesage":"Ping command failed"},"Expression2":{"JPath":"$curStep:$.output.exitCode","ErrorMesage":"Ping command failed"},"ErrorMesage":"Ping command failed"}
2025-06-30T09:21:47.1860541+05:30 [Information] Resolving path '$.output.exitCode' using $curStep:
2025-06-30T09:21:47.1861880+05:30 [Information] Resolved JPath '$curStep:$.output.exitCode' to '0'
2025-06-30T09:21:47.1863270+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$var:MathBaseline * $var:MaxRegressionPercent / 100}}","ErrorMesage":"Performance regression detected: {{$curStep:performance.executionTimeMs}}ms vs allowed {{$var:MathBaseline * $var:MaxRegressionPercent / 100}}ms ({{$var:MathBaseline}}ms * {{$var:MaxRegressionPercent}}% / 100)"},"Expression2":{"JPath":"$curStep:performance.executionTimeMs","ErrorMesage":"Performance regression detected: {{$curStep:performance.executionTimeMs}}ms vs allowed {{$var:MathBaseline * $var:MaxRegressionPercent / 100}}ms ({{$var:MathBaseline}}ms * {{$var:MaxRegressionPercent}}% / 100)"},"ErrorMesage":"Performance regression detected: {{$curStep:performance.executionTimeMs}}ms vs allowed {{$var:MathBaseline * $var:MaxRegressionPercent / 100}}ms ({{$var:MathBaseline}}ms * {{$var:MaxRegressionPercent}}% / 100)"}
2025-06-30T09:21:47.1864361+05:30 [Information] Processing $var: path 'MathBaseline * $var:MaxRegressionPercent / 100'
2025-06-30T09:21:47.1865163+05:30 [Information] Checking if 'MathBaseline * $var:MaxRegressionPercent / 100' is mathematical expression: hasArithmetic=True, hasArray=False, hasProperty=False, result=True
2025-06-30T09:21:47.1865704+05:30 [Information] Detected mathematical expression in $var: 'MathBaseline * $var:MaxRegressionPercent / 100'
2025-06-30T09:21:47.1868653+05:30 [Information] Evaluating mathematical expression: 'MathBaseline * $var:MaxRegressionPercent / 100'
2025-06-30T09:21:47.1871174+05:30 [Information] Resolving variables in expression: 'MathBaseline * $var:MaxRegressionPercent / 100'
2025-06-30T09:21:47.1875580+05:30 [Information] Resolved variable 'MaxRegressionPercent' from global variables: 150
2025-06-30T09:21:47.1878093+05:30 [Information] Resolved variable 'MathBaseline' from global variables: 100
2025-06-30T09:21:47.1879061+05:30 [Information] Expression after variable resolution: '100 * 150 / 100'
2025-06-30T09:21:47.1879642+05:30 [Information] Expression after variable resolution: '100 * 150 / 100'
2025-06-30T09:21:47.1909030+05:30 [Information] Mathematical expression 'MathBaseline * $var:MaxRegressionPercent / 100' evaluated to: 150
2025-06-30T09:21:47.1910228+05:30 [Information] Mathematical expression result: '150'
2025-06-30T09:21:47.1911836+05:30 [Information] Resolved JPath '$var:MathBaseline * $var:MaxRegressionPercent / 100' to '150'
2025-06-30T09:21:47.1912651+05:30 [Information] Resolved template pattern '{{$var:MathBaseline * $var:MaxRegressionPercent / 100}}' to '150' in '{{$var:MathBaseline * $var:MaxRegressionPercent / 100}}'
2025-06-30T09:21:47.1916542+05:30 [Information] Resolved template '{{$var:MathBaseline * $var:MaxRegressionPercent / 100}}' to '150'
2025-06-30T09:21:47.1917920+05:30 [Information] Resolving path 'performance.executionTimeMs' using $curStep:
2025-06-30T09:21:47.1919813+05:30 [Information] Resolved JPath '$curStep:performance.executionTimeMs' to '0'
2025-06-30T09:21:47.1921400+05:30 [Error] Asserter error : {"Expression1":{"Value":"{{$var:MathBaseline * $var:MaxRegressionPercent / 100}}","ErrorMesage":"Performance regression detected: {{$curStep:performance.executionTimeMs}}ms vs allowed {{$var:MathBaseline * $var:MaxRegressionPercent / 100}}ms ({{$var:MathBaseline}}ms * {{$var:MaxRegressionPercent}}% / 100)"},"Expression2":{"JPath":"$curStep:performance.executionTimeMs","ErrorMesage":"Performance regression detected: {{$curStep:performance.executionTimeMs}}ms vs allowed {{$var:MathBaseline * $var:MaxRegressionPercent / 100}}ms ({{$var:MathBaseline}}ms * {{$var:MaxRegressionPercent}}% / 100)"},"ErrorMesage":"Performance regression detected: {{$curStep:performance.executionTimeMs}}ms vs allowed {{$var:MathBaseline * $var:MaxRegressionPercent / 100}}ms ({{$var:MathBaseline}}ms * {{$var:MaxRegressionPercent}}% / 100)"}
2025-06-30T09:21:47.1930627+05:30 [Error] Assert Failure : Less(150, 0)
2025-06-30T09:21:47.1933239+05:30 [Information] Failed to execute step 'Regression Threshold Test': Performance regression detected: {{$curStep:performance.executionTimeMs}}ms vs allowed {{$var:MathBaseline * $var:MaxRegressionPercent / 100}}ms ({{$var:MathBaseline}}ms * {{$var:MaxRegressionPercent}}% / 100)
2025-06-30T09:21:47.1934125+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:21:47.1935012+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:21:47.1936029+05:30 [Information] No performance data found for step 'Regression Threshold Test' (cmd)
2025-06-30T09:21:47.1938553+05:30 [Error] Performance regression detected: {{$curStep:performance.executionTimeMs}}ms vs allowed {{$var:MathBaseline * $var:MaxRegressionPercent / 100}}ms ({{$var:MathBaseline}}ms * {{$var:MaxRegressionPercent}}% / 100)
2025-06-30T09:21:47.1940435+05:30 [Error] [ FAILED   ]
2025-06-30T09:21:47.1945632+05:30 [Information] [----------] 6 steps from Simple Mathematical Expressions Demo (779 ms total)
