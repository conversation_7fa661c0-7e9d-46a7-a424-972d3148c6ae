2025-06-30T09:38:24.5770970+05:30 [Information] [----------] 4 steps from ProtoReference test
2025-06-30T09:38:24.5779360+05:30 [Information] Set Value to Step 'afaf165b-e118-425b-8d88-1b05542f7bd5' name:
"Step 1: Connect to the MBusClient with admin credentials"
2025-06-30T09:38:24.5780766+05:30 [Information] Set Value to Step 'afaf165b-e118-425b-8d88-1b05542f7bd5' description:
"Connect to the MBusClient with admin credentials"
2025-06-30T09:38:24.5781514+05:30 [Information] [ RUN      ] ProtoReference test > Step 1: Connect to the MBusClient with admin credentials
2025-06-30T09:38:24.5786017+05:30 [Information] Executing single instance of step 'Step 1: Connect to the MBusClient with admin credentials'.
2025-06-30T09:38:24.5790670+05:30 [Information] Executing step 'Step 1: Connect to the MBusClient with admin credentials' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "afaf165b-e118-425b-8d88-1b05542f7bd5",
        "tables": {},
        "variables": {},
        "name": "Step 1: Connect to the MBusClient with admin credentials",
        "description": "Connect to the MBusClient with admin credentials"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "userId": "surgeon2"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:38:24.5791583+05:30 [Information] Processing $var: path 'serverIp'
2025-06-30T09:38:24.5792202+05:30 [Information] Checking if 'serverIp' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:24.5792737+05:30 [Information] Resolving path 'serverIp' using global variables.
2025-06-30T09:38:24.5793250+05:30 [Information] Resolved JPath '$var:serverIp' to '127.0.0.1'
2025-06-30T09:38:24.5793822+05:30 [Information] Resolved template pattern '{{$var:serverIp}}' to '127.0.0.1' in '{{$var:serverIp}}'
2025-06-30T09:38:24.5794210+05:30 [Information] Resolved template '{{$var:serverIp}}' to '127.0.0.1'
2025-06-30T09:38:24.5794750+05:30 [Information] Processing $var: path 'serverPort'
2025-06-30T09:38:24.5795171+05:30 [Information] Checking if 'serverPort' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:24.5795547+05:30 [Information] Resolving path 'serverPort' using global variables.
2025-06-30T09:38:24.5795962+05:30 [Information] Resolved JPath '$var:serverPort' to '60606'
2025-06-30T09:38:24.5796334+05:30 [Information] Resolved template pattern '{{$var:serverPort}}' to '60606' in '{{$var:serverPort}}'
2025-06-30T09:38:24.5796698+05:30 [Information] Resolved template '{{$var:serverPort}}' to '60606'
2025-06-30T09:38:24.5797238+05:30 [Information] Set Value to Step 'afaf165b-e118-425b-8d88-1b05542f7bd5' input:
{
  "ServerIp": "127.0.0.1",
  "ServerPort": 60606,
  "Username": "admin",
  "Password": "admin"
}
2025-06-30T09:38:26.5871630+05:30 [Information] Failed to execute step 'Step 1: Connect to the MBusClient with admin credentials': Failed to execute step : Cannot connect to 127.0.0.1:60606.
2025-06-30T09:38:26.5878356+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:38:26.5880723+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:38:26.5887485+05:30 [Information] No performance data found for step 'Step 1: Connect to the MBusClient with admin credentials' (mbus)
2025-06-30T09:38:26.5901114+05:30 [Error] Failed to execute step : Cannot connect to 127.0.0.1:60606.
2025-06-30T09:38:26.5910284+05:30 [Error] [ FAILED   ]
2025-06-30T09:38:26.5920118+05:30 [Information] [----------] 4 steps from ProtoReference test (2014 ms total)
