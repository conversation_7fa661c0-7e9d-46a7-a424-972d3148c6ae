2025-06-30T09:38:17.4656670+05:30 [Information] [----------] 2 steps from Itterative Test and Step
2025-06-30T09:38:17.4663068+05:30 [Information] Default-mapped column 'User' to variable 'User' with value '{
  "Username": "<PERSON>",
  "Email": "<EMAIL>"
}'
2025-06-30T09:38:17.4664230+05:30 [Information] Default-mapped column 'OrgName' to variable 'OrgName' with value 'Org1'
2025-06-30T09:38:17.4664903+05:30 [Information] Processing $var: path 'User.Username'
2025-06-30T09:38:17.4665395+05:30 [Information] Checking if 'User.Username' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=True, result=False
2025-06-30T09:38:17.4665865+05:30 [Information] Resolving path 'User.Username' using global variables.
2025-06-30T09:38:17.4667199+05:30 [Information] Resolved JPath '$var:User.Username' to 'Tom'
2025-06-30T09:38:17.4667869+05:30 [Information] Resolved template pattern '{{$var:User.Username}}' to 'Tom' in '{{$var:User.Username}}'
2025-06-30T09:38:17.4668333+05:30 [Information] Resolved template '{{$var:User.Username}}' to 'Tom'
2025-06-30T09:38:17.4669055+05:30 [Information] Mapped column '{{$var:User.Username}}' to variable 'Usernamelocal' with value 'Tom'
2025-06-30T09:38:17.4669560+05:30 [Information] Processing $var: path 'User.Email'
2025-06-30T09:38:17.4669958+05:30 [Information] Checking if 'User.Email' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=True, result=False
2025-06-30T09:38:17.4670369+05:30 [Information] Resolving path 'User.Email' using global variables.
2025-06-30T09:38:17.4670745+05:30 [Information] Resolved JPath '$var:User.Email' to '<EMAIL>'
2025-06-30T09:38:17.4671103+05:30 [Information] Resolved template pattern '{{$var:User.Email}}' to '<EMAIL>' in '{{$var:User.Email}}'
2025-06-30T09:38:17.4671466+05:30 [Information] Resolved template '{{$var:User.Email}}' to '<EMAIL>'
2025-06-30T09:38:17.4672006+05:30 [Information] Mapped column '{{$var:User.Email}}' to variable 'Emaillocal' with value '<EMAIL>'
2025-06-30T09:38:17.4672429+05:30 [Information] Processing $var: path 'Usernamelocal'
2025-06-30T09:38:17.4672819+05:30 [Information] Checking if 'Usernamelocal' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:17.4673191+05:30 [Information] Resolving path 'Usernamelocal' using global variables.
2025-06-30T09:38:17.4673566+05:30 [Information] Resolved JPath '$var:Usernamelocal' to 'Tom'
2025-06-30T09:38:17.4673917+05:30 [Information] Resolved template pattern '{{$var:Usernamelocal}}' to 'Tom' in 'Running Test for {{$var:Usernamelocal}}'
2025-06-30T09:38:17.4674275+05:30 [Information] Resolved template 'Running Test for {{$var:Usernamelocal}}' to 'Running Test for Tom'
2025-06-30T09:38:17.4675894+05:30 [Information] Test Iteration 1/2: Running Test for Tom
2025-06-30T09:38:17.4677223+05:30 [Information] Processing $var: path 'Usernamelocal'
2025-06-30T09:38:17.4677838+05:30 [Information] Checking if 'Usernamelocal' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:17.4678240+05:30 [Information] Resolving path 'Usernamelocal' using global variables.
2025-06-30T09:38:17.4679019+05:30 [Information] Resolved JPath '$var:Usernamelocal' to 'Tom'
2025-06-30T09:38:17.4679406+05:30 [Information] Resolved template pattern '{{$var:Usernamelocal}}' to 'Tom' in 'Step to get token for {{$var:Usernamelocal}}'
2025-06-30T09:38:17.4679789+05:30 [Information] Resolved template 'Step to get token for {{$var:Usernamelocal}}' to 'Step to get token for Tom'
2025-06-30T09:38:17.4680216+05:30 [Information] Processing $var: path 'Usernamelocal'
2025-06-30T09:38:17.4680579+05:30 [Information] Checking if 'Usernamelocal' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:17.4680940+05:30 [Information] Resolving path 'Usernamelocal' using global variables.
2025-06-30T09:38:17.4681301+05:30 [Information] Resolved JPath '$var:Usernamelocal' to 'Tom'
2025-06-30T09:38:17.4681777+05:30 [Information] Resolved template pattern '{{$var:Usernamelocal}}' to 'Tom' in 'Step to get token for {{$var:Usernamelocal}}'
2025-06-30T09:38:17.4682177+05:30 [Information] Resolved template 'Step to get token for {{$var:Usernamelocal}}' to 'Step to get token for Tom'
2025-06-30T09:38:17.4682615+05:30 [Information] Set Value to Step 'd4f4592c-0bf8-4dd4-bffa-5a2abcbfd7c2' name:
"Step to get token for Tom"
2025-06-30T09:38:17.4683006+05:30 [Information] Set Value to Step 'd4f4592c-0bf8-4dd4-bffa-5a2abcbfd7c2' description:
"Step to get token for Tom"
2025-06-30T09:38:17.4683372+05:30 [Information] [ RUN      ] Itterative Test and Step > Step to get token for Tom
2025-06-30T09:38:17.4685443+05:30 [Information] Executing single instance of step 'Step to get token for {{$var:Usernamelocal}}'.
2025-06-30T09:38:17.4686128+05:30 [Information] Executing step 'Step to get token for Tom' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "d4f4592c-0bf8-4dd4-bffa-5a2abcbfd7c2",
        "tables": {},
        "variables": {},
        "name": "Step to get token for Tom",
        "description": "Step to get token for Tom"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaseUrl": "https://httpbin.org",
      "User": {
        "Username": "Tom",
        "Email": "<EMAIL>"
      },
      "OrgName": "Org1",
      "Usernamelocal": "Tom",
      "Emaillocal": "<EMAIL>"
    },
    "tables": {
      "UserTable": [
        {
          "User": {
            "Username": "Tom",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org1"
        },
        {
          "User": {
            "Username": "John",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org2"
        }
      ],
      "UserRolesTable": [
        {
          "Name": "Admin"
        },
        {
          "Name": "User"
        }
      ]
    },
    "context": {
      "repeatForTable": "UserTable",
      "row": {
        "User": {
          "Username": "Tom",
          "Email": "<EMAIL>"
        },
        "OrgName": "Org1"
      },
      "rowIndex": 0
    }
  }
}
2025-06-30T09:38:17.4686685+05:30 [Information] Processing $var: path 'BaseUrl'
2025-06-30T09:38:17.4687100+05:30 [Information] Checking if 'BaseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:17.4687492+05:30 [Information] Resolving path 'BaseUrl' using global variables.
2025-06-30T09:38:17.4687882+05:30 [Information] Resolved JPath '$var:BaseUrl' to 'https://httpbin.org'
2025-06-30T09:38:17.4688410+05:30 [Information] Resolved template pattern '{{$var:BaseUrl}}' to 'https://httpbin.org' in '{{$var:BaseUrl}}/post'
2025-06-30T09:38:17.4688938+05:30 [Information] Resolved template '{{$var:BaseUrl}}/post' to 'https://httpbin.org/post'
2025-06-30T09:38:17.4690057+05:30 [Information] Resolving path 'row.User.Username' using test context.
2025-06-30T09:38:17.4690927+05:30 [Information] Resolved JPath '$test:row.User.Username' to 'Tom'
2025-06-30T09:38:17.4691436+05:30 [Information] Resolved template pattern '{{$test:row.User.Username}}' to 'Tom' in '{
  "username": "{{$test:row.User.Username}}",
  "email": "{{$var:Emaillocal}}",
  "org": "{{$var:OrgName}}"
}'
2025-06-30T09:38:17.4691859+05:30 [Information] Processing $var: path 'Emaillocal'
2025-06-30T09:38:17.4692239+05:30 [Information] Checking if 'Emaillocal' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:17.4692625+05:30 [Information] Resolving path 'Emaillocal' using global variables.
2025-06-30T09:38:17.4692998+05:30 [Information] Resolved JPath '$var:Emaillocal' to '<EMAIL>'
2025-06-30T09:38:17.4693360+05:30 [Information] Resolved template pattern '{{$var:Emaillocal}}' to '<EMAIL>' in '{
  "username": "{{$test:row.User.Username}}",
  "email": "{{$var:Emaillocal}}",
  "org": "{{$var:OrgName}}"
}'
2025-06-30T09:38:17.4693903+05:30 [Information] Processing $var: path 'OrgName'
2025-06-30T09:38:17.4694318+05:30 [Information] Checking if 'OrgName' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:17.4694703+05:30 [Information] Resolving path 'OrgName' using global variables.
2025-06-30T09:38:17.4695073+05:30 [Information] Resolved JPath '$var:OrgName' to 'Org1'
2025-06-30T09:38:17.4695426+05:30 [Information] Resolved template pattern '{{$var:OrgName}}' to 'Org1' in '{
  "username": "{{$test:row.User.Username}}",
  "email": "{{$var:Emaillocal}}",
  "org": "{{$var:OrgName}}"
}'
2025-06-30T09:38:17.4695801+05:30 [Information] Resolved template '{
  "username": "{{$test:row.User.Username}}",
  "email": "{{$var:Emaillocal}}",
  "org": "{{$var:OrgName}}"
}' to '{
  "username": "Tom",
  "email": "<EMAIL>",
  "org": "Org1"
}'
2025-06-30T09:38:17.4696431+05:30 [Information] Set Value to Step 'd4f4592c-0bf8-4dd4-bffa-5a2abcbfd7c2' input:
{
  "Method": "POST",
  "RequestUri": "https://httpbin.org/post",
  "Headers": {
    "Content-Type": "application/json"
  },
  "Content": {
    "username": "Tom",
    "email": "<EMAIL>",
    "org": "Org1"
  }
}
2025-06-30T09:38:18.6480269+05:30 [Information] Set Value to Step 'd4f4592c-0bf8-4dd4-bffa-5a2abcbfd7c2' output:
{
  "statusCode": 200,
  "reasonPhrase": "OK",
  "headers": [
    {
      "Key": "Date",
      "Value": [
        "Mon, 30 Jun 2025 04:08:24 GMT"
      ]
    },
    {
      "Key": "Connection",
      "Value": [
        "keep-alive"
      ]
    },
    {
      "Key": "Server",
      "Value": [
        "gunicorn/19.9.0"
      ]
    },
    {
      "Key": "Access-Control-Allow-Origin",
      "Value": [
        "*"
      ]
    },
    {
      "Key": "Access-Control-Allow-Credentials",
      "Value": [
        "true"
      ]
    }
  ],
  "content": {
    "args": {},
    "data": "{\r\n  \"username\": \"Tom\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"org\": \"Org1\"\r\n}",
    "files": {},
    "form": {},
    "headers": {
      "Content-Length": "74",
      "Content-Type": "application/json; charset=utf-8",
      "Host": "httpbin.org",
      "X-Amzn-Trace-Id": "Root=1-68620db7-7c25628b68f46e8535ae74ee"
    },
    "json": {
      "email": "<EMAIL>",
      "org": "Org1",
      "username": "Tom"
    },
    "origin": "**************",
    "url": "https://httpbin.org/post"
  }
}
2025-06-30T09:38:18.6486791+05:30 [Information] Resolving path 'output.content.json.username' using step input/output.
2025-06-30T09:38:18.6490953+05:30 [Information] Resolved JPath 'output.content.json.username' to 'Tom'
2025-06-30T09:38:18.6498960+05:30 [Information] Set Value to Step 'd4f4592c-0bf8-4dd4-bffa-5a2abcbfd7c2' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:08:17.4685427Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:38:18.6502450+05:30 [Information] Run Asserter : {"Expression1":{"Value":"200","ErrorMesage":null},"Expression2":{"JPath":"output.statusCode","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:38:18.6505086+05:30 [Information] Resolving path 'output.statusCode' using step input/output.
2025-06-30T09:38:18.6506653+05:30 [Information] Resolved JPath 'output.statusCode' to '200'
2025-06-30T09:38:18.6507757+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$test:row.User.Username}}","ErrorMesage":null},"Expression2":{"JPath":"output.content.json.username","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:38:18.6508865+05:30 [Information] Resolving path 'row.User.Username' using test context.
2025-06-30T09:38:18.6509928+05:30 [Information] Resolved JPath '$test:row.User.Username' to 'Tom'
2025-06-30T09:38:18.6511433+05:30 [Information] Resolved template pattern '{{$test:row.User.Username}}' to 'Tom' in '{{$test:row.User.Username}}'
2025-06-30T09:38:18.6512475+05:30 [Information] Resolved template '{{$test:row.User.Username}}' to 'Tom'
2025-06-30T09:38:18.6514261+05:30 [Information] Resolving path 'output.content.json.username' using step input/output.
2025-06-30T09:38:18.6515637+05:30 [Information] Resolved JPath 'output.content.json.username' to 'Tom'
2025-06-30T09:38:18.6516958+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$test:row.User.Email}}","ErrorMesage":null},"Expression2":{"JPath":"output.content.json.email","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:38:18.6517929+05:30 [Information] Resolving path 'row.User.Email' using test context.
2025-06-30T09:38:18.6518858+05:30 [Information] Resolved JPath '$test:row.User.Email' to '<EMAIL>'
2025-06-30T09:38:18.6519721+05:30 [Information] Resolved template pattern '{{$test:row.User.Email}}' to '<EMAIL>' in '{{$test:row.User.Email}}'
2025-06-30T09:38:18.6520612+05:30 [Information] Resolved template '{{$test:row.User.Email}}' to '<EMAIL>'
2025-06-30T09:38:18.6522081+05:30 [Information] Resolving path 'output.content.json.email' using step input/output.
2025-06-30T09:38:18.6523047+05:30 [Information] Resolved JPath 'output.content.json.email' to '<EMAIL>'
2025-06-30T09:38:18.6523877+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$test:row.OrgName}}","ErrorMesage":null},"Expression2":{"JPath":"output.content.json.org","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:38:18.6524709+05:30 [Information] Resolving path 'row.OrgName' using test context.
2025-06-30T09:38:18.6525525+05:30 [Information] Resolved JPath '$test:row.OrgName' to 'Org1'
2025-06-30T09:38:18.6526558+05:30 [Information] Resolved template pattern '{{$test:row.OrgName}}' to 'Org1' in '{{$test:row.OrgName}}'
2025-06-30T09:38:18.6530080+05:30 [Information] Resolved template '{{$test:row.OrgName}}' to 'Org1'
2025-06-30T09:38:18.6533634+05:30 [Information] Resolving path 'output.content.json.org' using step input/output.
2025-06-30T09:38:18.6534846+05:30 [Information] Resolved JPath 'output.content.json.org' to 'Org1'
2025-06-30T09:38:18.6536570+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$test:row.NewUsername}}","ErrorMesage":null},"Expression2":{"JPath":"output.content.json.username","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:38:18.6537848+05:30 [Information] Resolving path 'row.NewUsername' using test context.
2025-06-30T09:38:18.6538696+05:30 [Information] Resolved JPath '$test:row.NewUsername' to 'Tom'
2025-06-30T09:38:18.6539685+05:30 [Information] Resolved template pattern '{{$test:row.NewUsername}}' to 'Tom' in '{{$test:row.NewUsername}}'
2025-06-30T09:38:18.6541951+05:30 [Information] Resolved template '{{$test:row.NewUsername}}' to 'Tom'
2025-06-30T09:38:18.6546729+05:30 [Information] Resolving path 'output.content.json.username' using step input/output.
2025-06-30T09:38:18.6550402+05:30 [Information] Resolved JPath 'output.content.json.username' to 'Tom'
2025-06-30T09:38:18.6554850+05:30 [Information] Set Value to Step 'd4f4592c-0bf8-4dd4-bffa-5a2abcbfd7c2' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 1185,
  "startTime": "2025-06-30T04:08:17.4685427Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:38:18.6558965+05:30 [Information] Step 'Step to get token for Tom' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "d4f4592c-0bf8-4dd4-bffa-5a2abcbfd7c2",
        "tables": {},
        "variables": {},
        "name": "Step to get token for Tom",
        "description": "Step to get token for Tom",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "Tom",
            "email": "<EMAIL>",
            "org": "Org1"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:08:24 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"Tom\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"org\": \"Org1\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "74",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620db7-7c25628b68f46e8535ae74ee"
            },
            "json": {
              "email": "<EMAIL>",
              "org": "Org1",
              "username": "Tom"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 1185,
          "startTime": "2025-06-30T04:08:17.4685427Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaseUrl": "https://httpbin.org",
      "User": {
        "Username": "Tom",
        "Email": "<EMAIL>"
      },
      "OrgName": "Org1",
      "Usernamelocal": "Tom",
      "Emaillocal": "<EMAIL>"
    },
    "tables": {
      "UserTable": [
        {
          "User": {
            "Username": "Tom",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org1",
          "NewUsername": "Tom"
        },
        {
          "User": {
            "Username": "John",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org2"
        }
      ],
      "UserRolesTable": [
        {
          "Name": "Admin"
        },
        {
          "Name": "User"
        }
      ]
    },
    "context": {
      "repeatForTable": "UserTable",
      "row": {
        "User": {
          "Username": "Tom",
          "Email": "<EMAIL>"
        },
        "OrgName": "Org1",
        "NewUsername": "Tom"
      },
      "rowIndex": 0
    }
  }
}
2025-06-30T09:38:18.6562033+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:38:18.6562569+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:38:18.6562955+05:30 [Information] No performance data found for step 'Step to get token for {{$var:Usernamelocal}}' (http)
2025-06-30T09:38:18.6563340+05:30 [Information] [ OK       ]
2025-06-30T09:38:18.6567736+05:30 [Information] Processing $var: path 'Usernamelocal'
2025-06-30T09:38:18.6568970+05:30 [Information] Checking if 'Usernamelocal' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:18.6569534+05:30 [Information] Resolving path 'Usernamelocal' using global variables.
2025-06-30T09:38:18.6569997+05:30 [Information] Resolved JPath '$var:Usernamelocal' to 'Tom'
2025-06-30T09:38:18.6570383+05:30 [Information] Resolved template pattern '{{$var:Usernamelocal}}' to 'Tom' in 'Itterative Step for user {{$var:Usernamelocal}}'
2025-06-30T09:38:18.6570908+05:30 [Information] Resolved template 'Itterative Step for user {{$var:Usernamelocal}}' to 'Itterative Step for user Tom'
2025-06-30T09:38:18.6571451+05:30 [Information] Set Value to Step '1fbfd9d3-0e6e-413b-90d3-b6d4e5dc974c' name:
"Itterative Step for user Tom"
2025-06-30T09:38:18.6571903+05:30 [Information] Set Value to Step '1fbfd9d3-0e6e-413b-90d3-b6d4e5dc974c' description:
"Itterative Step"
2025-06-30T09:38:18.6572292+05:30 [Information] [ RUN      ] Itterative Test and Step > Itterative Step for user Tom
2025-06-30T09:38:18.6576642+05:30 [Information] Executing step 'Itterative Step for user {{$var:Usernamelocal}}' for row 1/2 in table 'UserRolesTable'.
2025-06-30T09:38:18.6577889+05:30 [Information] Default-mapped column 'Name' to variable 'Name' with value 'Admin'
2025-06-30T09:38:18.6578617+05:30 [Information] Resolved path 'row.Name' in step-specific context.
2025-06-30T09:38:18.6579093+05:30 [Information] Resolved JPath '$step:row.Name' to 'Admin'
2025-06-30T09:38:18.6579478+05:30 [Information] Resolved template pattern '{{$step:row.Name}}' to 'Admin' in '{{$step:row.Name}}'
2025-06-30T09:38:18.6579862+05:30 [Information] Resolved template '{{$step:row.Name}}' to 'Admin'
2025-06-30T09:38:18.6580618+05:30 [Information] Mapped column '{{$step:row.Name}}' to variable 'Role' with value 'Admin'
2025-06-30T09:38:18.6581094+05:30 [Information] Processing $var: path 'Usernamelocal'
2025-06-30T09:38:18.6581527+05:30 [Information] Checking if 'Usernamelocal' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:18.6581908+05:30 [Information] Resolving path 'Usernamelocal' using global variables.
2025-06-30T09:38:18.6582279+05:30 [Information] Resolved JPath '$var:Usernamelocal' to 'Tom'
2025-06-30T09:38:18.6582638+05:30 [Information] Resolved template pattern '{{$var:Usernamelocal}}' to 'Tom' in 'Running for {{$var:Usernamelocal}} and role {{$step:row.Name}}'
2025-06-30T09:38:18.6583067+05:30 [Information] Resolved path 'row.Name' in step-specific context.
2025-06-30T09:38:18.6583452+05:30 [Information] Resolved JPath '$step:row.Name' to 'Admin'
2025-06-30T09:38:18.6583813+05:30 [Information] Resolved template pattern '{{$step:row.Name}}' to 'Admin' in 'Running for {{$var:Usernamelocal}} and role {{$step:row.Name}}'
2025-06-30T09:38:18.6584679+05:30 [Information] Resolved template 'Running for {{$var:Usernamelocal}} and role {{$step:row.Name}}' to 'Running for Tom and role Admin'
2025-06-30T09:38:18.6587294+05:30 [Information] Step Iteration 1/2: Running for Tom and role Admin
2025-06-30T09:38:18.6588810+05:30 [Information] Executing step 'Running for Tom and role Admin' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "d4f4592c-0bf8-4dd4-bffa-5a2abcbfd7c2",
        "tables": {},
        "variables": {},
        "name": "Step to get token for Tom",
        "description": "Step to get token for Tom",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "Tom",
            "email": "<EMAIL>",
            "org": "Org1"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:08:24 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"Tom\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"org\": \"Org1\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "74",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620db7-7c25628b68f46e8535ae74ee"
            },
            "json": {
              "email": "<EMAIL>",
              "org": "Org1",
              "username": "Tom"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 1185,
          "startTime": "2025-06-30T04:08:17.4685427Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      },
      {
        "id": "1fbfd9d3-0e6e-413b-90d3-b6d4e5dc974c",
        "tables": {},
        "variables": {
          "Name": "Admin",
          "Role": "Admin"
        },
        "name": "Itterative Step for user Tom",
        "description": "Itterative Step",
        "context": {
          "repeatForTable": "UserRolesTable",
          "row": {
            "Name": "Admin"
          },
          "rowIndex": 0
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaseUrl": "https://httpbin.org",
      "User": {
        "Username": "Tom",
        "Email": "<EMAIL>"
      },
      "OrgName": "Org1",
      "Usernamelocal": "Tom",
      "Emaillocal": "<EMAIL>"
    },
    "tables": {
      "UserTable": [
        {
          "User": {
            "Username": "Tom",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org1",
          "NewUsername": "Tom"
        },
        {
          "User": {
            "Username": "John",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org2"
        }
      ],
      "UserRolesTable": [
        {
          "Name": "Admin"
        },
        {
          "Name": "User"
        }
      ]
    },
    "context": {
      "repeatForTable": "UserTable",
      "row": {
        "User": {
          "Username": "Tom",
          "Email": "<EMAIL>"
        },
        "OrgName": "Org1",
        "NewUsername": "Tom"
      },
      "rowIndex": 0
    }
  }
}
2025-06-30T09:38:18.6589613+05:30 [Information] Processing $var: path 'BaseUrl'
2025-06-30T09:38:18.6590083+05:30 [Information] Checking if 'BaseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:18.6590598+05:30 [Information] Resolving path 'BaseUrl' using global variables.
2025-06-30T09:38:18.6591012+05:30 [Information] Resolved JPath '$var:BaseUrl' to 'https://httpbin.org'
2025-06-30T09:38:18.6591606+05:30 [Information] Resolved template pattern '{{$var:BaseUrl}}' to 'https://httpbin.org' in '{{$var:BaseUrl}}/post'
2025-06-30T09:38:18.6591998+05:30 [Information] Resolved template '{{$var:BaseUrl}}/post' to 'https://httpbin.org/post'
2025-06-30T09:38:18.6592679+05:30 [Information] Resolving path 'row.NewUsername' using test context.
2025-06-30T09:38:18.6593107+05:30 [Information] Resolved JPath '$test:row.NewUsername' to 'Tom'
2025-06-30T09:38:18.6593486+05:30 [Information] Resolved template pattern '{{$test:row.NewUsername}}' to 'Tom' in '{
  "username": "{{$test:row.NewUsername}}",
  "role": "{{$var:Role}}"
}'
2025-06-30T09:38:18.6593994+05:30 [Information] Processing $var: path 'Role'
2025-06-30T09:38:18.6594408+05:30 [Information] Checking if 'Role' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:18.6594843+05:30 [Information] Resolved path 'Role' in step-specific variables.
2025-06-30T09:38:18.6595244+05:30 [Information] Resolved JPath '$var:Role' to 'Admin'
2025-06-30T09:38:18.6595627+05:30 [Information] Resolved template pattern '{{$var:Role}}' to 'Admin' in '{
  "username": "{{$test:row.NewUsername}}",
  "role": "{{$var:Role}}"
}'
2025-06-30T09:38:18.6596022+05:30 [Information] Resolved template '{
  "username": "{{$test:row.NewUsername}}",
  "role": "{{$var:Role}}"
}' to '{
  "username": "Tom",
  "role": "Admin"
}'
2025-06-30T09:38:18.6596758+05:30 [Information] Set Value to Step '1fbfd9d3-0e6e-413b-90d3-b6d4e5dc974c' input:
{
  "Method": "POST",
  "RequestUri": "https://httpbin.org/post",
  "Headers": {
    "Content-Type": "application/json"
  },
  "Content": {
    "username": "Tom",
    "role": "Admin"
  }
}
2025-06-30T09:38:19.4220145+05:30 [Information] Set Value to Step '1fbfd9d3-0e6e-413b-90d3-b6d4e5dc974c' output:
{
  "statusCode": 200,
  "reasonPhrase": "OK",
  "headers": [
    {
      "Key": "Date",
      "Value": [
        "Mon, 30 Jun 2025 04:08:24 GMT"
      ]
    },
    {
      "Key": "Connection",
      "Value": [
        "keep-alive"
      ]
    },
    {
      "Key": "Server",
      "Value": [
        "gunicorn/19.9.0"
      ]
    },
    {
      "Key": "Access-Control-Allow-Origin",
      "Value": [
        "*"
      ]
    },
    {
      "Key": "Access-Control-Allow-Credentials",
      "Value": [
        "true"
      ]
    }
  ],
  "content": {
    "args": {},
    "data": "{\r\n  \"username\": \"Tom\",\r\n  \"role\": \"Admin\"\r\n}",
    "files": {},
    "form": {},
    "headers": {
      "Content-Length": "45",
      "Content-Type": "application/json; charset=utf-8",
      "Host": "httpbin.org",
      "X-Amzn-Trace-Id": "Root=1-68620db8-5d13e2ce312d23d300cd12bd"
    },
    "json": {
      "role": "Admin",
      "username": "Tom"
    },
    "origin": "**************",
    "url": "https://httpbin.org/post"
  }
}
2025-06-30T09:38:19.4222783+05:30 [Information] Set Value to Step '1fbfd9d3-0e6e-413b-90d3-b6d4e5dc974c' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:08:18.6576326Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:38:19.4223591+05:30 [Information] Run Asserter : {"Expression1":{"Value":"200","ErrorMesage":null},"Expression2":{"JPath":"output.statusCode","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:38:19.4224211+05:30 [Information] Resolving path 'output.statusCode' using step input/output.
2025-06-30T09:38:19.4224720+05:30 [Information] Resolved JPath 'output.statusCode' to '200'
2025-06-30T09:38:19.4225204+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$test:row.NewUsername}}","ErrorMesage":null},"Expression2":{"JPath":"output.content.json.username","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:38:19.4225850+05:30 [Information] Resolving path 'row.NewUsername' using test context.
2025-06-30T09:38:19.4226354+05:30 [Information] Resolved JPath '$test:row.NewUsername' to 'Tom'
2025-06-30T09:38:19.4226730+05:30 [Information] Resolved template pattern '{{$test:row.NewUsername}}' to 'Tom' in '{{$test:row.NewUsername}}'
2025-06-30T09:38:19.4227089+05:30 [Information] Resolved template '{{$test:row.NewUsername}}' to 'Tom'
2025-06-30T09:38:19.4227892+05:30 [Information] Resolving path 'output.content.json.username' using step input/output.
2025-06-30T09:38:19.4228331+05:30 [Information] Resolved JPath 'output.content.json.username' to 'Tom'
2025-06-30T09:38:19.4229257+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$step:row.Name}}","ErrorMesage":null},"Expression2":{"JPath":"output.content.json.role","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:38:19.4230000+05:30 [Information] Resolved path 'row.Name' in step-specific context.
2025-06-30T09:38:19.4230396+05:30 [Information] Resolved JPath '$step:row.Name' to 'Admin'
2025-06-30T09:38:19.4230736+05:30 [Information] Resolved template pattern '{{$step:row.Name}}' to 'Admin' in '{{$step:row.Name}}'
2025-06-30T09:38:19.4231083+05:30 [Information] Resolved template '{{$step:row.Name}}' to 'Admin'
2025-06-30T09:38:19.4231672+05:30 [Information] Resolving path 'output.content.json.role' using step input/output.
2025-06-30T09:38:19.4232085+05:30 [Information] Resolved JPath 'output.content.json.role' to 'Admin'
2025-06-30T09:38:19.4232627+05:30 [Information] Set Value to Step '1fbfd9d3-0e6e-413b-90d3-b6d4e5dc974c' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 763,
  "startTime": "2025-06-30T04:08:18.6576326Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:38:19.4233257+05:30 [Information] Step 'Running for Tom and role Admin' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "d4f4592c-0bf8-4dd4-bffa-5a2abcbfd7c2",
        "tables": {},
        "variables": {},
        "name": "Step to get token for Tom",
        "description": "Step to get token for Tom",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "Tom",
            "email": "<EMAIL>",
            "org": "Org1"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:08:24 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"Tom\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"org\": \"Org1\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "74",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620db7-7c25628b68f46e8535ae74ee"
            },
            "json": {
              "email": "<EMAIL>",
              "org": "Org1",
              "username": "Tom"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 1185,
          "startTime": "2025-06-30T04:08:17.4685427Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      },
      {
        "id": "1fbfd9d3-0e6e-413b-90d3-b6d4e5dc974c",
        "tables": {},
        "variables": {
          "Name": "Admin",
          "Role": "Admin"
        },
        "name": "Itterative Step for user Tom",
        "description": "Itterative Step",
        "context": {
          "repeatForTable": "UserRolesTable",
          "row": {
            "Name": "Admin"
          },
          "rowIndex": 0
        },
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "Tom",
            "role": "Admin"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:08:24 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"Tom\",\r\n  \"role\": \"Admin\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "45",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620db8-5d13e2ce312d23d300cd12bd"
            },
            "json": {
              "role": "Admin",
              "username": "Tom"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 763,
          "startTime": "2025-06-30T04:08:18.6576326Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaseUrl": "https://httpbin.org",
      "User": {
        "Username": "Tom",
        "Email": "<EMAIL>"
      },
      "OrgName": "Org1",
      "Usernamelocal": "Tom",
      "Emaillocal": "<EMAIL>"
    },
    "tables": {
      "UserTable": [
        {
          "User": {
            "Username": "Tom",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org1",
          "NewUsername": "Tom"
        },
        {
          "User": {
            "Username": "John",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org2"
        }
      ],
      "UserRolesTable": [
        {
          "Name": "Admin"
        },
        {
          "Name": "User"
        }
      ]
    },
    "context": {
      "repeatForTable": "UserTable",
      "row": {
        "User": {
          "Username": "Tom",
          "Email": "<EMAIL>"
        },
        "OrgName": "Org1",
        "NewUsername": "Tom"
      },
      "rowIndex": 0
    }
  }
}
2025-06-30T09:38:19.4233772+05:30 [Information] Executing step 'Itterative Step for user {{$var:Usernamelocal}}' for row 2/2 in table 'UserRolesTable'.
2025-06-30T09:38:19.4234481+05:30 [Information] Default-mapped column 'Name' to variable 'Name' with value 'User'
2025-06-30T09:38:19.4235129+05:30 [Information] Resolved path 'row.Name' in step-specific context.
2025-06-30T09:38:19.4236331+05:30 [Information] Resolved JPath '$step:row.Name' to 'User'
2025-06-30T09:38:19.4236919+05:30 [Information] Resolved template pattern '{{$step:row.Name}}' to 'User' in '{{$step:row.Name}}'
2025-06-30T09:38:19.4237344+05:30 [Information] Resolved template '{{$step:row.Name}}' to 'User'
2025-06-30T09:38:19.4238136+05:30 [Information] Mapped column '{{$step:row.Name}}' to variable 'Role' with value 'User'
2025-06-30T09:38:19.4238634+05:30 [Information] Processing $var: path 'Usernamelocal'
2025-06-30T09:38:19.4239160+05:30 [Information] Checking if 'Usernamelocal' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:19.4239558+05:30 [Information] Resolving path 'Usernamelocal' using global variables.
2025-06-30T09:38:19.4239931+05:30 [Information] Resolved JPath '$var:Usernamelocal' to 'Tom'
2025-06-30T09:38:19.4240283+05:30 [Information] Resolved template pattern '{{$var:Usernamelocal}}' to 'Tom' in 'Running for {{$var:Usernamelocal}} and role {{$step:row.Name}}'
2025-06-30T09:38:19.4241620+05:30 [Information] Resolved path 'row.Name' in step-specific context.
2025-06-30T09:38:19.4242003+05:30 [Information] Resolved JPath '$step:row.Name' to 'User'
2025-06-30T09:38:19.4242372+05:30 [Information] Resolved template pattern '{{$step:row.Name}}' to 'User' in 'Running for {{$var:Usernamelocal}} and role {{$step:row.Name}}'
2025-06-30T09:38:19.4242729+05:30 [Information] Resolved template 'Running for {{$var:Usernamelocal}} and role {{$step:row.Name}}' to 'Running for Tom and role User'
2025-06-30T09:38:19.4245972+05:30 [Information] Step Iteration 2/2: Running for Tom and role User
2025-06-30T09:38:19.4247996+05:30 [Information] Executing step 'Running for Tom and role User' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "d4f4592c-0bf8-4dd4-bffa-5a2abcbfd7c2",
        "tables": {},
        "variables": {},
        "name": "Step to get token for Tom",
        "description": "Step to get token for Tom",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "Tom",
            "email": "<EMAIL>",
            "org": "Org1"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:08:24 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"Tom\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"org\": \"Org1\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "74",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620db7-7c25628b68f46e8535ae74ee"
            },
            "json": {
              "email": "<EMAIL>",
              "org": "Org1",
              "username": "Tom"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 1185,
          "startTime": "2025-06-30T04:08:17.4685427Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      },
      {
        "id": "1fbfd9d3-0e6e-413b-90d3-b6d4e5dc974c",
        "tables": {},
        "variables": {
          "Name": "User",
          "Role": "User"
        },
        "name": "Itterative Step for user Tom",
        "description": "Itterative Step",
        "context": {
          "repeatForTable": "UserRolesTable",
          "row": {
            "Name": "User"
          },
          "rowIndex": 1
        },
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "Tom",
            "role": "Admin"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:08:24 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"Tom\",\r\n  \"role\": \"Admin\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "45",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620db8-5d13e2ce312d23d300cd12bd"
            },
            "json": {
              "role": "Admin",
              "username": "Tom"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 763,
          "startTime": "2025-06-30T04:08:18.6576326Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaseUrl": "https://httpbin.org",
      "User": {
        "Username": "Tom",
        "Email": "<EMAIL>"
      },
      "OrgName": "Org1",
      "Usernamelocal": "Tom",
      "Emaillocal": "<EMAIL>"
    },
    "tables": {
      "UserTable": [
        {
          "User": {
            "Username": "Tom",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org1",
          "NewUsername": "Tom"
        },
        {
          "User": {
            "Username": "John",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org2"
        }
      ],
      "UserRolesTable": [
        {
          "Name": "Admin"
        },
        {
          "Name": "User"
        }
      ]
    },
    "context": {
      "repeatForTable": "UserTable",
      "row": {
        "User": {
          "Username": "Tom",
          "Email": "<EMAIL>"
        },
        "OrgName": "Org1",
        "NewUsername": "Tom"
      },
      "rowIndex": 0
    }
  }
}
2025-06-30T09:38:19.4248667+05:30 [Information] Processing $var: path 'BaseUrl'
2025-06-30T09:38:19.4249133+05:30 [Information] Checking if 'BaseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:19.4249576+05:30 [Information] Resolving path 'BaseUrl' using global variables.
2025-06-30T09:38:19.4249955+05:30 [Information] Resolved JPath '$var:BaseUrl' to 'https://httpbin.org'
2025-06-30T09:38:19.4250447+05:30 [Information] Resolved template pattern '{{$var:BaseUrl}}' to 'https://httpbin.org' in '{{$var:BaseUrl}}/post'
2025-06-30T09:38:19.4250799+05:30 [Information] Resolved template '{{$var:BaseUrl}}/post' to 'https://httpbin.org/post'
2025-06-30T09:38:19.4251559+05:30 [Information] Resolving path 'row.NewUsername' using test context.
2025-06-30T09:38:19.4251977+05:30 [Information] Resolved JPath '$test:row.NewUsername' to 'Tom'
2025-06-30T09:38:19.4252495+05:30 [Information] Resolved template pattern '{{$test:row.NewUsername}}' to 'Tom' in '{
  "username": "{{$test:row.NewUsername}}",
  "role": "{{$var:Role}}"
}'
2025-06-30T09:38:19.4252974+05:30 [Information] Processing $var: path 'Role'
2025-06-30T09:38:19.4253359+05:30 [Information] Checking if 'Role' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:19.4254856+05:30 [Information] Resolved path 'Role' in step-specific variables.
2025-06-30T09:38:19.4255556+05:30 [Information] Resolved JPath '$var:Role' to 'User'
2025-06-30T09:38:19.4255971+05:30 [Information] Resolved template pattern '{{$var:Role}}' to 'User' in '{
  "username": "{{$test:row.NewUsername}}",
  "role": "{{$var:Role}}"
}'
2025-06-30T09:38:19.4256354+05:30 [Information] Resolved template '{
  "username": "{{$test:row.NewUsername}}",
  "role": "{{$var:Role}}"
}' to '{
  "username": "Tom",
  "role": "User"
}'
2025-06-30T09:38:19.4257062+05:30 [Information] Set Value to Step '1fbfd9d3-0e6e-413b-90d3-b6d4e5dc974c' input:
{
  "Method": "POST",
  "RequestUri": "https://httpbin.org/post",
  "Headers": {
    "Content-Type": "application/json"
  },
  "Content": {
    "username": "Tom",
    "role": "User"
  }
}
2025-06-30T09:38:19.6787192+05:30 [Information] Set Value to Step '1fbfd9d3-0e6e-413b-90d3-b6d4e5dc974c' output:
{
  "statusCode": 200,
  "reasonPhrase": "OK",
  "headers": [
    {
      "Key": "Date",
      "Value": [
        "Mon, 30 Jun 2025 04:08:25 GMT"
      ]
    },
    {
      "Key": "Connection",
      "Value": [
        "keep-alive"
      ]
    },
    {
      "Key": "Server",
      "Value": [
        "gunicorn/19.9.0"
      ]
    },
    {
      "Key": "Access-Control-Allow-Origin",
      "Value": [
        "*"
      ]
    },
    {
      "Key": "Access-Control-Allow-Credentials",
      "Value": [
        "true"
      ]
    }
  ],
  "content": {
    "args": {},
    "data": "{\r\n  \"username\": \"Tom\",\r\n  \"role\": \"User\"\r\n}",
    "files": {},
    "form": {},
    "headers": {
      "Content-Length": "44",
      "Content-Type": "application/json; charset=utf-8",
      "Host": "httpbin.org",
      "X-Amzn-Trace-Id": "Root=1-68620db9-54a60e8f5ff6f7c729c6be0a"
    },
    "json": {
      "role": "User",
      "username": "Tom"
    },
    "origin": "**************",
    "url": "https://httpbin.org/post"
  }
}
2025-06-30T09:38:19.6792617+05:30 [Information] Set Value to Step '1fbfd9d3-0e6e-413b-90d3-b6d4e5dc974c' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:08:18.6576326Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:38:19.6795034+05:30 [Information] Run Asserter : {"Expression1":{"Value":"200","ErrorMesage":null},"Expression2":{"JPath":"output.statusCode","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:38:19.6796568+05:30 [Information] Resolving path 'output.statusCode' using step input/output.
2025-06-30T09:38:19.6797955+05:30 [Information] Resolved JPath 'output.statusCode' to '200'
2025-06-30T09:38:19.6799766+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$test:row.NewUsername}}","ErrorMesage":null},"Expression2":{"JPath":"output.content.json.username","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:38:19.6801088+05:30 [Information] Resolving path 'row.NewUsername' using test context.
2025-06-30T09:38:19.6802344+05:30 [Information] Resolved JPath '$test:row.NewUsername' to 'Tom'
2025-06-30T09:38:19.6803366+05:30 [Information] Resolved template pattern '{{$test:row.NewUsername}}' to 'Tom' in '{{$test:row.NewUsername}}'
2025-06-30T09:38:19.6804401+05:30 [Information] Resolved template '{{$test:row.NewUsername}}' to 'Tom'
2025-06-30T09:38:19.6806702+05:30 [Information] Resolving path 'output.content.json.username' using step input/output.
2025-06-30T09:38:19.6807927+05:30 [Information] Resolved JPath 'output.content.json.username' to 'Tom'
2025-06-30T09:38:19.6810430+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$step:row.Name}}","ErrorMesage":null},"Expression2":{"JPath":"output.content.json.role","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:38:19.6813423+05:30 [Information] Resolved path 'row.Name' in step-specific context.
2025-06-30T09:38:19.6814673+05:30 [Information] Resolved JPath '$step:row.Name' to 'User'
2025-06-30T09:38:19.6815706+05:30 [Information] Resolved template pattern '{{$step:row.Name}}' to 'User' in '{{$step:row.Name}}'
2025-06-30T09:38:19.6816630+05:30 [Information] Resolved template '{{$step:row.Name}}' to 'User'
2025-06-30T09:38:19.6818220+05:30 [Information] Resolving path 'output.content.json.role' using step input/output.
2025-06-30T09:38:19.6819121+05:30 [Information] Resolved JPath 'output.content.json.role' to 'User'
2025-06-30T09:38:19.6820255+05:30 [Information] Set Value to Step '1fbfd9d3-0e6e-413b-90d3-b6d4e5dc974c' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 256,
  "startTime": "2025-06-30T04:08:18.6576326Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:38:19.6821694+05:30 [Information] Step 'Running for Tom and role User' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "d4f4592c-0bf8-4dd4-bffa-5a2abcbfd7c2",
        "tables": {},
        "variables": {},
        "name": "Step to get token for Tom",
        "description": "Step to get token for Tom",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "Tom",
            "email": "<EMAIL>",
            "org": "Org1"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:08:24 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"Tom\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"org\": \"Org1\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "74",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620db7-7c25628b68f46e8535ae74ee"
            },
            "json": {
              "email": "<EMAIL>",
              "org": "Org1",
              "username": "Tom"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 1185,
          "startTime": "2025-06-30T04:08:17.4685427Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      },
      {
        "id": "1fbfd9d3-0e6e-413b-90d3-b6d4e5dc974c",
        "tables": {},
        "variables": {
          "Name": "User",
          "Role": "User"
        },
        "name": "Itterative Step for user Tom",
        "description": "Itterative Step",
        "context": {
          "repeatForTable": "UserRolesTable",
          "row": {
            "Name": "User"
          },
          "rowIndex": 1
        },
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "Tom",
            "role": "User"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:08:25 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"Tom\",\r\n  \"role\": \"User\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "44",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620db9-54a60e8f5ff6f7c729c6be0a"
            },
            "json": {
              "role": "User",
              "username": "Tom"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 256,
          "startTime": "2025-06-30T04:08:18.6576326Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaseUrl": "https://httpbin.org",
      "User": {
        "Username": "Tom",
        "Email": "<EMAIL>"
      },
      "OrgName": "Org1",
      "Usernamelocal": "Tom",
      "Emaillocal": "<EMAIL>"
    },
    "tables": {
      "UserTable": [
        {
          "User": {
            "Username": "Tom",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org1",
          "NewUsername": "Tom"
        },
        {
          "User": {
            "Username": "John",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org2"
        }
      ],
      "UserRolesTable": [
        {
          "Name": "Admin"
        },
        {
          "Name": "User"
        }
      ]
    },
    "context": {
      "repeatForTable": "UserTable",
      "row": {
        "User": {
          "Username": "Tom",
          "Email": "<EMAIL>"
        },
        "OrgName": "Org1",
        "NewUsername": "Tom"
      },
      "rowIndex": 0
    }
  }
}
2025-06-30T09:38:19.6822999+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:38:19.6824670+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:38:19.6826197+05:30 [Information] No performance data found for step 'Itterative Step for user {{$var:Usernamelocal}}' (http)
2025-06-30T09:38:19.6827053+05:30 [Information] [ OK       ]
2025-06-30T09:38:19.6832887+05:30 [Information] Default-mapped column 'User' to variable 'User' with value '{
  "Username": "John",
  "Email": "<EMAIL>"
}'
2025-06-30T09:38:19.6834784+05:30 [Information] Default-mapped column 'OrgName' to variable 'OrgName' with value 'Org2'
2025-06-30T09:38:19.6835811+05:30 [Information] Processing $var: path 'User.Username'
2025-06-30T09:38:19.6836551+05:30 [Information] Checking if 'User.Username' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=True, result=False
2025-06-30T09:38:19.6837211+05:30 [Information] Resolving path 'User.Username' using global variables.
2025-06-30T09:38:19.6837839+05:30 [Information] Resolved JPath '$var:User.Username' to 'John'
2025-06-30T09:38:19.6838336+05:30 [Information] Resolved template pattern '{{$var:User.Username}}' to 'John' in '{{$var:User.Username}}'
2025-06-30T09:38:19.6839319+05:30 [Information] Resolved template '{{$var:User.Username}}' to 'John'
2025-06-30T09:38:19.6841357+05:30 [Information] Mapped column '{{$var:User.Username}}' to variable 'Usernamelocal' with value 'John'
2025-06-30T09:38:19.6842417+05:30 [Information] Processing $var: path 'User.Email'
2025-06-30T09:38:19.6843090+05:30 [Information] Checking if 'User.Email' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=True, result=False
2025-06-30T09:38:19.6843645+05:30 [Information] Resolving path 'User.Email' using global variables.
2025-06-30T09:38:19.6844719+05:30 [Information] Resolved JPath '$var:User.Email' to '<EMAIL>'
2025-06-30T09:38:19.6845875+05:30 [Information] Resolved template pattern '{{$var:User.Email}}' to '<EMAIL>' in '{{$var:User.Email}}'
2025-06-30T09:38:19.6846563+05:30 [Information] Resolved template '{{$var:User.Email}}' to '<EMAIL>'
2025-06-30T09:38:19.6847417+05:30 [Information] Mapped column '{{$var:User.Email}}' to variable 'Emaillocal' with value '<EMAIL>'
2025-06-30T09:38:19.6848008+05:30 [Information] Processing $var: path 'Usernamelocal'
2025-06-30T09:38:19.6848487+05:30 [Information] Checking if 'Usernamelocal' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:19.6848934+05:30 [Information] Resolving path 'Usernamelocal' using global variables.
2025-06-30T09:38:19.6850720+05:30 [Information] Resolved JPath '$var:Usernamelocal' to 'John'
2025-06-30T09:38:19.6851329+05:30 [Information] Resolved template pattern '{{$var:Usernamelocal}}' to 'John' in 'Running Test for {{$var:Usernamelocal}}'
2025-06-30T09:38:19.6851768+05:30 [Information] Resolved template 'Running Test for {{$var:Usernamelocal}}' to 'Running Test for John'
2025-06-30T09:38:19.6853909+05:30 [Information] Test Iteration 2/2: Running Test for John
2025-06-30T09:38:19.6855331+05:30 [Information] Processing $var: path 'Usernamelocal'
2025-06-30T09:38:19.6855946+05:30 [Information] Checking if 'Usernamelocal' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:19.6856783+05:30 [Information] Resolving path 'Usernamelocal' using global variables.
2025-06-30T09:38:19.6857263+05:30 [Information] Resolved JPath '$var:Usernamelocal' to 'John'
2025-06-30T09:38:19.6857679+05:30 [Information] Resolved template pattern '{{$var:Usernamelocal}}' to 'John' in 'Step to get token for {{$var:Usernamelocal}}'
2025-06-30T09:38:19.6858086+05:30 [Information] Resolved template 'Step to get token for {{$var:Usernamelocal}}' to 'Step to get token for John'
2025-06-30T09:38:19.6858572+05:30 [Information] Set Value to Step '854bc276-7174-4de4-98c5-6d4275d61f85' name:
"Step to get token for John"
2025-06-30T09:38:19.6858980+05:30 [Information] Set Value to Step '854bc276-7174-4de4-98c5-6d4275d61f85' description:
"Step to get token for Tom"
2025-06-30T09:38:19.6859371+05:30 [Information] [ RUN      ] Itterative Test and Step > Step to get token for John
2025-06-30T09:38:19.6861593+05:30 [Information] Executing single instance of step 'Step to get token for {{$var:Usernamelocal}}'.
2025-06-30T09:38:19.6862567+05:30 [Information] Executing step 'Step to get token for John' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "d4f4592c-0bf8-4dd4-bffa-5a2abcbfd7c2",
        "tables": {},
        "variables": {},
        "name": "Step to get token for Tom",
        "description": "Step to get token for Tom",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "Tom",
            "email": "<EMAIL>",
            "org": "Org1"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:08:24 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"Tom\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"org\": \"Org1\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "74",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620db7-7c25628b68f46e8535ae74ee"
            },
            "json": {
              "email": "<EMAIL>",
              "org": "Org1",
              "username": "Tom"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 1185,
          "startTime": "2025-06-30T04:08:17.4685427Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      },
      {
        "id": "1fbfd9d3-0e6e-413b-90d3-b6d4e5dc974c",
        "tables": {},
        "variables": {
          "Name": "User",
          "Role": "User"
        },
        "name": "Itterative Step for user Tom",
        "description": "Itterative Step",
        "context": {
          "repeatForTable": "UserRolesTable",
          "row": {
            "Name": "User"
          },
          "rowIndex": 1
        },
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "Tom",
            "role": "User"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:08:25 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"Tom\",\r\n  \"role\": \"User\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "44",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620db9-54a60e8f5ff6f7c729c6be0a"
            },
            "json": {
              "role": "User",
              "username": "Tom"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 256,
          "startTime": "2025-06-30T04:08:18.6576326Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      },
      {
        "id": "854bc276-7174-4de4-98c5-6d4275d61f85",
        "tables": {},
        "variables": {},
        "name": "Step to get token for John",
        "description": "Step to get token for Tom"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaseUrl": "https://httpbin.org",
      "User": {
        "Username": "John",
        "Email": "<EMAIL>"
      },
      "OrgName": "Org2",
      "Usernamelocal": "John",
      "Emaillocal": "<EMAIL>"
    },
    "tables": {
      "UserTable": [
        {
          "User": {
            "Username": "Tom",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org1",
          "NewUsername": "Tom"
        },
        {
          "User": {
            "Username": "John",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org2"
        }
      ],
      "UserRolesTable": [
        {
          "Name": "Admin"
        },
        {
          "Name": "User"
        }
      ]
    },
    "context": {
      "repeatForTable": "UserTable",
      "row": {
        "User": {
          "Username": "John",
          "Email": "<EMAIL>"
        },
        "OrgName": "Org2"
      },
      "rowIndex": 1
    }
  }
}
2025-06-30T09:38:19.6863387+05:30 [Information] Processing $var: path 'BaseUrl'
2025-06-30T09:38:19.6863890+05:30 [Information] Checking if 'BaseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:19.6864294+05:30 [Information] Resolving path 'BaseUrl' using global variables.
2025-06-30T09:38:19.6864703+05:30 [Information] Resolved JPath '$var:BaseUrl' to 'https://httpbin.org'
2025-06-30T09:38:19.6865146+05:30 [Information] Resolved template pattern '{{$var:BaseUrl}}' to 'https://httpbin.org' in '{{$var:BaseUrl}}/post'
2025-06-30T09:38:19.6865523+05:30 [Information] Resolved template '{{$var:BaseUrl}}/post' to 'https://httpbin.org/post'
2025-06-30T09:38:19.6866099+05:30 [Information] Resolving path 'row.User.Username' using test context.
2025-06-30T09:38:19.6866511+05:30 [Information] Resolved JPath '$test:row.User.Username' to 'John'
2025-06-30T09:38:19.6866880+05:30 [Information] Resolved template pattern '{{$test:row.User.Username}}' to 'John' in '{
  "username": "{{$test:row.User.Username}}",
  "email": "{{$var:Emaillocal}}",
  "org": "{{$var:OrgName}}"
}'
2025-06-30T09:38:19.6867255+05:30 [Information] Processing $var: path 'Emaillocal'
2025-06-30T09:38:19.6867630+05:30 [Information] Checking if 'Emaillocal' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:19.6868003+05:30 [Information] Resolving path 'Emaillocal' using global variables.
2025-06-30T09:38:19.6868387+05:30 [Information] Resolved JPath '$var:Emaillocal' to '<EMAIL>'
2025-06-30T09:38:19.6868742+05:30 [Information] Resolved template pattern '{{$var:Emaillocal}}' to '<EMAIL>' in '{
  "username": "{{$test:row.User.Username}}",
  "email": "{{$var:Emaillocal}}",
  "org": "{{$var:OrgName}}"
}'
2025-06-30T09:38:19.6869225+05:30 [Information] Processing $var: path 'OrgName'
2025-06-30T09:38:19.6869794+05:30 [Information] Checking if 'OrgName' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:19.6870181+05:30 [Information] Resolving path 'OrgName' using global variables.
2025-06-30T09:38:19.6870547+05:30 [Information] Resolved JPath '$var:OrgName' to 'Org2'
2025-06-30T09:38:19.6870900+05:30 [Information] Resolved template pattern '{{$var:OrgName}}' to 'Org2' in '{
  "username": "{{$test:row.User.Username}}",
  "email": "{{$var:Emaillocal}}",
  "org": "{{$var:OrgName}}"
}'
2025-06-30T09:38:19.6871259+05:30 [Information] Resolved template '{
  "username": "{{$test:row.User.Username}}",
  "email": "{{$var:Emaillocal}}",
  "org": "{{$var:OrgName}}"
}' to '{
  "username": "John",
  "email": "<EMAIL>",
  "org": "Org2"
}'
2025-06-30T09:38:19.6871919+05:30 [Information] Set Value to Step '854bc276-7174-4de4-98c5-6d4275d61f85' input:
{
  "Method": "POST",
  "RequestUri": "https://httpbin.org/post",
  "Headers": {
    "Content-Type": "application/json"
  },
  "Content": {
    "username": "John",
    "email": "<EMAIL>",
    "org": "Org2"
  }
}
2025-06-30T09:38:19.9337692+05:30 [Information] Set Value to Step '854bc276-7174-4de4-98c5-6d4275d61f85' output:
{
  "statusCode": 200,
  "reasonPhrase": "OK",
  "headers": [
    {
      "Key": "Date",
      "Value": [
        "Mon, 30 Jun 2025 04:08:25 GMT"
      ]
    },
    {
      "Key": "Connection",
      "Value": [
        "keep-alive"
      ]
    },
    {
      "Key": "Server",
      "Value": [
        "gunicorn/19.9.0"
      ]
    },
    {
      "Key": "Access-Control-Allow-Origin",
      "Value": [
        "*"
      ]
    },
    {
      "Key": "Access-Control-Allow-Credentials",
      "Value": [
        "true"
      ]
    }
  ],
  "content": {
    "args": {},
    "data": "{\r\n  \"username\": \"John\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"org\": \"Org2\"\r\n}",
    "files": {},
    "form": {},
    "headers": {
      "Content-Length": "76",
      "Content-Type": "application/json; charset=utf-8",
      "Host": "httpbin.org",
      "X-Amzn-Trace-Id": "Root=1-68620db9-3ad38d4c2cbde6af444a2b77"
    },
    "json": {
      "email": "<EMAIL>",
      "org": "Org2",
      "username": "John"
    },
    "origin": "**************",
    "url": "https://httpbin.org/post"
  }
}
2025-06-30T09:38:19.9340272+05:30 [Information] Resolving path 'output.content.json.username' using step input/output.
2025-06-30T09:38:19.9341205+05:30 [Information] Resolved JPath 'output.content.json.username' to 'John'
2025-06-30T09:38:19.9343466+05:30 [Information] Set Value to Step '854bc276-7174-4de4-98c5-6d4275d61f85' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:08:19.6861577Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:38:19.9344725+05:30 [Information] Run Asserter : {"Expression1":{"Value":"200","ErrorMesage":null},"Expression2":{"JPath":"output.statusCode","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:38:19.9345370+05:30 [Information] Resolving path 'output.statusCode' using step input/output.
2025-06-30T09:38:19.9345849+05:30 [Information] Resolved JPath 'output.statusCode' to '200'
2025-06-30T09:38:19.9346443+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$test:row.User.Username}}","ErrorMesage":null},"Expression2":{"JPath":"output.content.json.username","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:38:19.9346873+05:30 [Information] Resolving path 'row.User.Username' using test context.
2025-06-30T09:38:19.9347326+05:30 [Information] Resolved JPath '$test:row.User.Username' to 'John'
2025-06-30T09:38:19.9347787+05:30 [Information] Resolved template pattern '{{$test:row.User.Username}}' to 'John' in '{{$test:row.User.Username}}'
2025-06-30T09:38:19.9348357+05:30 [Information] Resolved template '{{$test:row.User.Username}}' to 'John'
2025-06-30T09:38:19.9349212+05:30 [Information] Resolving path 'output.content.json.username' using step input/output.
2025-06-30T09:38:19.9349670+05:30 [Information] Resolved JPath 'output.content.json.username' to 'John'
2025-06-30T09:38:19.9350158+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$test:row.User.Email}}","ErrorMesage":null},"Expression2":{"JPath":"output.content.json.email","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:38:19.9350586+05:30 [Information] Resolving path 'row.User.Email' using test context.
2025-06-30T09:38:19.9350969+05:30 [Information] Resolved JPath '$test:row.User.Email' to '<EMAIL>'
2025-06-30T09:38:19.9351333+05:30 [Information] Resolved template pattern '{{$test:row.User.Email}}' to '<EMAIL>' in '{{$test:row.User.Email}}'
2025-06-30T09:38:19.9351683+05:30 [Information] Resolved template '{{$test:row.User.Email}}' to '<EMAIL>'
2025-06-30T09:38:19.9352238+05:30 [Information] Resolving path 'output.content.json.email' using step input/output.
2025-06-30T09:38:19.9352680+05:30 [Information] Resolved JPath 'output.content.json.email' to '<EMAIL>'
2025-06-30T09:38:19.9353475+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$test:row.OrgName}}","ErrorMesage":null},"Expression2":{"JPath":"output.content.json.org","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:38:19.9354032+05:30 [Information] Resolving path 'row.OrgName' using test context.
2025-06-30T09:38:19.9354547+05:30 [Information] Resolved JPath '$test:row.OrgName' to 'Org2'
2025-06-30T09:38:19.9355669+05:30 [Information] Resolved template pattern '{{$test:row.OrgName}}' to 'Org2' in '{{$test:row.OrgName}}'
2025-06-30T09:38:19.9356400+05:30 [Information] Resolved template '{{$test:row.OrgName}}' to 'Org2'
2025-06-30T09:38:19.9357176+05:30 [Information] Resolving path 'output.content.json.org' using step input/output.
2025-06-30T09:38:19.9357666+05:30 [Information] Resolved JPath 'output.content.json.org' to 'Org2'
2025-06-30T09:38:19.9358148+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$test:row.NewUsername}}","ErrorMesage":null},"Expression2":{"JPath":"output.content.json.username","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:38:19.9358586+05:30 [Information] Resolving path 'row.NewUsername' using test context.
2025-06-30T09:38:19.9358953+05:30 [Information] Resolved JPath '$test:row.NewUsername' to 'John'
2025-06-30T09:38:19.9359313+05:30 [Information] Resolved template pattern '{{$test:row.NewUsername}}' to 'John' in '{{$test:row.NewUsername}}'
2025-06-30T09:38:19.9359753+05:30 [Information] Resolved template '{{$test:row.NewUsername}}' to 'John'
2025-06-30T09:38:19.9360345+05:30 [Information] Resolving path 'output.content.json.username' using step input/output.
2025-06-30T09:38:19.9361000+05:30 [Information] Resolved JPath 'output.content.json.username' to 'John'
2025-06-30T09:38:19.9362159+05:30 [Information] Set Value to Step '854bc276-7174-4de4-98c5-6d4275d61f85' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 248,
  "startTime": "2025-06-30T04:08:19.6861577Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:38:19.9363216+05:30 [Information] Step 'Step to get token for John' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "d4f4592c-0bf8-4dd4-bffa-5a2abcbfd7c2",
        "tables": {},
        "variables": {},
        "name": "Step to get token for Tom",
        "description": "Step to get token for Tom",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "Tom",
            "email": "<EMAIL>",
            "org": "Org1"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:08:24 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"Tom\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"org\": \"Org1\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "74",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620db7-7c25628b68f46e8535ae74ee"
            },
            "json": {
              "email": "<EMAIL>",
              "org": "Org1",
              "username": "Tom"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 1185,
          "startTime": "2025-06-30T04:08:17.4685427Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      },
      {
        "id": "1fbfd9d3-0e6e-413b-90d3-b6d4e5dc974c",
        "tables": {},
        "variables": {
          "Name": "User",
          "Role": "User"
        },
        "name": "Itterative Step for user Tom",
        "description": "Itterative Step",
        "context": {
          "repeatForTable": "UserRolesTable",
          "row": {
            "Name": "User"
          },
          "rowIndex": 1
        },
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "Tom",
            "role": "User"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:08:25 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"Tom\",\r\n  \"role\": \"User\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "44",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620db9-54a60e8f5ff6f7c729c6be0a"
            },
            "json": {
              "role": "User",
              "username": "Tom"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 256,
          "startTime": "2025-06-30T04:08:18.6576326Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      },
      {
        "id": "854bc276-7174-4de4-98c5-6d4275d61f85",
        "tables": {},
        "variables": {},
        "name": "Step to get token for John",
        "description": "Step to get token for Tom",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "John",
            "email": "<EMAIL>",
            "org": "Org2"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:08:25 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"John\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"org\": \"Org2\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "76",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620db9-3ad38d4c2cbde6af444a2b77"
            },
            "json": {
              "email": "<EMAIL>",
              "org": "Org2",
              "username": "John"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 248,
          "startTime": "2025-06-30T04:08:19.6861577Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaseUrl": "https://httpbin.org",
      "User": {
        "Username": "John",
        "Email": "<EMAIL>"
      },
      "OrgName": "Org2",
      "Usernamelocal": "John",
      "Emaillocal": "<EMAIL>"
    },
    "tables": {
      "UserTable": [
        {
          "User": {
            "Username": "Tom",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org1",
          "NewUsername": "Tom"
        },
        {
          "User": {
            "Username": "John",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org2",
          "NewUsername": "John"
        }
      ],
      "UserRolesTable": [
        {
          "Name": "Admin"
        },
        {
          "Name": "User"
        }
      ]
    },
    "context": {
      "repeatForTable": "UserTable",
      "row": {
        "User": {
          "Username": "John",
          "Email": "<EMAIL>"
        },
        "OrgName": "Org2",
        "NewUsername": "John"
      },
      "rowIndex": 1
    }
  }
}
2025-06-30T09:38:19.9364140+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:38:19.9364816+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:38:19.9365354+05:30 [Information] No performance data found for step 'Step to get token for {{$var:Usernamelocal}}' (http)
2025-06-30T09:38:19.9365922+05:30 [Information] [ OK       ]
2025-06-30T09:38:19.9368229+05:30 [Information] Processing $var: path 'Usernamelocal'
2025-06-30T09:38:19.9369336+05:30 [Information] Checking if 'Usernamelocal' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:19.9370061+05:30 [Information] Resolving path 'Usernamelocal' using global variables.
2025-06-30T09:38:19.9370944+05:30 [Information] Resolved JPath '$var:Usernamelocal' to 'John'
2025-06-30T09:38:19.9371527+05:30 [Information] Resolved template pattern '{{$var:Usernamelocal}}' to 'John' in 'Itterative Step for user {{$var:Usernamelocal}}'
2025-06-30T09:38:19.9372102+05:30 [Information] Resolved template 'Itterative Step for user {{$var:Usernamelocal}}' to 'Itterative Step for user John'
2025-06-30T09:38:19.9372803+05:30 [Information] Set Value to Step '88b229c7-c75e-41ae-96b9-5e431b831e2b' name:
"Itterative Step for user John"
2025-06-30T09:38:19.9373288+05:30 [Information] Set Value to Step '88b229c7-c75e-41ae-96b9-5e431b831e2b' description:
"Itterative Step"
2025-06-30T09:38:19.9373699+05:30 [Information] [ RUN      ] Itterative Test and Step > Itterative Step for user John
2025-06-30T09:38:19.9376068+05:30 [Information] Executing step 'Itterative Step for user {{$var:Usernamelocal}}' for row 1/2 in table 'UserRolesTable'.
2025-06-30T09:38:19.9378074+05:30 [Information] Default-mapped column 'Name' to variable 'Name' with value 'Admin'
2025-06-30T09:38:19.9378964+05:30 [Information] Resolved path 'row.Name' in step-specific context.
2025-06-30T09:38:19.9379502+05:30 [Information] Resolved JPath '$step:row.Name' to 'Admin'
2025-06-30T09:38:19.9379951+05:30 [Information] Resolved template pattern '{{$step:row.Name}}' to 'Admin' in '{{$step:row.Name}}'
2025-06-30T09:38:19.9380400+05:30 [Information] Resolved template '{{$step:row.Name}}' to 'Admin'
2025-06-30T09:38:19.9381181+05:30 [Information] Mapped column '{{$step:row.Name}}' to variable 'Role' with value 'Admin'
2025-06-30T09:38:19.9381688+05:30 [Information] Processing $var: path 'Usernamelocal'
2025-06-30T09:38:19.9382149+05:30 [Information] Checking if 'Usernamelocal' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:19.9382605+05:30 [Information] Resolving path 'Usernamelocal' using global variables.
2025-06-30T09:38:19.9383069+05:30 [Information] Resolved JPath '$var:Usernamelocal' to 'John'
2025-06-30T09:38:19.9383493+05:30 [Information] Resolved template pattern '{{$var:Usernamelocal}}' to 'John' in 'Running for {{$var:Usernamelocal}} and role {{$step:row.Name}}'
2025-06-30T09:38:19.9384267+05:30 [Information] Resolved path 'row.Name' in step-specific context.
2025-06-30T09:38:19.9385244+05:30 [Information] Resolved JPath '$step:row.Name' to 'Admin'
2025-06-30T09:38:19.9385950+05:30 [Information] Resolved template pattern '{{$step:row.Name}}' to 'Admin' in 'Running for {{$var:Usernamelocal}} and role {{$step:row.Name}}'
2025-06-30T09:38:19.9386605+05:30 [Information] Resolved template 'Running for {{$var:Usernamelocal}} and role {{$step:row.Name}}' to 'Running for John and role Admin'
2025-06-30T09:38:19.9388297+05:30 [Information] Step Iteration 1/2: Running for John and role Admin
2025-06-30T09:38:19.9391047+05:30 [Information] Executing step 'Running for John and role Admin' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "d4f4592c-0bf8-4dd4-bffa-5a2abcbfd7c2",
        "tables": {},
        "variables": {},
        "name": "Step to get token for Tom",
        "description": "Step to get token for Tom",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "Tom",
            "email": "<EMAIL>",
            "org": "Org1"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:08:24 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"Tom\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"org\": \"Org1\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "74",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620db7-7c25628b68f46e8535ae74ee"
            },
            "json": {
              "email": "<EMAIL>",
              "org": "Org1",
              "username": "Tom"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 1185,
          "startTime": "2025-06-30T04:08:17.4685427Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      },
      {
        "id": "1fbfd9d3-0e6e-413b-90d3-b6d4e5dc974c",
        "tables": {},
        "variables": {
          "Name": "User",
          "Role": "User"
        },
        "name": "Itterative Step for user Tom",
        "description": "Itterative Step",
        "context": {
          "repeatForTable": "UserRolesTable",
          "row": {
            "Name": "User"
          },
          "rowIndex": 1
        },
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "Tom",
            "role": "User"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:08:25 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"Tom\",\r\n  \"role\": \"User\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "44",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620db9-54a60e8f5ff6f7c729c6be0a"
            },
            "json": {
              "role": "User",
              "username": "Tom"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 256,
          "startTime": "2025-06-30T04:08:18.6576326Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      },
      {
        "id": "854bc276-7174-4de4-98c5-6d4275d61f85",
        "tables": {},
        "variables": {},
        "name": "Step to get token for John",
        "description": "Step to get token for Tom",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "John",
            "email": "<EMAIL>",
            "org": "Org2"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:08:25 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"John\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"org\": \"Org2\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "76",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620db9-3ad38d4c2cbde6af444a2b77"
            },
            "json": {
              "email": "<EMAIL>",
              "org": "Org2",
              "username": "John"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 248,
          "startTime": "2025-06-30T04:08:19.6861577Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      },
      {
        "id": "88b229c7-c75e-41ae-96b9-5e431b831e2b",
        "tables": {},
        "variables": {
          "Name": "Admin",
          "Role": "Admin"
        },
        "name": "Itterative Step for user John",
        "description": "Itterative Step",
        "context": {
          "repeatForTable": "UserRolesTable",
          "row": {
            "Name": "Admin"
          },
          "rowIndex": 0
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaseUrl": "https://httpbin.org",
      "User": {
        "Username": "John",
        "Email": "<EMAIL>"
      },
      "OrgName": "Org2",
      "Usernamelocal": "John",
      "Emaillocal": "<EMAIL>"
    },
    "tables": {
      "UserTable": [
        {
          "User": {
            "Username": "Tom",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org1",
          "NewUsername": "Tom"
        },
        {
          "User": {
            "Username": "John",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org2",
          "NewUsername": "John"
        }
      ],
      "UserRolesTable": [
        {
          "Name": "Admin"
        },
        {
          "Name": "User"
        }
      ]
    },
    "context": {
      "repeatForTable": "UserTable",
      "row": {
        "User": {
          "Username": "John",
          "Email": "<EMAIL>"
        },
        "OrgName": "Org2",
        "NewUsername": "John"
      },
      "rowIndex": 1
    }
  }
}
2025-06-30T09:38:19.9392123+05:30 [Information] Processing $var: path 'BaseUrl'
2025-06-30T09:38:19.9392641+05:30 [Information] Checking if 'BaseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:19.9393064+05:30 [Information] Resolving path 'BaseUrl' using global variables.
2025-06-30T09:38:19.9393459+05:30 [Information] Resolved JPath '$var:BaseUrl' to 'https://httpbin.org'
2025-06-30T09:38:19.9393830+05:30 [Information] Resolved template pattern '{{$var:BaseUrl}}' to 'https://httpbin.org' in '{{$var:BaseUrl}}/post'
2025-06-30T09:38:19.9394204+05:30 [Information] Resolved template '{{$var:BaseUrl}}/post' to 'https://httpbin.org/post'
2025-06-30T09:38:19.9394773+05:30 [Information] Resolving path 'row.NewUsername' using test context.
2025-06-30T09:38:19.9395262+05:30 [Information] Resolved JPath '$test:row.NewUsername' to 'John'
2025-06-30T09:38:19.9395633+05:30 [Information] Resolved template pattern '{{$test:row.NewUsername}}' to 'John' in '{
  "username": "{{$test:row.NewUsername}}",
  "role": "{{$var:Role}}"
}'
2025-06-30T09:38:19.9396018+05:30 [Information] Processing $var: path 'Role'
2025-06-30T09:38:19.9396391+05:30 [Information] Checking if 'Role' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:19.9396792+05:30 [Information] Resolved path 'Role' in step-specific variables.
2025-06-30T09:38:19.9397220+05:30 [Information] Resolved JPath '$var:Role' to 'Admin'
2025-06-30T09:38:19.9397592+05:30 [Information] Resolved template pattern '{{$var:Role}}' to 'Admin' in '{
  "username": "{{$test:row.NewUsername}}",
  "role": "{{$var:Role}}"
}'
2025-06-30T09:38:19.9397966+05:30 [Information] Resolved template '{
  "username": "{{$test:row.NewUsername}}",
  "role": "{{$var:Role}}"
}' to '{
  "username": "John",
  "role": "Admin"
}'
2025-06-30T09:38:19.9398610+05:30 [Information] Set Value to Step '88b229c7-c75e-41ae-96b9-5e431b831e2b' input:
{
  "Method": "POST",
  "RequestUri": "https://httpbin.org/post",
  "Headers": {
    "Content-Type": "application/json"
  },
  "Content": {
    "username": "John",
    "role": "Admin"
  }
}
2025-06-30T09:38:20.2646214+05:30 [Information] Set Value to Step '88b229c7-c75e-41ae-96b9-5e431b831e2b' output:
{
  "statusCode": 200,
  "reasonPhrase": "OK",
  "headers": [
    {
      "Key": "Date",
      "Value": [
        "Mon, 30 Jun 2025 04:08:25 GMT"
      ]
    },
    {
      "Key": "Connection",
      "Value": [
        "keep-alive"
      ]
    },
    {
      "Key": "Server",
      "Value": [
        "gunicorn/19.9.0"
      ]
    },
    {
      "Key": "Access-Control-Allow-Origin",
      "Value": [
        "*"
      ]
    },
    {
      "Key": "Access-Control-Allow-Credentials",
      "Value": [
        "true"
      ]
    }
  ],
  "content": {
    "args": {},
    "data": "{\r\n  \"username\": \"John\",\r\n  \"role\": \"Admin\"\r\n}",
    "files": {},
    "form": {},
    "headers": {
      "Content-Length": "46",
      "Content-Type": "application/json; charset=utf-8",
      "Host": "httpbin.org",
      "X-Amzn-Trace-Id": "Root=1-68620db9-10b4b00317ca6620505b8cdc"
    },
    "json": {
      "role": "Admin",
      "username": "John"
    },
    "origin": "**************",
    "url": "https://httpbin.org/post"
  }
}
2025-06-30T09:38:20.2653584+05:30 [Information] Set Value to Step '88b229c7-c75e-41ae-96b9-5e431b831e2b' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:08:19.9375813Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:38:20.2658034+05:30 [Information] Run Asserter : {"Expression1":{"Value":"200","ErrorMesage":null},"Expression2":{"JPath":"output.statusCode","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:38:20.2661214+05:30 [Information] Resolving path 'output.statusCode' using step input/output.
2025-06-30T09:38:20.2665052+05:30 [Information] Resolved JPath 'output.statusCode' to '200'
2025-06-30T09:38:20.2668246+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$test:row.NewUsername}}","ErrorMesage":null},"Expression2":{"JPath":"output.content.json.username","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:38:20.2670608+05:30 [Information] Resolving path 'row.NewUsername' using test context.
2025-06-30T09:38:20.2672838+05:30 [Information] Resolved JPath '$test:row.NewUsername' to 'John'
2025-06-30T09:38:20.2674881+05:30 [Information] Resolved template pattern '{{$test:row.NewUsername}}' to 'John' in '{{$test:row.NewUsername}}'
2025-06-30T09:38:20.2677340+05:30 [Information] Resolved template '{{$test:row.NewUsername}}' to 'John'
2025-06-30T09:38:20.2681053+05:30 [Information] Resolving path 'output.content.json.username' using step input/output.
2025-06-30T09:38:20.2683814+05:30 [Information] Resolved JPath 'output.content.json.username' to 'John'
2025-06-30T09:38:20.2686276+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$step:row.Name}}","ErrorMesage":null},"Expression2":{"JPath":"output.content.json.role","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:38:20.2688584+05:30 [Information] Resolved path 'row.Name' in step-specific context.
2025-06-30T09:38:20.2690623+05:30 [Information] Resolved JPath '$step:row.Name' to 'Admin'
2025-06-30T09:38:20.2692701+05:30 [Information] Resolved template pattern '{{$step:row.Name}}' to 'Admin' in '{{$step:row.Name}}'
2025-06-30T09:38:20.2694775+05:30 [Information] Resolved template '{{$step:row.Name}}' to 'Admin'
2025-06-30T09:38:20.2697036+05:30 [Information] Resolving path 'output.content.json.role' using step input/output.
2025-06-30T09:38:20.2697462+05:30 [Information] Resolved JPath 'output.content.json.role' to 'Admin'
2025-06-30T09:38:20.2697947+05:30 [Information] Set Value to Step '88b229c7-c75e-41ae-96b9-5e431b831e2b' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 329,
  "startTime": "2025-06-30T04:08:19.9375813Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:38:20.2698790+05:30 [Information] Step 'Running for John and role Admin' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "d4f4592c-0bf8-4dd4-bffa-5a2abcbfd7c2",
        "tables": {},
        "variables": {},
        "name": "Step to get token for Tom",
        "description": "Step to get token for Tom",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "Tom",
            "email": "<EMAIL>",
            "org": "Org1"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:08:24 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"Tom\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"org\": \"Org1\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "74",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620db7-7c25628b68f46e8535ae74ee"
            },
            "json": {
              "email": "<EMAIL>",
              "org": "Org1",
              "username": "Tom"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 1185,
          "startTime": "2025-06-30T04:08:17.4685427Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      },
      {
        "id": "1fbfd9d3-0e6e-413b-90d3-b6d4e5dc974c",
        "tables": {},
        "variables": {
          "Name": "User",
          "Role": "User"
        },
        "name": "Itterative Step for user Tom",
        "description": "Itterative Step",
        "context": {
          "repeatForTable": "UserRolesTable",
          "row": {
            "Name": "User"
          },
          "rowIndex": 1
        },
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "Tom",
            "role": "User"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:08:25 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"Tom\",\r\n  \"role\": \"User\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "44",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620db9-54a60e8f5ff6f7c729c6be0a"
            },
            "json": {
              "role": "User",
              "username": "Tom"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 256,
          "startTime": "2025-06-30T04:08:18.6576326Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      },
      {
        "id": "854bc276-7174-4de4-98c5-6d4275d61f85",
        "tables": {},
        "variables": {},
        "name": "Step to get token for John",
        "description": "Step to get token for Tom",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "John",
            "email": "<EMAIL>",
            "org": "Org2"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:08:25 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"John\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"org\": \"Org2\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "76",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620db9-3ad38d4c2cbde6af444a2b77"
            },
            "json": {
              "email": "<EMAIL>",
              "org": "Org2",
              "username": "John"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 248,
          "startTime": "2025-06-30T04:08:19.6861577Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      },
      {
        "id": "88b229c7-c75e-41ae-96b9-5e431b831e2b",
        "tables": {},
        "variables": {
          "Name": "Admin",
          "Role": "Admin"
        },
        "name": "Itterative Step for user John",
        "description": "Itterative Step",
        "context": {
          "repeatForTable": "UserRolesTable",
          "row": {
            "Name": "Admin"
          },
          "rowIndex": 0
        },
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "John",
            "role": "Admin"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:08:25 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"John\",\r\n  \"role\": \"Admin\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "46",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620db9-10b4b00317ca6620505b8cdc"
            },
            "json": {
              "role": "Admin",
              "username": "John"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 329,
          "startTime": "2025-06-30T04:08:19.9375813Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaseUrl": "https://httpbin.org",
      "User": {
        "Username": "John",
        "Email": "<EMAIL>"
      },
      "OrgName": "Org2",
      "Usernamelocal": "John",
      "Emaillocal": "<EMAIL>"
    },
    "tables": {
      "UserTable": [
        {
          "User": {
            "Username": "Tom",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org1",
          "NewUsername": "Tom"
        },
        {
          "User": {
            "Username": "John",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org2",
          "NewUsername": "John"
        }
      ],
      "UserRolesTable": [
        {
          "Name": "Admin"
        },
        {
          "Name": "User"
        }
      ]
    },
    "context": {
      "repeatForTable": "UserTable",
      "row": {
        "User": {
          "Username": "John",
          "Email": "<EMAIL>"
        },
        "OrgName": "Org2",
        "NewUsername": "John"
      },
      "rowIndex": 1
    }
  }
}
2025-06-30T09:38:20.2699541+05:30 [Information] Executing step 'Itterative Step for user {{$var:Usernamelocal}}' for row 2/2 in table 'UserRolesTable'.
2025-06-30T09:38:20.2700151+05:30 [Information] Default-mapped column 'Name' to variable 'Name' with value 'User'
2025-06-30T09:38:20.2700635+05:30 [Information] Resolved path 'row.Name' in step-specific context.
2025-06-30T09:38:20.2701050+05:30 [Information] Resolved JPath '$step:row.Name' to 'User'
2025-06-30T09:38:20.2701401+05:30 [Information] Resolved template pattern '{{$step:row.Name}}' to 'User' in '{{$step:row.Name}}'
2025-06-30T09:38:20.2701844+05:30 [Information] Resolved template '{{$step:row.Name}}' to 'User'
2025-06-30T09:38:20.2702484+05:30 [Information] Mapped column '{{$step:row.Name}}' to variable 'Role' with value 'User'
2025-06-30T09:38:20.2702893+05:30 [Information] Processing $var: path 'Usernamelocal'
2025-06-30T09:38:20.2703283+05:30 [Information] Checking if 'Usernamelocal' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:20.2703655+05:30 [Information] Resolving path 'Usernamelocal' using global variables.
2025-06-30T09:38:20.2704014+05:30 [Information] Resolved JPath '$var:Usernamelocal' to 'John'
2025-06-30T09:38:20.2704363+05:30 [Information] Resolved template pattern '{{$var:Usernamelocal}}' to 'John' in 'Running for {{$var:Usernamelocal}} and role {{$step:row.Name}}'
2025-06-30T09:38:20.2705284+05:30 [Information] Resolved path 'row.Name' in step-specific context.
2025-06-30T09:38:20.2707232+05:30 [Information] Resolved JPath '$step:row.Name' to 'User'
2025-06-30T09:38:20.2708028+05:30 [Information] Resolved template pattern '{{$step:row.Name}}' to 'User' in 'Running for {{$var:Usernamelocal}} and role {{$step:row.Name}}'
2025-06-30T09:38:20.2708691+05:30 [Information] Resolved template 'Running for {{$var:Usernamelocal}} and role {{$step:row.Name}}' to 'Running for John and role User'
2025-06-30T09:38:20.2712791+05:30 [Information] Step Iteration 2/2: Running for John and role User
2025-06-30T09:38:20.2714789+05:30 [Information] Executing step 'Running for John and role User' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "d4f4592c-0bf8-4dd4-bffa-5a2abcbfd7c2",
        "tables": {},
        "variables": {},
        "name": "Step to get token for Tom",
        "description": "Step to get token for Tom",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "Tom",
            "email": "<EMAIL>",
            "org": "Org1"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:08:24 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"Tom\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"org\": \"Org1\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "74",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620db7-7c25628b68f46e8535ae74ee"
            },
            "json": {
              "email": "<EMAIL>",
              "org": "Org1",
              "username": "Tom"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 1185,
          "startTime": "2025-06-30T04:08:17.4685427Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      },
      {
        "id": "1fbfd9d3-0e6e-413b-90d3-b6d4e5dc974c",
        "tables": {},
        "variables": {
          "Name": "User",
          "Role": "User"
        },
        "name": "Itterative Step for user Tom",
        "description": "Itterative Step",
        "context": {
          "repeatForTable": "UserRolesTable",
          "row": {
            "Name": "User"
          },
          "rowIndex": 1
        },
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "Tom",
            "role": "User"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:08:25 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"Tom\",\r\n  \"role\": \"User\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "44",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620db9-54a60e8f5ff6f7c729c6be0a"
            },
            "json": {
              "role": "User",
              "username": "Tom"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 256,
          "startTime": "2025-06-30T04:08:18.6576326Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      },
      {
        "id": "854bc276-7174-4de4-98c5-6d4275d61f85",
        "tables": {},
        "variables": {},
        "name": "Step to get token for John",
        "description": "Step to get token for Tom",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "John",
            "email": "<EMAIL>",
            "org": "Org2"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:08:25 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"John\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"org\": \"Org2\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "76",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620db9-3ad38d4c2cbde6af444a2b77"
            },
            "json": {
              "email": "<EMAIL>",
              "org": "Org2",
              "username": "John"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 248,
          "startTime": "2025-06-30T04:08:19.6861577Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      },
      {
        "id": "88b229c7-c75e-41ae-96b9-5e431b831e2b",
        "tables": {},
        "variables": {
          "Name": "User",
          "Role": "User"
        },
        "name": "Itterative Step for user John",
        "description": "Itterative Step",
        "context": {
          "repeatForTable": "UserRolesTable",
          "row": {
            "Name": "User"
          },
          "rowIndex": 1
        },
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "John",
            "role": "Admin"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:08:25 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"John\",\r\n  \"role\": \"Admin\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "46",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620db9-10b4b00317ca6620505b8cdc"
            },
            "json": {
              "role": "Admin",
              "username": "John"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 329,
          "startTime": "2025-06-30T04:08:19.9375813Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaseUrl": "https://httpbin.org",
      "User": {
        "Username": "John",
        "Email": "<EMAIL>"
      },
      "OrgName": "Org2",
      "Usernamelocal": "John",
      "Emaillocal": "<EMAIL>"
    },
    "tables": {
      "UserTable": [
        {
          "User": {
            "Username": "Tom",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org1",
          "NewUsername": "Tom"
        },
        {
          "User": {
            "Username": "John",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org2",
          "NewUsername": "John"
        }
      ],
      "UserRolesTable": [
        {
          "Name": "Admin"
        },
        {
          "Name": "User"
        }
      ]
    },
    "context": {
      "repeatForTable": "UserTable",
      "row": {
        "User": {
          "Username": "John",
          "Email": "<EMAIL>"
        },
        "OrgName": "Org2",
        "NewUsername": "John"
      },
      "rowIndex": 1
    }
  }
}
2025-06-30T09:38:20.2715984+05:30 [Information] Processing $var: path 'BaseUrl'
2025-06-30T09:38:20.2716519+05:30 [Information] Checking if 'BaseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:20.2717695+05:30 [Information] Resolving path 'BaseUrl' using global variables.
2025-06-30T09:38:20.2718093+05:30 [Information] Resolved JPath '$var:BaseUrl' to 'https://httpbin.org'
2025-06-30T09:38:20.2718569+05:30 [Information] Resolved template pattern '{{$var:BaseUrl}}' to 'https://httpbin.org' in '{{$var:BaseUrl}}/post'
2025-06-30T09:38:20.2718943+05:30 [Information] Resolved template '{{$var:BaseUrl}}/post' to 'https://httpbin.org/post'
2025-06-30T09:38:20.2719659+05:30 [Information] Resolving path 'row.NewUsername' using test context.
2025-06-30T09:38:20.2720052+05:30 [Information] Resolved JPath '$test:row.NewUsername' to 'John'
2025-06-30T09:38:20.2720410+05:30 [Information] Resolved template pattern '{{$test:row.NewUsername}}' to 'John' in '{
  "username": "{{$test:row.NewUsername}}",
  "role": "{{$var:Role}}"
}'
2025-06-30T09:38:20.2720882+05:30 [Information] Processing $var: path 'Role'
2025-06-30T09:38:20.2721249+05:30 [Information] Checking if 'Role' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:20.2721873+05:30 [Information] Resolved path 'Role' in step-specific variables.
2025-06-30T09:38:20.2722265+05:30 [Information] Resolved JPath '$var:Role' to 'User'
2025-06-30T09:38:20.2722777+05:30 [Information] Resolved template pattern '{{$var:Role}}' to 'User' in '{
  "username": "{{$test:row.NewUsername}}",
  "role": "{{$var:Role}}"
}'
2025-06-30T09:38:20.2723290+05:30 [Information] Resolved template '{
  "username": "{{$test:row.NewUsername}}",
  "role": "{{$var:Role}}"
}' to '{
  "username": "John",
  "role": "User"
}'
2025-06-30T09:38:20.2723917+05:30 [Information] Set Value to Step '88b229c7-c75e-41ae-96b9-5e431b831e2b' input:
{
  "Method": "POST",
  "RequestUri": "https://httpbin.org/post",
  "Headers": {
    "Content-Type": "application/json"
  },
  "Content": {
    "username": "John",
    "role": "User"
  }
}
2025-06-30T09:38:20.9773928+05:30 [Information] Set Value to Step '88b229c7-c75e-41ae-96b9-5e431b831e2b' output:
{
  "statusCode": 200,
  "reasonPhrase": "OK",
  "headers": [
    {
      "Key": "Date",
      "Value": [
        "Mon, 30 Jun 2025 04:08:26 GMT"
      ]
    },
    {
      "Key": "Connection",
      "Value": [
        "keep-alive"
      ]
    },
    {
      "Key": "Server",
      "Value": [
        "gunicorn/19.9.0"
      ]
    },
    {
      "Key": "Access-Control-Allow-Origin",
      "Value": [
        "*"
      ]
    },
    {
      "Key": "Access-Control-Allow-Credentials",
      "Value": [
        "true"
      ]
    }
  ],
  "content": {
    "args": {},
    "data": "{\r\n  \"username\": \"John\",\r\n  \"role\": \"User\"\r\n}",
    "files": {},
    "form": {},
    "headers": {
      "Content-Length": "45",
      "Content-Type": "application/json; charset=utf-8",
      "Host": "httpbin.org",
      "X-Amzn-Trace-Id": "Root=1-68620db9-1a07c41f5da7f34d45d83c48"
    },
    "json": {
      "role": "User",
      "username": "John"
    },
    "origin": "**************",
    "url": "https://httpbin.org/post"
  }
}
2025-06-30T09:38:20.9776424+05:30 [Information] Set Value to Step '88b229c7-c75e-41ae-96b9-5e431b831e2b' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:08:19.9375813Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:38:20.9777379+05:30 [Information] Run Asserter : {"Expression1":{"Value":"200","ErrorMesage":null},"Expression2":{"JPath":"output.statusCode","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:38:20.9777977+05:30 [Information] Resolving path 'output.statusCode' using step input/output.
2025-06-30T09:38:20.9778502+05:30 [Information] Resolved JPath 'output.statusCode' to '200'
2025-06-30T09:38:20.9779121+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$test:row.NewUsername}}","ErrorMesage":null},"Expression2":{"JPath":"output.content.json.username","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:38:20.9779592+05:30 [Information] Resolving path 'row.NewUsername' using test context.
2025-06-30T09:38:20.9780007+05:30 [Information] Resolved JPath '$test:row.NewUsername' to 'John'
2025-06-30T09:38:20.9780474+05:30 [Information] Resolved template pattern '{{$test:row.NewUsername}}' to 'John' in '{{$test:row.NewUsername}}'
2025-06-30T09:38:20.9780943+05:30 [Information] Resolved template '{{$test:row.NewUsername}}' to 'John'
2025-06-30T09:38:20.9781917+05:30 [Information] Resolving path 'output.content.json.username' using step input/output.
2025-06-30T09:38:20.9782410+05:30 [Information] Resolved JPath 'output.content.json.username' to 'John'
2025-06-30T09:38:20.9782955+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$step:row.Name}}","ErrorMesage":null},"Expression2":{"JPath":"output.content.json.role","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:38:20.9783516+05:30 [Information] Resolved path 'row.Name' in step-specific context.
2025-06-30T09:38:20.9783911+05:30 [Information] Resolved JPath '$step:row.Name' to 'User'
2025-06-30T09:38:20.9784273+05:30 [Information] Resolved template pattern '{{$step:row.Name}}' to 'User' in '{{$step:row.Name}}'
2025-06-30T09:38:20.9784918+05:30 [Information] Resolved template '{{$step:row.Name}}' to 'User'
2025-06-30T09:38:20.9785606+05:30 [Information] Resolving path 'output.content.json.role' using step input/output.
2025-06-30T09:38:20.9786029+05:30 [Information] Resolved JPath 'output.content.json.role' to 'User'
2025-06-30T09:38:20.9786597+05:30 [Information] Set Value to Step '88b229c7-c75e-41ae-96b9-5e431b831e2b' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 706,
  "startTime": "2025-06-30T04:08:19.9375813Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:38:20.9787447+05:30 [Information] Step 'Running for John and role User' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "d4f4592c-0bf8-4dd4-bffa-5a2abcbfd7c2",
        "tables": {},
        "variables": {},
        "name": "Step to get token for Tom",
        "description": "Step to get token for Tom",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "Tom",
            "email": "<EMAIL>",
            "org": "Org1"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:08:24 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"Tom\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"org\": \"Org1\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "74",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620db7-7c25628b68f46e8535ae74ee"
            },
            "json": {
              "email": "<EMAIL>",
              "org": "Org1",
              "username": "Tom"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 1185,
          "startTime": "2025-06-30T04:08:17.4685427Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      },
      {
        "id": "1fbfd9d3-0e6e-413b-90d3-b6d4e5dc974c",
        "tables": {},
        "variables": {
          "Name": "User",
          "Role": "User"
        },
        "name": "Itterative Step for user Tom",
        "description": "Itterative Step",
        "context": {
          "repeatForTable": "UserRolesTable",
          "row": {
            "Name": "User"
          },
          "rowIndex": 1
        },
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "Tom",
            "role": "User"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:08:25 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"Tom\",\r\n  \"role\": \"User\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "44",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620db9-54a60e8f5ff6f7c729c6be0a"
            },
            "json": {
              "role": "User",
              "username": "Tom"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 256,
          "startTime": "2025-06-30T04:08:18.6576326Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      },
      {
        "id": "854bc276-7174-4de4-98c5-6d4275d61f85",
        "tables": {},
        "variables": {},
        "name": "Step to get token for John",
        "description": "Step to get token for Tom",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "John",
            "email": "<EMAIL>",
            "org": "Org2"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:08:25 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"John\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"org\": \"Org2\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "76",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620db9-3ad38d4c2cbde6af444a2b77"
            },
            "json": {
              "email": "<EMAIL>",
              "org": "Org2",
              "username": "John"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 248,
          "startTime": "2025-06-30T04:08:19.6861577Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      },
      {
        "id": "88b229c7-c75e-41ae-96b9-5e431b831e2b",
        "tables": {},
        "variables": {
          "Name": "User",
          "Role": "User"
        },
        "name": "Itterative Step for user John",
        "description": "Itterative Step",
        "context": {
          "repeatForTable": "UserRolesTable",
          "row": {
            "Name": "User"
          },
          "rowIndex": 1
        },
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "John",
            "role": "User"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:08:26 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"John\",\r\n  \"role\": \"User\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "45",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620db9-1a07c41f5da7f34d45d83c48"
            },
            "json": {
              "role": "User",
              "username": "John"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 706,
          "startTime": "2025-06-30T04:08:19.9375813Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaseUrl": "https://httpbin.org",
      "User": {
        "Username": "John",
        "Email": "<EMAIL>"
      },
      "OrgName": "Org2",
      "Usernamelocal": "John",
      "Emaillocal": "<EMAIL>"
    },
    "tables": {
      "UserTable": [
        {
          "User": {
            "Username": "Tom",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org1",
          "NewUsername": "Tom"
        },
        {
          "User": {
            "Username": "John",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org2",
          "NewUsername": "John"
        }
      ],
      "UserRolesTable": [
        {
          "Name": "Admin"
        },
        {
          "Name": "User"
        }
      ]
    },
    "context": {
      "repeatForTable": "UserTable",
      "row": {
        "User": {
          "Username": "John",
          "Email": "<EMAIL>"
        },
        "OrgName": "Org2",
        "NewUsername": "John"
      },
      "rowIndex": 1
    }
  }
}
2025-06-30T09:38:20.9788188+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:38:20.9788633+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:38:20.9789003+05:30 [Information] No performance data found for step 'Itterative Step for user {{$var:Usernamelocal}}' (http)
2025-06-30T09:38:20.9789551+05:30 [Information] [ OK       ]
2025-06-30T09:38:20.9792051+05:30 [Information] [----------] 2 steps from Itterative Test and Step (3512 ms total)
