﻿
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup Label="Globals">
    <ProjectGuid>{147DF982-596E-3A6E-9874-E3DDEEBB09E5}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <Configurations>Debug;Release</Configurations>
    <EnableDefaultItems>false</EnableDefaultItems>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
    <ManagedAssembly>true</ManagedAssembly>
    <TargetFramework>net8.0</TargetFramework>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <OutputType>Library</OutputType>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)' == 'Debug'">
    <OutputPath>D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\build\Debug\</OutputPath>
    <AssemblyName>testlib_unit_tests</AssemblyName>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <DebugType>full</DebugType>
    <DefineConstants>TRACE;DEBUG;_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <LangVersion>latest</LangVersion>
    <Optimize>false</Optimize>
    <Platform>x64</Platform>
    <WarningLevel>3</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)' == 'Release'">
    <OutputPath>D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\build\Release\</OutputPath>
    <AssemblyName>testlib_unit_tests</AssemblyName>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <DebugType>none</DebugType>
    <DefineConstants>TRACE;_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR</DefineConstants>
    <ErrorReport>queue</ErrorReport>
    <LangVersion>latest</LangVersion>
    <Optimize>true</Optimize>
    <Platform>x64</Platform>
    <WarningLevel>1</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <None Include="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\CMakeLists.txt">
      <Link>CMakeLists.txt</Link>
    </None>
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCSharpInformation.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeGenericSystem.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\FindGit.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageMessage.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\cmake\Globals.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\cmake\VsPackageReferences.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\build\CMakeFiles\4.0.0-rc4\CMakeCSharpCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\build\CMakeFiles\4.0.0-rc4\CMakeSystem.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\build\CMakeFiles\cmake.verify_globs" />
  </ItemGroup>
  <Target Condition="'$(Configuration)' == 'Debug'"
    Name="CustomCommand_Debug_f2df39e3ff209a9cc1aac6b9a90fe0e7"
    Inputs="D:/kibisoft/workarea/git/interop/libs/test/service/testlib/tests/unit/CMakeLists.txt;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCSharpInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindGit.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake;D:\kibisoft\workarea\git\interop\cmake\Globals.cmake;D:\kibisoft\workarea\git\interop\cmake\VsPackageReferences.cmake;D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\build\CMakeFiles\4.0.0-rc4\CMakeCSharpCompiler.cmake;D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\build\CMakeFiles\4.0.0-rc4\CMakeSystem.cmake;D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\build\CMakeFiles\cmake.verify_globs"
    Outputs="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\build\CMakeFiles\generate.stamp"
    BeforeTargets="CoreCompile"
    DependsOnTargets="PrepareForBuild">
    <Exec Command="echo Building Custom Rule D:/kibisoft/workarea/git/interop/libs/test/service/testlib/tests/unit/CMakeLists.txt" />
    <Exec Command="setlocal&#10;&quot;C:\Program Files\CMake\bin\cmake.exe&quot; -SD:/kibisoft/workarea/git/interop/libs/test/service/testlib/tests/unit -BD:/kibisoft/workarea/git/interop/libs/test/service/testlib/tests/unit/build --check-stamp-file D:/kibisoft/workarea/git/interop/libs/test/service/testlib/tests/unit/build/CMakeFiles/generate.stamp&#10;if %errorlevel% neq 0 goto :cmEnd&#10;:cmEnd&#10;endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone&#10;:cmErrorLevel&#10;exit /b %1&#10;:cmDone&#10;if %errorlevel% neq 0 goto :VCEnd&#10;:VCEnd" />
  </Target>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCSharpInformation.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeGenericSystem.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\FindGit.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageMessage.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\cmake\Globals.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\cmake\VsPackageReferences.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\build\CMakeFiles\4.0.0-rc4\CMakeCSharpCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\build\CMakeFiles\4.0.0-rc4\CMakeSystem.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\build\CMakeFiles\cmake.verify_globs" />
  </ItemGroup>
  <Target Condition="'$(Configuration)' == 'Release'"
    Name="CustomCommand_Release_f2df39e3ff209a9cc1aac6b9a90fe0e7"
    Inputs="D:/kibisoft/workarea/git/interop/libs/test/service/testlib/tests/unit/CMakeLists.txt;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCSharpInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindGit.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake;D:\kibisoft\workarea\git\interop\cmake\Globals.cmake;D:\kibisoft\workarea\git\interop\cmake\VsPackageReferences.cmake;D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\build\CMakeFiles\4.0.0-rc4\CMakeCSharpCompiler.cmake;D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\build\CMakeFiles\4.0.0-rc4\CMakeSystem.cmake;D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\build\CMakeFiles\cmake.verify_globs"
    Outputs="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\build\CMakeFiles\generate.stamp"
    BeforeTargets="CoreCompile"
    DependsOnTargets="PrepareForBuild">
    <Exec Command="echo Building Custom Rule D:/kibisoft/workarea/git/interop/libs/test/service/testlib/tests/unit/CMakeLists.txt" />
    <Exec Command="setlocal&#10;&quot;C:\Program Files\CMake\bin\cmake.exe&quot; -SD:/kibisoft/workarea/git/interop/libs/test/service/testlib/tests/unit -BD:/kibisoft/workarea/git/interop/libs/test/service/testlib/tests/unit/build --check-stamp-file D:/kibisoft/workarea/git/interop/libs/test/service/testlib/tests/unit/build/CMakeFiles/generate.stamp&#10;if %errorlevel% neq 0 goto :cmEnd&#10;:cmEnd&#10;endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone&#10;:cmErrorLevel&#10;exit /b %1&#10;:cmDone&#10;if %errorlevel% neq 0 goto :VCEnd&#10;:VCEnd" />
  </Target>
  <ItemGroup>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\src\AsserterBasedRetryConditionTests.cs">
      <Link>src\AsserterBasedRetryConditionTests.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\src\BaseTestStepExecutorTests.cs">
      <Link>src\BaseTestStepExecutorTests.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\src\CmdTestStepExecutorTests.cs">
      <Link>src\CmdTestStepExecutorTests.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\src\CmdTestStepFactoryTests.cs">
      <Link>src\CmdTestStepFactoryTests.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\src\ConstantExpressionTests.cs">
      <Link>src\ConstantExpressionTests.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\src\ContainExpressionTests.cs">
      <Link>src\ContainExpressionTests.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\src\CsScriptExpressionTests.cs">
      <Link>src\CsScriptExpressionTests.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\src\EqualExpressionTests.cs">
      <Link>src\EqualExpressionTests.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\src\ExpressionErrorMessageTests.cs">
      <Link>src\ExpressionErrorMessageTests.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\src\HttpTestStepExecutorTests.cs">
      <Link>src\HttpTestStepExecutorTests.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\src\HttpTestStepFactoryTests.cs">
      <Link>src\HttpTestStepFactoryTests.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\src\JTokenComparerContainsTests.cs">
      <Link>src\JTokenComparerContainsTests.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\src\JTokenComparerTests.cs">
      <Link>src\JTokenComparerTests.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\src\JsonSchemaExpressionTests.cs">
      <Link>src\JsonSchemaExpressionTests.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\src\MBusTestStepFactoryTests.cs">
      <Link>src\MBusTestStepFactoryTests.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\src\MathematicalExpressionTests.cs">
      <Link>src\MathematicalExpressionTests.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\src\NotContainExpressionTests.cs">
      <Link>src\NotContainExpressionTests.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\src\PerformanceDataAccessTests.cs">
      <Link>src\PerformanceDataAccessTests.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\src\PyScriptExpressionTests.cs">
      <Link>src\PyScriptExpressionTests.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\src\ResolveMethodTests.cs">
      <Link>src\ResolveMethodTests.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\src\RpcPerformanceDataTests.cs">
      <Link>src\RpcPerformanceDataTests.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\src\RpcTestStepFactoryTests.cs">
      <Link>src\RpcTestStepFactoryTests.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\src\RpcTestStepOutputTests.cs">
      <Link>src\RpcTestStepOutputTests.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\src\StartupStepTests.cs">
      <Link>src\StartupStepTests.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\src\TestContextResolveTemplateTests.cs">
      <Link>src\TestContextResolveTemplateTests.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\src\TestContextResolveTests.cs">
      <Link>src\TestContextResolveTests.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\src\TestContextTableTests.cs">
      <Link>src\TestContextTableTests.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\src\TestContextTests.cs">
      <Link>src\TestContextTests.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\src\TestContextVariableTests.cs">
      <Link>src\TestContextVariableTests.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\src\TestExecutionWithTableTests.cs">
      <Link>src\TestExecutionWithTableTests.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\src\TestFactoryTests.cs">
      <Link>src\TestFactoryTests.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\src\TestLibIntegrationTests.cs">
      <Link>src\TestLibIntegrationTests.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\src\TestResultWriterTests.cs">
      <Link>src\TestResultWriterTests.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\src\TestStepExecutionSettingsTests.cs">
      <Link>src\TestStepExecutionSettingsTests.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\src\TestStepFromYamlDataTests.cs">
      <Link>src\TestStepFromYamlDataTests.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\src\TestSuiteTests.cs">
      <Link>src\TestSuiteTests.cs</Link>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="NUnit" Version="3.14.0" />
    <PackageReference Include="NUnit3TestAdapter" Version="4.5.0" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
    <PackageReference Include="Moq" Version="4.20.70" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\build\ZERO_CHECK.vcxproj">
      <Project>{61469607-E4E8-314E-BE85-14884F74CA7F}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\build\testlib\testlib.csproj">
      <Project>{DBEFA130-DC42-3B19-9C88-AF7B519F8AE5}</Project>
      <Name>testlib</Name>
      <SkipGetTargetFrameworkProperties>true</SkipGetTargetFrameworkProperties>
    </ProjectReference>
  </ItemGroup>
</Project>