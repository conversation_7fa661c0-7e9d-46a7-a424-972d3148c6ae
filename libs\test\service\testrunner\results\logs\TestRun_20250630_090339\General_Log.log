2025-06-30T09:03:39.2300141+05:30 [Information] Global variables set to: baseUrl: https://httpbin.org, serverIp: 127.0.0.1, serverPort: 60606
2025-06-30T09:03:39.2464328+05:30 [Information] Loading tests from D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc with pattern *math-expressions-simple.yaml
2025-06-30T09:03:39.2508643+05:30 [Information] Loading test from D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc\math-expressions-simple.yaml
2025-06-30T09:03:39.2630529+05:30 [Information] Global variables set to: 
2025-06-30T09:03:39.2641473+05:30 [Information] [==========] Running 1 test from Yaml files
2025-06-30T09:03:39.2657491+05:30 [Information] [-------------------------------------    Startup     -------------------------------------]
2025-06-30T09:03:39.2863740+05:30 [Information] [----------] 1 step from Startup
2025-06-30T09:03:39.2890130+05:30 [Information] [ RUN      ] Startup > Initialize Database
2025-06-30T09:03:40.5528223+05:30 [Information] [ OK       ]
2025-06-30T09:03:40.5539709+05:30 [Information] [----------] 1 step from Startup (1266 ms total)
2025-06-30T09:03:40.5565455+05:30 [Information] Global variables set to: curDir: D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc, baseUrl: https://httpbin.org, serverIp: 127.0.0.1, serverPort: 60606, dbInitStatus: initialize_db
2025-06-30T09:03:40.5572286+05:30 [Information] [------------------------------------- Test Execution -------------------------------------]
2025-06-30T09:03:40.5586868+05:30 [Information] [----------] 6 steps from Simple Mathematical Expressions Demo
2025-06-30T09:03:40.5602677+05:30 [Information] [ RUN      ] Simple Mathematical Expressions Demo > Fast Baseline Operation
2025-06-30T09:03:40.6489645+05:30 [Information] [ OK       ]
2025-06-30T09:03:40.6494074+05:30 [Information] [ RUN      ] Simple Mathematical Expressions Demo > Regression Threshold Test
