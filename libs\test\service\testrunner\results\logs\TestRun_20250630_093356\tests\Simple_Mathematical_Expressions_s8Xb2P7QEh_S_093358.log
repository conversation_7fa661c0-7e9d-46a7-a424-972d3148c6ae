2025-06-30T09:33:58.9924459+05:30 [Information] [----------] 6 steps from Simple Mathematical Expressions Demo - Passing Tests
2025-06-30T09:33:58.9940088+05:30 [Information] Set Value to Step '557edee5-d889-465a-9ac8-44abe9b70c7d' name:
"Fast Baseline Operation"
2025-06-30T09:33:58.9941658+05:30 [Information] [ RUN      ] Simple Mathematical Expressions Demo - Passing Tests > Fast Baseline Operation
2025-06-30T09:33:58.9944722+05:30 [Information] Executing single instance of step 'Fast Baseline Operation'.
2025-06-30T09:33:58.9946362+05:30 [Information] Executing step 'Fast Baseline Operation' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "557edee5-d889-465a-9ac8-44abe9b70c7d",
        "tables": {},
        "variables": {},
        "name": "Fast Baseline Operation"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaselineThreshold": 100,
      "MaxRegressionPercent": 1000,
      "BufferTime": 500,
      "FastThreshold": 10000,
      "SlowThreshold": 20000,
      "MathBaseline": 1
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:33:58.9956831+05:30 [Information] Set Value to Step '557edee5-d889-465a-9ac8-44abe9b70c7d' input:
{
  "command": "echo",
  "arguments": [
    "Establishing baseline performance"
  ],
  "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
  "printOutput": false
}
2025-06-30T09:33:58.9958235+05:30 [Information] Delaying step execution for 50ms
2025-06-30T09:33:59.0804287+05:30 [Information] Set Value to Step '557edee5-d889-465a-9ac8-44abe9b70c7d' output:
{
  "stdout": "Establishing baseline performance",
  "stderr": "",
  "exitCode": 0
}
2025-06-30T09:33:59.0806412+05:30 [Information] Resolving path 'performance.executionTimeMs' using global context.
2025-06-30T09:33:59.0807416+05:30 [Information] Resolved JPath 'performance.executionTimeMs' to ''
2025-06-30T09:33:59.0808780+05:30 [Information] Set Value to Step '557edee5-d889-465a-9ac8-44abe9b70c7d' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:03:58.9944681Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "cmd"
}
2025-06-30T09:33:59.0816425+05:30 [Information] Run Asserter : {"Expression1":{"Value":"0","ErrorMesage":"Echo command failed"},"Expression2":{"JPath":"$curStep:$.output.exitCode","ErrorMesage":"Echo command failed"},"ErrorMesage":"Echo command failed"}
2025-06-30T09:33:59.0817885+05:30 [Information] Resolving path '$.output.exitCode' using $curStep:
2025-06-30T09:33:59.0818860+05:30 [Information] Resolved JPath '$curStep:$.output.exitCode' to '0'
2025-06-30T09:33:59.0829626+05:30 [Information] Run Asserter : {"_lessExpression":{"Expression1":{"Value":"0","ErrorMesage":"Performance data not available"},"Expression2":{"JPath":"$curStep:performance.executionTimeMs","ErrorMesage":"Performance data not available"},"ErrorMesage":null},"Expression1":{"Value":"0","ErrorMesage":"Performance data not available"},"Expression2":{"JPath":"$curStep:performance.executionTimeMs","ErrorMesage":"Performance data not available"},"ErrorMesage":"Performance data not available"}
2025-06-30T09:33:59.0830975+05:30 [Information] Resolving path 'performance.executionTimeMs' using $curStep:
2025-06-30T09:33:59.0832065+05:30 [Information] Resolved JPath '$curStep:performance.executionTimeMs' to '0'
2025-06-30T09:33:59.0835494+05:30 [Information] Run Asserter : {"_lessExpression":{"Expression1":{"Value":"0","ErrorMesage":"Performance timing should be non-negative"},"Expression2":{"JPath":"$curStep:performance.executionTimeMs","ErrorMesage":"Performance timing should be non-negative"},"ErrorMesage":null},"Expression1":{"Value":"0","ErrorMesage":"Performance timing should be non-negative"},"Expression2":{"JPath":"$curStep:performance.executionTimeMs","ErrorMesage":"Performance timing should be non-negative"},"ErrorMesage":"Performance timing should be non-negative"}
2025-06-30T09:33:59.0836622+05:30 [Information] Resolving path 'performance.executionTimeMs' using $curStep:
2025-06-30T09:33:59.0837498+05:30 [Information] Resolved JPath '$curStep:performance.executionTimeMs' to '0'
2025-06-30T09:33:59.0838325+05:30 [Information] Run Asserter : {"Expression1":{"JPath":"$curStep:performance.executionTimeMs","ErrorMesage":"Baseline too slow: {{$curStep:performance.executionTimeMs}}ms > {{$var:FastThreshold}}ms (calculated threshold)"},"Expression2":{"Value":"{{$var:FastThreshold}}","ErrorMesage":"Baseline too slow: {{$curStep:performance.executionTimeMs}}ms > {{$var:FastThreshold}}ms (calculated threshold)"},"ErrorMesage":"Baseline too slow: {{$curStep:performance.executionTimeMs}}ms > {{$var:FastThreshold}}ms (calculated threshold)"}
2025-06-30T09:33:59.0838998+05:30 [Information] Resolving path 'performance.executionTimeMs' using $curStep:
2025-06-30T09:33:59.0839567+05:30 [Information] Resolved JPath '$curStep:performance.executionTimeMs' to '0'
2025-06-30T09:33:59.0840229+05:30 [Information] Processing $var: path 'FastThreshold'
2025-06-30T09:33:59.0840802+05:30 [Information] Checking if 'FastThreshold' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:33:59.0841356+05:30 [Information] Resolving path 'FastThreshold' using global variables.
2025-06-30T09:33:59.0841875+05:30 [Information] Resolved JPath '$var:FastThreshold' to '10000'
2025-06-30T09:33:59.0842354+05:30 [Information] Resolved template pattern '{{$var:FastThreshold}}' to '10000' in '{{$var:FastThreshold}}'
2025-06-30T09:33:59.0842967+05:30 [Information] Resolved template '{{$var:FastThreshold}}' to '10000'
2025-06-30T09:33:59.0843798+05:30 [Information] Set Value to Step '557edee5-d889-465a-9ac8-44abe9b70c7d' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 88,
  "startTime": "2025-06-30T04:03:58.9944681Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "cmd"
}
2025-06-30T09:33:59.0844815+05:30 [Information] Step 'Fast Baseline Operation' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "557edee5-d889-465a-9ac8-44abe9b70c7d",
        "tables": {},
        "variables": {},
        "name": "Fast Baseline Operation",
        "input": {
          "command": "echo",
          "arguments": [
            "Establishing baseline performance"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Establishing baseline performance",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 88,
          "startTime": "2025-06-30T04:03:58.9944681Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaselineThreshold": 100,
      "MaxRegressionPercent": 1000,
      "BufferTime": 500,
      "FastThreshold": 10000,
      "SlowThreshold": 20000,
      "MathBaseline": 1
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:33:59.0845685+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:33:59.0846400+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:33:59.0846969+05:30 [Information] No performance data found for step 'Fast Baseline Operation' (cmd)
2025-06-30T09:33:59.0847450+05:30 [Information] [ OK       ]
2025-06-30T09:33:59.0850187+05:30 [Information] Set Value to Step '15e45821-794f-44b9-817b-c287f8440ab1' name:
"Regression Threshold Test"
2025-06-30T09:33:59.0851331+05:30 [Information] [ RUN      ] Simple Mathematical Expressions Demo - Passing Tests > Regression Threshold Test
2025-06-30T09:33:59.0853829+05:30 [Information] Executing single instance of step 'Regression Threshold Test'.
2025-06-30T09:33:59.0854991+05:30 [Information] Executing step 'Regression Threshold Test' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "557edee5-d889-465a-9ac8-44abe9b70c7d",
        "tables": {},
        "variables": {},
        "name": "Fast Baseline Operation",
        "input": {
          "command": "echo",
          "arguments": [
            "Establishing baseline performance"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Establishing baseline performance",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 88,
          "startTime": "2025-06-30T04:03:58.9944681Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "15e45821-794f-44b9-817b-c287f8440ab1",
        "tables": {},
        "variables": {},
        "name": "Regression Threshold Test"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaselineThreshold": 100,
      "MaxRegressionPercent": 1000,
      "BufferTime": 500,
      "FastThreshold": 10000,
      "SlowThreshold": 20000,
      "MathBaseline": 1
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:33:59.0856183+05:30 [Information] Set Value to Step '15e45821-794f-44b9-817b-c287f8440ab1' input:
{
  "command": "echo",
  "arguments": [
    "Testing mathematical expression: MathBaseline * MaxRegressionPercent / 100"
  ],
  "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
  "printOutput": false
}
2025-06-30T09:33:59.0856859+05:30 [Information] Delaying step execution for 200ms
2025-06-30T09:33:59.3129391+05:30 [Information] Set Value to Step '15e45821-794f-44b9-817b-c287f8440ab1' output:
{
  "stdout": "Testing mathematical expression: MathBaseline * MaxRegressionPercent / 100",
  "stderr": "",
  "exitCode": 0
}
2025-06-30T09:33:59.3131268+05:30 [Information] Set Value to Step '15e45821-794f-44b9-817b-c287f8440ab1' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:03:59.0853802Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "cmd"
}
2025-06-30T09:33:59.3132428+05:30 [Information] Run Asserter : {"Expression1":{"Value":"0","ErrorMesage":"Echo command failed"},"Expression2":{"JPath":"$curStep:$.output.exitCode","ErrorMesage":"Echo command failed"},"ErrorMesage":"Echo command failed"}
2025-06-30T09:33:59.3133592+05:30 [Information] Resolving path '$.output.exitCode' using $curStep:
2025-06-30T09:33:59.3134292+05:30 [Information] Resolved JPath '$curStep:$.output.exitCode' to '0'
2025-06-30T09:33:59.3135201+05:30 [Information] Run Asserter : {"Expression1":{"JPath":"$curStep:performance.executionTimeMs","ErrorMesage":"Performance regression detected: {{$curStep:performance.executionTimeMs}}ms vs allowed {{$var:MathBaseline * $var:MaxRegressionPercent / 100}}ms ({{$var:MathBaseline}}ms * {{$var:MaxRegressionPercent}}% / 100)"},"Expression2":{"Value":"{{$var:MathBaseline * $var:MaxRegressionPercent / 100}}","ErrorMesage":"Performance regression detected: {{$curStep:performance.executionTimeMs}}ms vs allowed {{$var:MathBaseline * $var:MaxRegressionPercent / 100}}ms ({{$var:MathBaseline}}ms * {{$var:MaxRegressionPercent}}% / 100)"},"ErrorMesage":"Performance regression detected: {{$curStep:performance.executionTimeMs}}ms vs allowed {{$var:MathBaseline * $var:MaxRegressionPercent / 100}}ms ({{$var:MathBaseline}}ms * {{$var:MaxRegressionPercent}}% / 100)"}
2025-06-30T09:33:59.3136019+05:30 [Information] Resolving path 'performance.executionTimeMs' using $curStep:
2025-06-30T09:33:59.3136647+05:30 [Information] Resolved JPath '$curStep:performance.executionTimeMs' to '0'
2025-06-30T09:33:59.3137360+05:30 [Information] Processing $var: path 'MathBaseline * $var:MaxRegressionPercent / 100'
2025-06-30T09:33:59.3137952+05:30 [Information] Checking if 'MathBaseline * $var:MaxRegressionPercent / 100' is mathematical expression: hasArithmetic=True, hasArray=False, hasProperty=False, result=True
2025-06-30T09:33:59.3138377+05:30 [Information] Detected mathematical expression in $var: 'MathBaseline * $var:MaxRegressionPercent / 100'
2025-06-30T09:33:59.3141124+05:30 [Information] Evaluating mathematical expression: 'MathBaseline * $var:MaxRegressionPercent / 100'
2025-06-30T09:33:59.3143417+05:30 [Information] Resolving variables in expression: 'MathBaseline * $var:MaxRegressionPercent / 100'
2025-06-30T09:33:59.3147402+05:30 [Information] Resolved variable 'MaxRegressionPercent' from global variables: 1000
2025-06-30T09:33:59.3149780+05:30 [Information] Resolved variable 'MathBaseline' from global variables: 1
2025-06-30T09:33:59.3150915+05:30 [Information] Expression after variable resolution: '1 * 1000 / 100'
2025-06-30T09:33:59.3151490+05:30 [Information] Expression after variable resolution: '1 * 1000 / 100'
2025-06-30T09:33:59.3181625+05:30 [Information] Mathematical expression 'MathBaseline * $var:MaxRegressionPercent / 100' evaluated to: 10
2025-06-30T09:33:59.3182849+05:30 [Information] Mathematical expression result: '10'
2025-06-30T09:33:59.3183671+05:30 [Information] Resolved JPath '$var:MathBaseline * $var:MaxRegressionPercent / 100' to '10'
2025-06-30T09:33:59.3184221+05:30 [Information] Resolved template pattern '{{$var:MathBaseline * $var:MaxRegressionPercent / 100}}' to '10' in '{{$var:MathBaseline * $var:MaxRegressionPercent / 100}}'
2025-06-30T09:33:59.3186862+05:30 [Information] Resolved template '{{$var:MathBaseline * $var:MaxRegressionPercent / 100}}' to '10'
2025-06-30T09:33:59.3188311+05:30 [Information] Set Value to Step '15e45821-794f-44b9-817b-c287f8440ab1' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 233,
  "startTime": "2025-06-30T04:03:59.0853802Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "cmd"
}
2025-06-30T09:33:59.3189885+05:30 [Information] Step 'Regression Threshold Test' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "557edee5-d889-465a-9ac8-44abe9b70c7d",
        "tables": {},
        "variables": {},
        "name": "Fast Baseline Operation",
        "input": {
          "command": "echo",
          "arguments": [
            "Establishing baseline performance"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Establishing baseline performance",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 88,
          "startTime": "2025-06-30T04:03:58.9944681Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "15e45821-794f-44b9-817b-c287f8440ab1",
        "tables": {},
        "variables": {},
        "name": "Regression Threshold Test",
        "input": {
          "command": "echo",
          "arguments": [
            "Testing mathematical expression: MathBaseline * MaxRegressionPercent / 100"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Testing mathematical expression: MathBaseline * MaxRegressionPercent / 100",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 233,
          "startTime": "2025-06-30T04:03:59.0853802Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaselineThreshold": 100,
      "MaxRegressionPercent": 1000,
      "BufferTime": 500,
      "FastThreshold": 10000,
      "SlowThreshold": 20000,
      "MathBaseline": 1
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:33:59.3190608+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:33:59.3191279+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:33:59.3191807+05:30 [Information] No performance data found for step 'Regression Threshold Test' (cmd)
2025-06-30T09:33:59.3192261+05:30 [Information] [ OK       ]
2025-06-30T09:33:59.3195085+05:30 [Information] Set Value to Step '534318e3-8b0e-4ffc-8b49-e7deea8e38c3' name:
"Complex Expression Test"
2025-06-30T09:33:59.3196184+05:30 [Information] [ RUN      ] Simple Mathematical Expressions Demo - Passing Tests > Complex Expression Test
2025-06-30T09:33:59.3198834+05:30 [Information] Executing single instance of step 'Complex Expression Test'.
2025-06-30T09:33:59.3200089+05:30 [Information] Executing step 'Complex Expression Test' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "557edee5-d889-465a-9ac8-44abe9b70c7d",
        "tables": {},
        "variables": {},
        "name": "Fast Baseline Operation",
        "input": {
          "command": "echo",
          "arguments": [
            "Establishing baseline performance"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Establishing baseline performance",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 88,
          "startTime": "2025-06-30T04:03:58.9944681Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "15e45821-794f-44b9-817b-c287f8440ab1",
        "tables": {},
        "variables": {},
        "name": "Regression Threshold Test",
        "input": {
          "command": "echo",
          "arguments": [
            "Testing mathematical expression: MathBaseline * MaxRegressionPercent / 100"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Testing mathematical expression: MathBaseline * MaxRegressionPercent / 100",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 233,
          "startTime": "2025-06-30T04:03:59.0853802Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "534318e3-8b0e-4ffc-8b49-e7deea8e38c3",
        "tables": {},
        "variables": {},
        "name": "Complex Expression Test"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaselineThreshold": 100,
      "MaxRegressionPercent": 1000,
      "BufferTime": 500,
      "FastThreshold": 10000,
      "SlowThreshold": 20000,
      "MathBaseline": 1
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:33:59.3201402+05:30 [Information] Set Value to Step '534318e3-8b0e-4ffc-8b49-e7deea8e38c3' input:
{
  "command": "echo",
  "arguments": [
    "Testing complex expression: MathBaseline * MaxRegressionPercent / 100 + BufferTime"
  ],
  "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
  "printOutput": false
}
2025-06-30T09:33:59.3203034+05:30 [Information] Delaying step execution for 150ms
2025-06-30T09:33:59.4878061+05:30 [Information] Set Value to Step '534318e3-8b0e-4ffc-8b49-e7deea8e38c3' output:
{
  "stdout": "Testing complex expression: MathBaseline * MaxRegressionPercent / 100 + BufferTime",
  "stderr": "",
  "exitCode": 0
}
2025-06-30T09:33:59.4880520+05:30 [Information] Set Value to Step '534318e3-8b0e-4ffc-8b49-e7deea8e38c3' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:03:59.3198807Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "cmd"
}
2025-06-30T09:33:59.4881903+05:30 [Information] Run Asserter : {"Expression1":{"Value":"0","ErrorMesage":"Echo command failed"},"Expression2":{"JPath":"$curStep:$.output.exitCode","ErrorMesage":"Echo command failed"},"ErrorMesage":"Echo command failed"}
2025-06-30T09:33:59.4883441+05:30 [Information] Resolving path '$.output.exitCode' using $curStep:
2025-06-30T09:33:59.4884463+05:30 [Information] Resolved JPath '$curStep:$.output.exitCode' to '0'
2025-06-30T09:33:59.4885639+05:30 [Information] Run Asserter : {"Expression1":{"JPath":"$curStep:performance.executionTimeMs","ErrorMesage":"Complex threshold exceeded: {{$curStep:performance.executionTimeMs}}ms vs {{$var:MathBaseline * $var:MaxRegressionPercent / 100 + $var:BufferTime}}ms ({{$var:MathBaseline}} * {{$var:MaxRegressionPercent}}% / 100 + {{$var:BufferTime}})"},"Expression2":{"Value":"{{$var:MathBaseline * $var:MaxRegressionPercent / 100 + $var:BufferTime}}","ErrorMesage":"Complex threshold exceeded: {{$curStep:performance.executionTimeMs}}ms vs {{$var:MathBaseline * $var:MaxRegressionPercent / 100 + $var:BufferTime}}ms ({{$var:MathBaseline}} * {{$var:MaxRegressionPercent}}% / 100 + {{$var:BufferTime}})"},"ErrorMesage":"Complex threshold exceeded: {{$curStep:performance.executionTimeMs}}ms vs {{$var:MathBaseline * $var:MaxRegressionPercent / 100 + $var:BufferTime}}ms ({{$var:MathBaseline}} * {{$var:MaxRegressionPercent}}% / 100 + {{$var:BufferTime}})"}
2025-06-30T09:33:59.4887077+05:30 [Information] Resolving path 'performance.executionTimeMs' using $curStep:
2025-06-30T09:33:59.4888071+05:30 [Information] Resolved JPath '$curStep:performance.executionTimeMs' to '0'
2025-06-30T09:33:59.4890198+05:30 [Information] Processing $var: path 'MathBaseline * $var:MaxRegressionPercent / 100 + $var:BufferTime'
2025-06-30T09:33:59.4891419+05:30 [Information] Checking if 'MathBaseline * $var:MaxRegressionPercent / 100 + $var:BufferTime' is mathematical expression: hasArithmetic=True, hasArray=False, hasProperty=False, result=True
2025-06-30T09:33:59.4891988+05:30 [Information] Detected mathematical expression in $var: 'MathBaseline * $var:MaxRegressionPercent / 100 + $var:BufferTime'
2025-06-30T09:33:59.4892467+05:30 [Information] Evaluating mathematical expression: 'MathBaseline * $var:MaxRegressionPercent / 100 + $var:BufferTime'
2025-06-30T09:33:59.4892911+05:30 [Information] Resolving variables in expression: 'MathBaseline * $var:MaxRegressionPercent / 100 + $var:BufferTime'
2025-06-30T09:33:59.4893614+05:30 [Information] Resolved variable 'MaxRegressionPercent' from global variables: 1000
2025-06-30T09:33:59.4894232+05:30 [Information] Resolved variable 'BufferTime' from global variables: 500
2025-06-30T09:33:59.4894877+05:30 [Information] Resolved variable 'MathBaseline' from global variables: 1
2025-06-30T09:33:59.4895399+05:30 [Information] Expression after variable resolution: '1 * 1000 / 100 + 500'
2025-06-30T09:33:59.4895825+05:30 [Information] Expression after variable resolution: '1 * 1000 / 100 + 500'
2025-06-30T09:33:59.4896512+05:30 [Information] Mathematical expression 'MathBaseline * $var:MaxRegressionPercent / 100 + $var:BufferTime' evaluated to: 510
2025-06-30T09:33:59.4897018+05:30 [Information] Mathematical expression result: '510'
2025-06-30T09:33:59.4897569+05:30 [Information] Resolved JPath '$var:MathBaseline * $var:MaxRegressionPercent / 100 + $var:BufferTime' to '510'
2025-06-30T09:33:59.4898017+05:30 [Information] Resolved template pattern '{{$var:MathBaseline * $var:MaxRegressionPercent / 100 + $var:BufferTime}}' to '510' in '{{$var:MathBaseline * $var:MaxRegressionPercent / 100 + $var:BufferTime}}'
2025-06-30T09:33:59.4898487+05:30 [Information] Resolved template '{{$var:MathBaseline * $var:MaxRegressionPercent / 100 + $var:BufferTime}}' to '510'
2025-06-30T09:33:59.4899325+05:30 [Information] Set Value to Step '534318e3-8b0e-4ffc-8b49-e7deea8e38c3' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 169,
  "startTime": "2025-06-30T04:03:59.3198807Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "cmd"
}
2025-06-30T09:33:59.4900615+05:30 [Information] Step 'Complex Expression Test' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "557edee5-d889-465a-9ac8-44abe9b70c7d",
        "tables": {},
        "variables": {},
        "name": "Fast Baseline Operation",
        "input": {
          "command": "echo",
          "arguments": [
            "Establishing baseline performance"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Establishing baseline performance",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 88,
          "startTime": "2025-06-30T04:03:58.9944681Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "15e45821-794f-44b9-817b-c287f8440ab1",
        "tables": {},
        "variables": {},
        "name": "Regression Threshold Test",
        "input": {
          "command": "echo",
          "arguments": [
            "Testing mathematical expression: MathBaseline * MaxRegressionPercent / 100"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Testing mathematical expression: MathBaseline * MaxRegressionPercent / 100",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 233,
          "startTime": "2025-06-30T04:03:59.0853802Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "534318e3-8b0e-4ffc-8b49-e7deea8e38c3",
        "tables": {},
        "variables": {},
        "name": "Complex Expression Test",
        "input": {
          "command": "echo",
          "arguments": [
            "Testing complex expression: MathBaseline * MaxRegressionPercent / 100 + BufferTime"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Testing complex expression: MathBaseline * MaxRegressionPercent / 100 + BufferTime",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 169,
          "startTime": "2025-06-30T04:03:59.3198807Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaselineThreshold": 100,
      "MaxRegressionPercent": 1000,
      "BufferTime": 500,
      "FastThreshold": 10000,
      "SlowThreshold": 20000,
      "MathBaseline": 1
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:33:59.4901340+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:33:59.4901892+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:33:59.4902336+05:30 [Information] No performance data found for step 'Complex Expression Test' (cmd)
2025-06-30T09:33:59.4902767+05:30 [Information] [ OK       ]
2025-06-30T09:33:59.4906462+05:30 [Information] Set Value to Step '6182ac10-0605-42b3-a731-002ead732e62' name:
"Operator Precedence Test"
2025-06-30T09:33:59.4907441+05:30 [Information] [ RUN      ] Simple Mathematical Expressions Demo - Passing Tests > Operator Precedence Test
2025-06-30T09:33:59.4911137+05:30 [Information] Executing single instance of step 'Operator Precedence Test'.
2025-06-30T09:33:59.4912562+05:30 [Information] Executing step 'Operator Precedence Test' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "557edee5-d889-465a-9ac8-44abe9b70c7d",
        "tables": {},
        "variables": {},
        "name": "Fast Baseline Operation",
        "input": {
          "command": "echo",
          "arguments": [
            "Establishing baseline performance"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Establishing baseline performance",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 88,
          "startTime": "2025-06-30T04:03:58.9944681Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "15e45821-794f-44b9-817b-c287f8440ab1",
        "tables": {},
        "variables": {},
        "name": "Regression Threshold Test",
        "input": {
          "command": "echo",
          "arguments": [
            "Testing mathematical expression: MathBaseline * MaxRegressionPercent / 100"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Testing mathematical expression: MathBaseline * MaxRegressionPercent / 100",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 233,
          "startTime": "2025-06-30T04:03:59.0853802Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "534318e3-8b0e-4ffc-8b49-e7deea8e38c3",
        "tables": {},
        "variables": {},
        "name": "Complex Expression Test",
        "input": {
          "command": "echo",
          "arguments": [
            "Testing complex expression: MathBaseline * MaxRegressionPercent / 100 + BufferTime"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Testing complex expression: MathBaseline * MaxRegressionPercent / 100 + BufferTime",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 169,
          "startTime": "2025-06-30T04:03:59.3198807Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "6182ac10-0605-42b3-a731-002ead732e62",
        "tables": {},
        "variables": {},
        "name": "Operator Precedence Test"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaselineThreshold": 100,
      "MaxRegressionPercent": 1000,
      "BufferTime": 500,
      "FastThreshold": 10000,
      "SlowThreshold": 20000,
      "MathBaseline": 1
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:33:59.4913929+05:30 [Information] Set Value to Step '6182ac10-0605-42b3-a731-002ead732e62' input:
{
  "command": "echo",
  "arguments": [
    "Testing operator precedence"
  ],
  "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
  "printOutput": false
}
2025-06-30T09:33:59.4914549+05:30 [Information] Delaying step execution for 75ms
2025-06-30T09:33:59.5900067+05:30 [Information] Set Value to Step '6182ac10-0605-42b3-a731-002ead732e62' output:
{
  "stdout": "Testing operator precedence",
  "stderr": "",
  "exitCode": 0
}
2025-06-30T09:33:59.5902193+05:30 [Information] Set Value to Step '6182ac10-0605-42b3-a731-002ead732e62' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:03:59.4911111Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "cmd"
}
2025-06-30T09:33:59.5903696+05:30 [Information] Run Asserter : {"Expression1":{"Value":"0","ErrorMesage":"Echo command failed"},"Expression2":{"JPath":"$curStep:$.output.exitCode","ErrorMesage":"Echo command failed"},"ErrorMesage":"Echo command failed"}
2025-06-30T09:33:59.5904578+05:30 [Information] Resolving path '$.output.exitCode' using $curStep:
2025-06-30T09:33:59.5906413+05:30 [Information] Resolved JPath '$curStep:$.output.exitCode' to '0'
2025-06-30T09:33:59.5909083+05:30 [Information] Run Asserter : {"Expression1":{"JPath":"$curStep:performance.executionTimeMs","ErrorMesage":"Parentheses test failed: {{$curStep:performance.executionTimeMs}}ms vs {{$var:(MathBaseline + $var:BufferTime) * 2}}ms = ({{$var:MathBaseline}} + {{$var:BufferTime}}) * 2"},"Expression2":{"Value":"{{$var:(MathBaseline + $var:BufferTime) * 2}}","ErrorMesage":"Parentheses test failed: {{$curStep:performance.executionTimeMs}}ms vs {{$var:(MathBaseline + $var:BufferTime) * 2}}ms = ({{$var:MathBaseline}} + {{$var:BufferTime}}) * 2"},"ErrorMesage":"Parentheses test failed: {{$curStep:performance.executionTimeMs}}ms vs {{$var:(MathBaseline + $var:BufferTime) * 2}}ms = ({{$var:MathBaseline}} + {{$var:BufferTime}}) * 2"}
2025-06-30T09:33:59.5909984+05:30 [Information] Resolving path 'performance.executionTimeMs' using $curStep:
2025-06-30T09:33:59.5910608+05:30 [Information] Resolved JPath '$curStep:performance.executionTimeMs' to '0'
2025-06-30T09:33:59.5911209+05:30 [Information] Processing $var: path '(MathBaseline + $var:BufferTime) * 2'
2025-06-30T09:33:59.5911760+05:30 [Information] Checking if '(MathBaseline + $var:BufferTime) * 2' is mathematical expression: hasArithmetic=True, hasArray=False, hasProperty=False, result=True
2025-06-30T09:33:59.5912191+05:30 [Information] Detected mathematical expression in $var: '(MathBaseline + $var:BufferTime) * 2'
2025-06-30T09:33:59.5912674+05:30 [Information] Evaluating mathematical expression: '(MathBaseline + $var:BufferTime) * 2'
2025-06-30T09:33:59.5913089+05:30 [Information] Resolving variables in expression: '(MathBaseline + $var:BufferTime) * 2'
2025-06-30T09:33:59.5913658+05:30 [Information] Resolved variable 'BufferTime' from global variables: 500
2025-06-30T09:33:59.5914361+05:30 [Information] Resolved variable 'MathBaseline' from global variables: 1
2025-06-30T09:33:59.5914845+05:30 [Information] Expression after variable resolution: '(1 + 500) * 2'
2025-06-30T09:33:59.5915253+05:30 [Information] Expression after variable resolution: '(1 + 500) * 2'
2025-06-30T09:33:59.5915861+05:30 [Information] Mathematical expression '(MathBaseline + $var:BufferTime) * 2' evaluated to: 1002
2025-06-30T09:33:59.5916278+05:30 [Information] Mathematical expression result: '1002'
2025-06-30T09:33:59.5916761+05:30 [Information] Resolved JPath '$var:(MathBaseline + $var:BufferTime) * 2' to '1002'
2025-06-30T09:33:59.5917439+05:30 [Information] Resolved template pattern '{{$var:(MathBaseline + $var:BufferTime) * 2}}' to '1002' in '{{$var:(MathBaseline + $var:BufferTime) * 2}}'
2025-06-30T09:33:59.5917956+05:30 [Information] Resolved template '{{$var:(MathBaseline + $var:BufferTime) * 2}}' to '1002'
2025-06-30T09:33:59.5919128+05:30 [Information] Run Asserter : {"Expression1":{"JPath":"$curStep:performance.executionTimeMs","ErrorMesage":"Precedence test failed: {{$curStep:performance.executionTimeMs}}ms vs {{$var:MathBaseline + $var:BufferTime * 2}}ms = {{$var:MathBaseline}} + {{$var:BufferTime}} * 2"},"Expression2":{"Value":"{{$var:MathBaseline + $var:BufferTime * 2}}","ErrorMesage":"Precedence test failed: {{$curStep:performance.executionTimeMs}}ms vs {{$var:MathBaseline + $var:BufferTime * 2}}ms = {{$var:MathBaseline}} + {{$var:BufferTime}} * 2"},"ErrorMesage":"Precedence test failed: {{$curStep:performance.executionTimeMs}}ms vs {{$var:MathBaseline + $var:BufferTime * 2}}ms = {{$var:MathBaseline}} + {{$var:BufferTime}} * 2"}
2025-06-30T09:33:59.5920147+05:30 [Information] Resolving path 'performance.executionTimeMs' using $curStep:
2025-06-30T09:33:59.5921102+05:30 [Information] Resolved JPath '$curStep:performance.executionTimeMs' to '0'
2025-06-30T09:33:59.5921997+05:30 [Information] Processing $var: path 'MathBaseline + $var:BufferTime * 2'
2025-06-30T09:33:59.5922968+05:30 [Information] Checking if 'MathBaseline + $var:BufferTime * 2' is mathematical expression: hasArithmetic=True, hasArray=False, hasProperty=False, result=True
2025-06-30T09:33:59.5923527+05:30 [Information] Detected mathematical expression in $var: 'MathBaseline + $var:BufferTime * 2'
2025-06-30T09:33:59.5923964+05:30 [Information] Evaluating mathematical expression: 'MathBaseline + $var:BufferTime * 2'
2025-06-30T09:33:59.5924514+05:30 [Information] Resolving variables in expression: 'MathBaseline + $var:BufferTime * 2'
2025-06-30T09:33:59.5925356+05:30 [Information] Resolved variable 'BufferTime' from global variables: 500
2025-06-30T09:33:59.5926276+05:30 [Information] Resolved variable 'MathBaseline' from global variables: 1
2025-06-30T09:33:59.5926953+05:30 [Information] Expression after variable resolution: '1 + 500 * 2'
2025-06-30T09:33:59.5927565+05:30 [Information] Expression after variable resolution: '1 + 500 * 2'
2025-06-30T09:33:59.5928297+05:30 [Information] Mathematical expression 'MathBaseline + $var:BufferTime * 2' evaluated to: 1001
2025-06-30T09:33:59.5928893+05:30 [Information] Mathematical expression result: '1001'
2025-06-30T09:33:59.5929543+05:30 [Information] Resolved JPath '$var:MathBaseline + $var:BufferTime * 2' to '1001'
2025-06-30T09:33:59.5930126+05:30 [Information] Resolved template pattern '{{$var:MathBaseline + $var:BufferTime * 2}}' to '1001' in '{{$var:MathBaseline + $var:BufferTime * 2}}'
2025-06-30T09:33:59.5930714+05:30 [Information] Resolved template '{{$var:MathBaseline + $var:BufferTime * 2}}' to '1001'
2025-06-30T09:33:59.5931729+05:30 [Information] Set Value to Step '6182ac10-0605-42b3-a731-002ead732e62' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 101,
  "startTime": "2025-06-30T04:03:59.4911111Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "cmd"
}
2025-06-30T09:33:59.5933449+05:30 [Information] Step 'Operator Precedence Test' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "557edee5-d889-465a-9ac8-44abe9b70c7d",
        "tables": {},
        "variables": {},
        "name": "Fast Baseline Operation",
        "input": {
          "command": "echo",
          "arguments": [
            "Establishing baseline performance"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Establishing baseline performance",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 88,
          "startTime": "2025-06-30T04:03:58.9944681Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "15e45821-794f-44b9-817b-c287f8440ab1",
        "tables": {},
        "variables": {},
        "name": "Regression Threshold Test",
        "input": {
          "command": "echo",
          "arguments": [
            "Testing mathematical expression: MathBaseline * MaxRegressionPercent / 100"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Testing mathematical expression: MathBaseline * MaxRegressionPercent / 100",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 233,
          "startTime": "2025-06-30T04:03:59.0853802Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "534318e3-8b0e-4ffc-8b49-e7deea8e38c3",
        "tables": {},
        "variables": {},
        "name": "Complex Expression Test",
        "input": {
          "command": "echo",
          "arguments": [
            "Testing complex expression: MathBaseline * MaxRegressionPercent / 100 + BufferTime"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Testing complex expression: MathBaseline * MaxRegressionPercent / 100 + BufferTime",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 169,
          "startTime": "2025-06-30T04:03:59.3198807Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "6182ac10-0605-42b3-a731-002ead732e62",
        "tables": {},
        "variables": {},
        "name": "Operator Precedence Test",
        "input": {
          "command": "echo",
          "arguments": [
            "Testing operator precedence"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Testing operator precedence",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 101,
          "startTime": "2025-06-30T04:03:59.4911111Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaselineThreshold": 100,
      "MaxRegressionPercent": 1000,
      "BufferTime": 500,
      "FastThreshold": 10000,
      "SlowThreshold": 20000,
      "MathBaseline": 1
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:33:59.5934180+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:33:59.5934714+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:33:59.5935559+05:30 [Information] No performance data found for step 'Operator Precedence Test' (cmd)
2025-06-30T09:33:59.5936061+05:30 [Information] [ OK       ]
2025-06-30T09:33:59.5942182+05:30 [Information] Set Value to Step '60d6a620-5461-4849-8a5a-cc7238867b48' name:
"Performance Budget Test"
2025-06-30T09:33:59.5943122+05:30 [Information] [ RUN      ] Simple Mathematical Expressions Demo - Passing Tests > Performance Budget Test
2025-06-30T09:33:59.5945512+05:30 [Information] Executing single instance of step 'Performance Budget Test'.
2025-06-30T09:33:59.5949237+05:30 [Information] Executing step 'Performance Budget Test' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "557edee5-d889-465a-9ac8-44abe9b70c7d",
        "tables": {},
        "variables": {},
        "name": "Fast Baseline Operation",
        "input": {
          "command": "echo",
          "arguments": [
            "Establishing baseline performance"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Establishing baseline performance",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 88,
          "startTime": "2025-06-30T04:03:58.9944681Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "15e45821-794f-44b9-817b-c287f8440ab1",
        "tables": {},
        "variables": {},
        "name": "Regression Threshold Test",
        "input": {
          "command": "echo",
          "arguments": [
            "Testing mathematical expression: MathBaseline * MaxRegressionPercent / 100"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Testing mathematical expression: MathBaseline * MaxRegressionPercent / 100",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 233,
          "startTime": "2025-06-30T04:03:59.0853802Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "534318e3-8b0e-4ffc-8b49-e7deea8e38c3",
        "tables": {},
        "variables": {},
        "name": "Complex Expression Test",
        "input": {
          "command": "echo",
          "arguments": [
            "Testing complex expression: MathBaseline * MaxRegressionPercent / 100 + BufferTime"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Testing complex expression: MathBaseline * MaxRegressionPercent / 100 + BufferTime",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 169,
          "startTime": "2025-06-30T04:03:59.3198807Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "6182ac10-0605-42b3-a731-002ead732e62",
        "tables": {},
        "variables": {},
        "name": "Operator Precedence Test",
        "input": {
          "command": "echo",
          "arguments": [
            "Testing operator precedence"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Testing operator precedence",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 101,
          "startTime": "2025-06-30T04:03:59.4911111Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "60d6a620-5461-4849-8a5a-cc7238867b48",
        "tables": {},
        "variables": {
          "CriticalPath": 5000,
          "OptimizationFactor": 0.8
        },
        "name": "Performance Budget Test"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaselineThreshold": 100,
      "MaxRegressionPercent": 1000,
      "BufferTime": 500,
      "FastThreshold": 10000,
      "SlowThreshold": 20000,
      "MathBaseline": 1
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:33:59.5950883+05:30 [Information] Set Value to Step '60d6a620-5461-4849-8a5a-cc7238867b48' input:
{
  "command": "echo",
  "arguments": [
    "Testing performance budget: CriticalPath * OptimizationFactor"
  ],
  "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
  "printOutput": false
}
2025-06-30T09:33:59.5951750+05:30 [Information] Delaying step execution for 200ms
2025-06-30T09:33:59.8243282+05:30 [Information] Set Value to Step '60d6a620-5461-4849-8a5a-cc7238867b48' output:
{
  "stdout": "Testing performance budget: CriticalPath * OptimizationFactor",
  "stderr": "",
  "exitCode": 0
}
2025-06-30T09:33:59.8245250+05:30 [Information] Set Value to Step '60d6a620-5461-4849-8a5a-cc7238867b48' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:03:59.594549Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "cmd"
}
2025-06-30T09:33:59.8246797+05:30 [Information] Run Asserter : {"Expression1":{"Value":"0","ErrorMesage":"Echo command failed"},"Expression2":{"JPath":"$curStep:$.output.exitCode","ErrorMesage":"Echo command failed"},"ErrorMesage":"Echo command failed"}
2025-06-30T09:33:59.8247954+05:30 [Information] Resolving path '$.output.exitCode' using $curStep:
2025-06-30T09:33:59.8250100+05:30 [Information] Resolved JPath '$curStep:$.output.exitCode' to '0'
2025-06-30T09:33:59.8252558+05:30 [Information] Run Asserter : {"Expression1":{"JPath":"$curStep:performance.executionTimeMs","ErrorMesage":"Performance budget exceeded: {{$curStep:performance.executionTimeMs}}ms vs budget {{$var:CriticalPath * $var:OptimizationFactor}}ms"},"Expression2":{"Value":"{{$var:CriticalPath * $var:OptimizationFactor}}","ErrorMesage":"Performance budget exceeded: {{$curStep:performance.executionTimeMs}}ms vs budget {{$var:CriticalPath * $var:OptimizationFactor}}ms"},"ErrorMesage":"Performance budget exceeded: {{$curStep:performance.executionTimeMs}}ms vs budget {{$var:CriticalPath * $var:OptimizationFactor}}ms"}
2025-06-30T09:33:59.8254262+05:30 [Information] Resolving path 'performance.executionTimeMs' using $curStep:
2025-06-30T09:33:59.8255721+05:30 [Information] Resolved JPath '$curStep:performance.executionTimeMs' to '0'
2025-06-30T09:33:59.8257256+05:30 [Information] Processing $var: path 'CriticalPath * $var:OptimizationFactor'
2025-06-30T09:33:59.8258619+05:30 [Information] Checking if 'CriticalPath * $var:OptimizationFactor' is mathematical expression: hasArithmetic=True, hasArray=False, hasProperty=False, result=True
2025-06-30T09:33:59.8260346+05:30 [Information] Detected mathematical expression in $var: 'CriticalPath * $var:OptimizationFactor'
2025-06-30T09:33:59.8261413+05:30 [Information] Evaluating mathematical expression: 'CriticalPath * $var:OptimizationFactor'
2025-06-30T09:33:59.8262316+05:30 [Information] Resolving variables in expression: 'CriticalPath * $var:OptimizationFactor'
2025-06-30T09:33:59.8263801+05:30 [Information] Resolved variable 'OptimizationFactor' from step variables: 0.8
2025-06-30T09:33:59.8264892+05:30 [Information] Resolved variable 'CriticalPath' from step variables: 5000
2025-06-30T09:33:59.8265623+05:30 [Information] Expression after variable resolution: '5000 * 0.8'
2025-06-30T09:33:59.8266129+05:30 [Information] Expression after variable resolution: '5000 * 0.8'
2025-06-30T09:33:59.8266829+05:30 [Information] Mathematical expression 'CriticalPath * $var:OptimizationFactor' evaluated to: 4000
2025-06-30T09:33:59.8267437+05:30 [Information] Mathematical expression result: '4000'
2025-06-30T09:33:59.8268042+05:30 [Information] Resolved JPath '$var:CriticalPath * $var:OptimizationFactor' to '4000'
2025-06-30T09:33:59.8268555+05:30 [Information] Resolved template pattern '{{$var:CriticalPath * $var:OptimizationFactor}}' to '4000' in '{{$var:CriticalPath * $var:OptimizationFactor}}'
2025-06-30T09:33:59.8269031+05:30 [Information] Resolved template '{{$var:CriticalPath * $var:OptimizationFactor}}' to '4000'
2025-06-30T09:33:59.8269732+05:30 [Information] Run Asserter : {"Expression1":{"JPath":"$curStep:performance.executionTimeMs","ErrorMesage":"Absolute performance limit exceeded: {{$curStep:performance.executionTimeMs}}ms vs {{$var:CriticalPath}}ms"},"Expression2":{"Value":"{{$var:CriticalPath}}","ErrorMesage":"Absolute performance limit exceeded: {{$curStep:performance.executionTimeMs}}ms vs {{$var:CriticalPath}}ms"},"ErrorMesage":"Absolute performance limit exceeded: {{$curStep:performance.executionTimeMs}}ms vs {{$var:CriticalPath}}ms"}
2025-06-30T09:33:59.8270264+05:30 [Information] Resolving path 'performance.executionTimeMs' using $curStep:
2025-06-30T09:33:59.8270770+05:30 [Information] Resolved JPath '$curStep:performance.executionTimeMs' to '0'
2025-06-30T09:33:59.8271292+05:30 [Information] Processing $var: path 'CriticalPath'
2025-06-30T09:33:59.8271743+05:30 [Information] Checking if 'CriticalPath' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:33:59.8272193+05:30 [Information] Resolved path 'CriticalPath' in step-specific variables.
2025-06-30T09:33:59.8272683+05:30 [Information] Resolved JPath '$var:CriticalPath' to '5000'
2025-06-30T09:33:59.8273176+05:30 [Information] Resolved template pattern '{{$var:CriticalPath}}' to '5000' in '{{$var:CriticalPath}}'
2025-06-30T09:33:59.8273596+05:30 [Information] Resolved template '{{$var:CriticalPath}}' to '5000'
2025-06-30T09:33:59.8274305+05:30 [Information] Set Value to Step '60d6a620-5461-4849-8a5a-cc7238867b48' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 232,
  "startTime": "2025-06-30T04:03:59.594549Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "cmd"
}
2025-06-30T09:33:59.8276102+05:30 [Information] Step 'Performance Budget Test' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "557edee5-d889-465a-9ac8-44abe9b70c7d",
        "tables": {},
        "variables": {},
        "name": "Fast Baseline Operation",
        "input": {
          "command": "echo",
          "arguments": [
            "Establishing baseline performance"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Establishing baseline performance",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 88,
          "startTime": "2025-06-30T04:03:58.9944681Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "15e45821-794f-44b9-817b-c287f8440ab1",
        "tables": {},
        "variables": {},
        "name": "Regression Threshold Test",
        "input": {
          "command": "echo",
          "arguments": [
            "Testing mathematical expression: MathBaseline * MaxRegressionPercent / 100"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Testing mathematical expression: MathBaseline * MaxRegressionPercent / 100",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 233,
          "startTime": "2025-06-30T04:03:59.0853802Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "534318e3-8b0e-4ffc-8b49-e7deea8e38c3",
        "tables": {},
        "variables": {},
        "name": "Complex Expression Test",
        "input": {
          "command": "echo",
          "arguments": [
            "Testing complex expression: MathBaseline * MaxRegressionPercent / 100 + BufferTime"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Testing complex expression: MathBaseline * MaxRegressionPercent / 100 + BufferTime",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 169,
          "startTime": "2025-06-30T04:03:59.3198807Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "6182ac10-0605-42b3-a731-002ead732e62",
        "tables": {},
        "variables": {},
        "name": "Operator Precedence Test",
        "input": {
          "command": "echo",
          "arguments": [
            "Testing operator precedence"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Testing operator precedence",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 101,
          "startTime": "2025-06-30T04:03:59.4911111Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "60d6a620-5461-4849-8a5a-cc7238867b48",
        "tables": {},
        "variables": {
          "CriticalPath": 5000,
          "OptimizationFactor": 0.8
        },
        "name": "Performance Budget Test",
        "input": {
          "command": "echo",
          "arguments": [
            "Testing performance budget: CriticalPath * OptimizationFactor"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Testing performance budget: CriticalPath * OptimizationFactor",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 232,
          "startTime": "2025-06-30T04:03:59.594549Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaselineThreshold": 100,
      "MaxRegressionPercent": 1000,
      "BufferTime": 500,
      "FastThreshold": 10000,
      "SlowThreshold": 20000,
      "MathBaseline": 1
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:33:59.8276979+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:33:59.8277530+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:33:59.8277980+05:30 [Information] No performance data found for step 'Performance Budget Test' (cmd)
2025-06-30T09:33:59.8278388+05:30 [Information] [ OK       ]
2025-06-30T09:33:59.8281058+05:30 [Information] Set Value to Step '8015d080-74f6-4d21-98c8-caad4d6395c7' name:
"Error Message Demo"
2025-06-30T09:33:59.8281794+05:30 [Information] [ RUN      ] Simple Mathematical Expressions Demo - Passing Tests > Error Message Demo
2025-06-30T09:33:59.8284205+05:30 [Information] Executing single instance of step 'Error Message Demo'.
2025-06-30T09:33:59.8286496+05:30 [Information] Executing step 'Error Message Demo' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "557edee5-d889-465a-9ac8-44abe9b70c7d",
        "tables": {},
        "variables": {},
        "name": "Fast Baseline Operation",
        "input": {
          "command": "echo",
          "arguments": [
            "Establishing baseline performance"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Establishing baseline performance",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 88,
          "startTime": "2025-06-30T04:03:58.9944681Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "15e45821-794f-44b9-817b-c287f8440ab1",
        "tables": {},
        "variables": {},
        "name": "Regression Threshold Test",
        "input": {
          "command": "echo",
          "arguments": [
            "Testing mathematical expression: MathBaseline * MaxRegressionPercent / 100"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Testing mathematical expression: MathBaseline * MaxRegressionPercent / 100",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 233,
          "startTime": "2025-06-30T04:03:59.0853802Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "534318e3-8b0e-4ffc-8b49-e7deea8e38c3",
        "tables": {},
        "variables": {},
        "name": "Complex Expression Test",
        "input": {
          "command": "echo",
          "arguments": [
            "Testing complex expression: MathBaseline * MaxRegressionPercent / 100 + BufferTime"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Testing complex expression: MathBaseline * MaxRegressionPercent / 100 + BufferTime",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 169,
          "startTime": "2025-06-30T04:03:59.3198807Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "6182ac10-0605-42b3-a731-002ead732e62",
        "tables": {},
        "variables": {},
        "name": "Operator Precedence Test",
        "input": {
          "command": "echo",
          "arguments": [
            "Testing operator precedence"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Testing operator precedence",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 101,
          "startTime": "2025-06-30T04:03:59.4911111Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "60d6a620-5461-4849-8a5a-cc7238867b48",
        "tables": {},
        "variables": {
          "CriticalPath": 5000,
          "OptimizationFactor": 0.8
        },
        "name": "Performance Budget Test",
        "input": {
          "command": "echo",
          "arguments": [
            "Testing performance budget: CriticalPath * OptimizationFactor"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Testing performance budget: CriticalPath * OptimizationFactor",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 232,
          "startTime": "2025-06-30T04:03:59.594549Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "8015d080-74f6-4d21-98c8-caad4d6395c7",
        "tables": {},
        "variables": {},
        "name": "Error Message Demo"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaselineThreshold": 100,
      "MaxRegressionPercent": 1000,
      "BufferTime": 500,
      "FastThreshold": 10000,
      "SlowThreshold": 20000,
      "MathBaseline": 1
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:33:59.8287944+05:30 [Information] Set Value to Step '8015d080-74f6-4d21-98c8-caad4d6395c7' input:
{
  "command": "echo",
  "arguments": [
    "Mathematical expressions work in error messages too!"
  ],
  "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
  "printOutput": false
}
2025-06-30T09:33:59.8288631+05:30 [Information] Delaying step execution for 50ms
2025-06-30T09:33:59.9081959+05:30 [Information] Set Value to Step '8015d080-74f6-4d21-98c8-caad4d6395c7' output:
{
  "stdout": "Mathematical expressions work in error messages too!",
  "stderr": "",
  "exitCode": 0
}
2025-06-30T09:33:59.9083702+05:30 [Information] Set Value to Step '8015d080-74f6-4d21-98c8-caad4d6395c7' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:03:59.8284175Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "cmd"
}
2025-06-30T09:33:59.9085008+05:30 [Information] Run Asserter : {"Expression1":{"Value":"0","ErrorMesage":"Echo command failed"},"Expression2":{"JPath":"$curStep:$.output.exitCode","ErrorMesage":"Echo command failed"},"ErrorMesage":"Echo command failed"}
2025-06-30T09:33:59.9085976+05:30 [Information] Resolving path '$.output.exitCode' using $curStep:
2025-06-30T09:33:59.9086685+05:30 [Information] Resolved JPath '$curStep:$.output.exitCode' to '0'
2025-06-30T09:33:59.9087774+05:30 [Information] Run Asserter : {"Expression1":{"JPath":"$curStep:performance.executionTimeMs","ErrorMesage":"Performance test failed: {{$curStep:performance.executionTimeMs}}ms exceeded {{$var:MathBaseline * 3}}ms (3x baseline of {{$var:MathBaseline}}ms)"},"Expression2":{"Value":"{{$var:MathBaseline * 3}}","ErrorMesage":"Performance test failed: {{$curStep:performance.executionTimeMs}}ms exceeded {{$var:MathBaseline * 3}}ms (3x baseline of {{$var:MathBaseline}}ms)"},"ErrorMesage":"Performance test failed: {{$curStep:performance.executionTimeMs}}ms exceeded {{$var:MathBaseline * 3}}ms (3x baseline of {{$var:MathBaseline}}ms)"}
2025-06-30T09:33:59.9088415+05:30 [Information] Resolving path 'performance.executionTimeMs' using $curStep:
2025-06-30T09:33:59.9088948+05:30 [Information] Resolved JPath '$curStep:performance.executionTimeMs' to '0'
2025-06-30T09:33:59.9089472+05:30 [Information] Processing $var: path 'MathBaseline * 3'
2025-06-30T09:33:59.9090007+05:30 [Information] Checking if 'MathBaseline * 3' is mathematical expression: hasArithmetic=True, hasArray=False, hasProperty=False, result=True
2025-06-30T09:33:59.9090501+05:30 [Information] Detected mathematical expression in $var: 'MathBaseline * 3'
2025-06-30T09:33:59.9090953+05:30 [Information] Evaluating mathematical expression: 'MathBaseline * 3'
2025-06-30T09:33:59.9091466+05:30 [Information] Resolving variables in expression: 'MathBaseline * 3'
2025-06-30T09:33:59.9092105+05:30 [Information] Resolved variable 'MathBaseline' from global variables: 1
2025-06-30T09:33:59.9092644+05:30 [Information] Expression after variable resolution: '1 * 3'
2025-06-30T09:33:59.9093045+05:30 [Information] Expression after variable resolution: '1 * 3'
2025-06-30T09:33:59.9093606+05:30 [Information] Mathematical expression 'MathBaseline * 3' evaluated to: 3
2025-06-30T09:33:59.9094059+05:30 [Information] Mathematical expression result: '3'
2025-06-30T09:33:59.9094543+05:30 [Information] Resolved JPath '$var:MathBaseline * 3' to '3'
2025-06-30T09:33:59.9094913+05:30 [Information] Resolved template pattern '{{$var:MathBaseline * 3}}' to '3' in '{{$var:MathBaseline * 3}}'
2025-06-30T09:33:59.9095340+05:30 [Information] Resolved template '{{$var:MathBaseline * 3}}' to '3'
2025-06-30T09:33:59.9096171+05:30 [Information] Set Value to Step '8015d080-74f6-4d21-98c8-caad4d6395c7' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 80,
  "startTime": "2025-06-30T04:03:59.8284175Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "cmd"
}
2025-06-30T09:33:59.9097949+05:30 [Information] Step 'Error Message Demo' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "557edee5-d889-465a-9ac8-44abe9b70c7d",
        "tables": {},
        "variables": {},
        "name": "Fast Baseline Operation",
        "input": {
          "command": "echo",
          "arguments": [
            "Establishing baseline performance"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Establishing baseline performance",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 88,
          "startTime": "2025-06-30T04:03:58.9944681Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "15e45821-794f-44b9-817b-c287f8440ab1",
        "tables": {},
        "variables": {},
        "name": "Regression Threshold Test",
        "input": {
          "command": "echo",
          "arguments": [
            "Testing mathematical expression: MathBaseline * MaxRegressionPercent / 100"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Testing mathematical expression: MathBaseline * MaxRegressionPercent / 100",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 233,
          "startTime": "2025-06-30T04:03:59.0853802Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "534318e3-8b0e-4ffc-8b49-e7deea8e38c3",
        "tables": {},
        "variables": {},
        "name": "Complex Expression Test",
        "input": {
          "command": "echo",
          "arguments": [
            "Testing complex expression: MathBaseline * MaxRegressionPercent / 100 + BufferTime"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Testing complex expression: MathBaseline * MaxRegressionPercent / 100 + BufferTime",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 169,
          "startTime": "2025-06-30T04:03:59.3198807Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "6182ac10-0605-42b3-a731-002ead732e62",
        "tables": {},
        "variables": {},
        "name": "Operator Precedence Test",
        "input": {
          "command": "echo",
          "arguments": [
            "Testing operator precedence"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Testing operator precedence",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 101,
          "startTime": "2025-06-30T04:03:59.4911111Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "60d6a620-5461-4849-8a5a-cc7238867b48",
        "tables": {},
        "variables": {
          "CriticalPath": 5000,
          "OptimizationFactor": 0.8
        },
        "name": "Performance Budget Test",
        "input": {
          "command": "echo",
          "arguments": [
            "Testing performance budget: CriticalPath * OptimizationFactor"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Testing performance budget: CriticalPath * OptimizationFactor",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 232,
          "startTime": "2025-06-30T04:03:59.594549Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "8015d080-74f6-4d21-98c8-caad4d6395c7",
        "tables": {},
        "variables": {},
        "name": "Error Message Demo",
        "input": {
          "command": "echo",
          "arguments": [
            "Mathematical expressions work in error messages too!"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Mathematical expressions work in error messages too!",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 80,
          "startTime": "2025-06-30T04:03:59.8284175Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaselineThreshold": 100,
      "MaxRegressionPercent": 1000,
      "BufferTime": 500,
      "FastThreshold": 10000,
      "SlowThreshold": 20000,
      "MathBaseline": 1
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:33:59.9098730+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:33:59.9099285+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:33:59.9099701+05:30 [Information] No performance data found for step 'Error Message Demo' (cmd)
2025-06-30T09:33:59.9100098+05:30 [Information] [ OK       ]
2025-06-30T09:33:59.9102339+05:30 [Information] [----------] 6 steps from Simple Mathematical Expressions Demo - Passing Tests (917 ms total)
