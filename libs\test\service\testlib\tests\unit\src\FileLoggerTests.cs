/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using Microsoft.Extensions.Logging;
using NUnit.Framework;
using System;
using System.IO;

namespace TestLib.Tests;

[TestFixture]
public class FileLoggerTests
{
    private string _testDirectory;
    private string _logFilePath;
    private FileLogger _logger;

    [SetUp]
    public void Setup()
    {
        _testDirectory = Path.Combine(Path.GetTempPath(), Guid.NewGuid().ToString());
        Directory.CreateDirectory(_testDirectory);
        _logFilePath = Path.Combine(_testDirectory, "test.log");
        _logger = new FileLogger(_logFilePath);
    }

    [TearDown]
    public void TearDown()
    {
        if (Directory.Exists(_testDirectory))
        {
            Directory.Delete(_testDirectory, true);
        }
    }

    [Test]
    public void Constructor_CreatesFileLogger()
    {
        var logger = new FileLogger(_logFilePath);
        Assert.IsNotNull(logger);
    }

    [Test]
    public void IsEnabled_WithValidLogLevel_ReturnsTrue()
    {
        Assert.IsTrue(_logger.IsEnabled(LogLevel.Information));
        Assert.IsTrue(_logger.IsEnabled(LogLevel.Warning));
        Assert.IsTrue(_logger.IsEnabled(LogLevel.Error));
        Assert.IsTrue(_logger.IsEnabled(LogLevel.Debug));
        Assert.IsTrue(_logger.IsEnabled(LogLevel.Trace));
        Assert.IsTrue(_logger.IsEnabled(LogLevel.Critical));
    }

    [Test]
    public void IsEnabled_WithNoneLogLevel_ReturnsFalse()
    {
        Assert.IsFalse(_logger.IsEnabled(LogLevel.None));
    }

    [Test]
    public void BeginScope_ReturnsNull()
    {
        var scope = _logger.BeginScope("test scope");
        Assert.IsNull(scope);
    }

    [Test]
    public void Log_WithInformationLevel_WritesToFile()
    {
        // Arrange
        var message = "Test information message";
        
        // Act
        _logger.Log(LogLevel.Information, new EventId(1), message, null, (state, ex) => state.ToString());
        
        // Assert
        Assert.IsTrue(File.Exists(_logFilePath));
        var content = File.ReadAllText(_logFilePath);
        StringAssert.Contains("[Information]", content);
        StringAssert.Contains(message, content);
    }

    [Test]
    public void Log_WithErrorLevel_WritesToFile()
    {
        // Arrange
        var message = "Test error message";
        
        // Act
        _logger.Log(LogLevel.Error, new EventId(2), message, null, (state, ex) => state.ToString());
        
        // Assert
        Assert.IsTrue(File.Exists(_logFilePath));
        var content = File.ReadAllText(_logFilePath);
        StringAssert.Contains("[Error]", content);
        StringAssert.Contains(message, content);
    }

    [Test]
    public void Log_WithWarningLevel_WritesToFile()
    {
        // Arrange
        var message = "Test warning message";
        
        // Act
        _logger.Log(LogLevel.Warning, new EventId(3), message, null, (state, ex) => state.ToString());
        
        // Assert
        Assert.IsTrue(File.Exists(_logFilePath));
        var content = File.ReadAllText(_logFilePath);
        StringAssert.Contains("[Warning]", content);
        StringAssert.Contains(message, content);
    }

    [Test]
    public void Log_WithNoneLevel_DoesNotWriteToFile()
    {
        // Arrange
        var message = "Test none message";
        
        // Act
        _logger.Log(LogLevel.None, new EventId(4), message, null, (state, ex) => state.ToString());
        
        // Assert
        Assert.IsFalse(File.Exists(_logFilePath));
    }

    [Test]
    public void Log_MultipleMessages_AppendsToFile()
    {
        // Arrange
        var message1 = "First message";
        var message2 = "Second message";
        
        // Act
        _logger.Log(LogLevel.Information, new EventId(1), message1, null, (state, ex) => state.ToString());
        _logger.Log(LogLevel.Warning, new EventId(2), message2, null, (state, ex) => state.ToString());
        
        // Assert
        Assert.IsTrue(File.Exists(_logFilePath));
        var content = File.ReadAllText(_logFilePath);
        StringAssert.Contains(message1, content);
        StringAssert.Contains(message2, content);
        StringAssert.Contains("[Information]", content);
        StringAssert.Contains("[Warning]", content);
    }

    [Test]
    public void Log_WithException_IncludesExceptionInMessage()
    {
        // Arrange
        var message = "Test message with exception";
        var exception = new InvalidOperationException("Test exception");
        
        // Act
        _logger.Log(LogLevel.Error, new EventId(5), message, exception, (state, ex) => $"{state} - Exception: {ex?.Message}");
        
        // Assert
        Assert.IsTrue(File.Exists(_logFilePath));
        var content = File.ReadAllText(_logFilePath);
        StringAssert.Contains(message, content);
        StringAssert.Contains("Test exception", content);
        StringAssert.Contains("[Error]", content);
    }

    [Test]
    public void Log_WithTimestamp_IncludesTimestamp()
    {
        // Arrange
        var message = "Test timestamped message";
        var beforeLog = DateTime.Now;
        
        // Act
        _logger.Log(LogLevel.Information, new EventId(6), message, null, (state, ex) => state.ToString());
        
        var afterLog = DateTime.Now;
        
        // Assert
        Assert.IsTrue(File.Exists(_logFilePath));
        var content = File.ReadAllText(_logFilePath);
        
        // Check that the log contains a timestamp (ISO 8601 format)
        Assert.IsTrue(content.Contains("T") && content.Contains(":"));
        StringAssert.Contains(message, content);
    }

    [Test]
    public void Log_ConcurrentAccess_HandlesThreadSafety()
    {
        // Arrange
        var tasks = new System.Threading.Tasks.Task[10];
        
        // Act
        for (int i = 0; i < tasks.Length; i++)
        {
            int messageIndex = i;
            tasks[i] = System.Threading.Tasks.Task.Run(() =>
            {
                _logger.Log(LogLevel.Information, new EventId(messageIndex), $"Message {messageIndex}", null, (state, ex) => state.ToString());
            });
        }
        
        System.Threading.Tasks.Task.WaitAll(tasks);
        
        // Assert
        Assert.IsTrue(File.Exists(_logFilePath));
        var content = File.ReadAllText(_logFilePath);
        
        // Verify all messages were written
        for (int i = 0; i < tasks.Length; i++)
        {
            StringAssert.Contains($"Message {i}", content);
        }
    }

    [Test]
    public void Log_WithComplexState_FormatsCorrectly()
    {
        // Arrange
        var complexState = new { UserId = 123, Action = "Login", Timestamp = DateTime.Now };
        
        // Act
        _logger.Log(LogLevel.Information, new EventId(7), complexState, null, 
            (state, ex) => $"User {state.UserId} performed {state.Action} at {state.Timestamp}");
        
        // Assert
        Assert.IsTrue(File.Exists(_logFilePath));
        var content = File.ReadAllText(_logFilePath);
        StringAssert.Contains("User 123", content);
        StringAssert.Contains("performed Login", content);
    }
}
