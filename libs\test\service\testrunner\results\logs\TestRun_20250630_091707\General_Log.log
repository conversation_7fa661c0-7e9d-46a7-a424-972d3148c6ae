2025-06-30T09:17:07.1831508+05:30 [Information] Global variables set to: baseUrl: https://httpbin.org, serverIp: 127.0.0.1, serverPort: 60606
2025-06-30T09:17:07.1991239+05:30 [Information] Loading tests from D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc with pattern *math-expressions-simple.yaml
2025-06-30T09:17:07.2030516+05:30 [Information] Loading test from D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc\math-expressions-simple.yaml
2025-06-30T09:17:07.2152845+05:30 [Information] Global variables set to: 
2025-06-30T09:17:07.2163644+05:30 [Information] [==========] Running 1 test from Yaml files
2025-06-30T09:17:07.2179058+05:30 [Information] [-------------------------------------    Startup     -------------------------------------]
2025-06-30T09:17:07.2383588+05:30 [Information] [----------] 1 step from Startup
2025-06-30T09:17:07.2413222+05:30 [Information] [ RUN      ] Startup > Initialize Database
2025-06-30T09:17:09.5652610+05:30 [Information] [ OK       ]
2025-06-30T09:17:09.5662963+05:30 [Information] [----------] 1 step from Startup (2326 ms total)
2025-06-30T09:17:09.5684129+05:30 [Information] Global variables set to: curDir: D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc, baseUrl: https://httpbin.org, serverIp: 127.0.0.1, serverPort: 60606, dbInitStatus: initialize_db
2025-06-30T09:17:09.5689498+05:30 [Information] [------------------------------------- Test Execution -------------------------------------]
2025-06-30T09:17:09.5702202+05:30 [Information] [----------] 6 steps from Simple Mathematical Expressions Demo
2025-06-30T09:17:09.5716805+05:30 [Information] [ RUN      ] Simple Mathematical Expressions Demo > Fast Baseline Operation
2025-06-30T09:17:09.6561430+05:30 [Information] [ OK       ]
2025-06-30T09:17:09.6565253+05:30 [Information] [ RUN      ] Simple Mathematical Expressions Demo > Regression Threshold Test
2025-06-30T09:17:10.8060539+05:30 [Error] Assert Failure : Less(150, 0)
2025-06-30T09:17:10.8067729+05:30 [Error] Performance regression detected: {{$curStep:performance.executionTimeMs}}ms vs allowed {{$var:MathBaseline * $var:MaxRegressionPercent / 100}}ms ({{$var:MathBaseline}}ms * {{$var:MaxRegressionPercent}}% / 100)
2025-06-30T09:17:10.8068801+05:30 [Error] [ FAILED   ]
2025-06-30T09:17:10.8073266+05:30 [Information] [----------] 6 steps from Simple Mathematical Expressions Demo (1236 ms total)
2025-06-30T09:17:10.8077845+05:30 [Information] [-------------------------------------    Teardown    -------------------------------------]
2025-06-30T09:17:10.8090281+05:30 [Information] [----------] 1 step from Teardown
2025-06-30T09:17:10.8096144+05:30 [Information] [ RUN      ] Teardown > Clean up Database
2025-06-30T09:17:12.7495871+05:30 [Information] [ OK       ]
2025-06-30T09:17:12.7509098+05:30 [Information] [----------] 1 step from Teardown (1941 ms total)
2025-06-30T09:17:12.7518659+05:30 [Information] Global variables set to: curDir: D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc, baseUrl: https://httpbin.org, serverIp: 127.0.0.1, serverPort: 60606, dbInitStatus: initialize_db
2025-06-30T09:17:12.7521179+05:30 [Information] [-------------------------------------  Test Results  -------------------------------------]
2025-06-30T09:17:12.7530626+05:30 [Information] [==========] 1 test from Yaml files ran. (5537 ms total)
2025-06-30T09:17:12.7551723+05:30 [Information] [  PASSED  ] 0 tests.
2025-06-30T09:17:12.7557039+05:30 [Error] [  FAILED  ] 1 test, listed below:
2025-06-30T09:17:12.7563039+05:30 [Error] [  FAILED  ] Simple Mathematical Expressions Demo
