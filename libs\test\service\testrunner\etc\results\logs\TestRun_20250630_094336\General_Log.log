2025-06-30T09:43:36.7148117+05:30 [Information] Loading tests from D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc\examples with pattern *performance-math-advanced.yaml
2025-06-30T09:43:36.7275807+05:30 [Information] Loading test from D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc\examples\performance-math-advanced.yaml
2025-06-30T09:43:36.8158148+05:30 [Information] Global variables set to: 
2025-06-30T09:43:36.8170447+05:30 [Information] [==========] Running 1 test from Yaml files
2025-06-30T09:43:36.8183090+05:30 [Information] [------------------------------------- Test Execution -------------------------------------]
2025-06-30T09:43:36.8379987+05:30 [Information] [----------] 7 steps from Advanced Performance Mathematics
2025-06-30T09:43:36.8474814+05:30 [Information] [ RUN      ] Advanced Performance Mathematics > Database Under Load
2025-06-30T09:43:38.1806122+05:30 [Error] Assert Failure : Less(0, 16.5)
2025-06-30T09:43:38.1819548+05:30 [Error] Database query too slow under load: {{$curStep:performance.executionTimeMs}}ms vs expected {{$var:DatabaseQueryBaseline * $var:LoadFactor + $var:NetworkLatency}}ms
2025-06-30T09:43:38.1821718+05:30 [Error] [ FAILED   ]
2025-06-30T09:43:38.1832560+05:30 [Information] [----------] 7 steps from Advanced Performance Mathematics (1343 ms total)
2025-06-30T09:43:38.1845164+05:30 [Information] [-------------------------------------  Test Results  -------------------------------------]
2025-06-30T09:43:38.1849169+05:30 [Information] [==========] 1 test from Yaml files ran. (1368 ms total)
2025-06-30T09:43:38.1861906+05:30 [Information] [  PASSED  ] 0 tests.
2025-06-30T09:43:38.1865491+05:30 [Error] [  FAILED  ] 1 test, listed below:
2025-06-30T09:43:38.1869230+05:30 [Error] [  FAILED  ] Advanced Performance Mathematics
