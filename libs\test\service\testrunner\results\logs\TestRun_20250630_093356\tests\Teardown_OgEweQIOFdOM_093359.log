2025-06-30T09:33:59.9118686+05:30 [Information] [----------] 1 step from Teardown
2025-06-30T09:33:59.9124248+05:30 [Information] Set Value to Step '8be010c4-5ab6-4c0d-8b34-3cb06fb4ac51' name:
"Clean up Database"
2025-06-30T09:33:59.9125518+05:30 [Information] Set Value to Step '8be010c4-5ab6-4c0d-8b34-3cb06fb4ac51' description:
"Tear down the database state"
2025-06-30T09:33:59.9126233+05:30 [Information] [ RUN      ] Teardown > Clean up Database
2025-06-30T09:33:59.9129139+05:30 [Information] Executing single instance of step 'Clean up Database'.
2025-06-30T09:33:59.9130417+05:30 [Information] Executing step 'Clean up Database' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "8be010c4-5ab6-4c0d-8b34-3cb06fb4ac51",
        "tables": {},
        "variables": {},
        "name": "Clean up Database",
        "description": "Tear down the database state"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:33:59.9132378+05:30 [Information] Processing $var: path 'baseUrl'
2025-06-30T09:33:59.9133196+05:30 [Information] Checking if 'baseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:33:59.9134086+05:30 [Information] Resolving path 'baseUrl' using global variables.
2025-06-30T09:33:59.9134743+05:30 [Information] Resolved JPath '$var:baseUrl' to 'https://httpbin.org'
2025-06-30T09:33:59.9135403+05:30 [Information] Resolved template pattern '{{$var:baseUrl}}' to 'https://httpbin.org' in '{{$var:baseUrl}}/post'
2025-06-30T09:33:59.9135992+05:30 [Information] Resolved template '{{$var:baseUrl}}/post' to 'https://httpbin.org/post'
2025-06-30T09:33:59.9137635+05:30 [Information] Set Value to Step '8be010c4-5ab6-4c0d-8b34-3cb06fb4ac51' input:
{
  "Method": "POST",
  "RequestUri": "https://httpbin.org/post",
  "Headers": {
    "Content-Type": "application/json"
  },
  "Content": {
    "action": "cleanup_db"
  }
}
2025-06-30T09:34:01.2673647+05:30 [Information] Set Value to Step '8be010c4-5ab6-4c0d-8b34-3cb06fb4ac51' output:
{
  "statusCode": 200,
  "reasonPhrase": "OK",
  "headers": [
    {
      "Key": "Date",
      "Value": [
        "Mon, 30 Jun 2025 04:04:06 GMT"
      ]
    },
    {
      "Key": "Connection",
      "Value": [
        "keep-alive"
      ]
    },
    {
      "Key": "Server",
      "Value": [
        "gunicorn/19.9.0"
      ]
    },
    {
      "Key": "Access-Control-Allow-Origin",
      "Value": [
        "*"
      ]
    },
    {
      "Key": "Access-Control-Allow-Credentials",
      "Value": [
        "true"
      ]
    }
  ],
  "content": {
    "args": {},
    "data": "{\r\n  \"action\": \"cleanup_db\"\r\n}",
    "files": {},
    "form": {},
    "headers": {
      "Content-Length": "30",
      "Content-Type": "application/json; charset=utf-8",
      "Host": "httpbin.org",
      "X-Amzn-Trace-Id": "Root=1-68620cb6-088e2ccf6e831f8c38f9c2a3"
    },
    "json": {
      "action": "cleanup_db"
    },
    "origin": "**************",
    "url": "https://httpbin.org/post"
  }
}
2025-06-30T09:34:01.2696888+05:30 [Information] Set Value to Step '8be010c4-5ab6-4c0d-8b34-3cb06fb4ac51' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:03:59.9129117Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:34:01.2704417+05:30 [Information] Set Value to Step '8be010c4-5ab6-4c0d-8b34-3cb06fb4ac51' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 1356,
  "startTime": "2025-06-30T04:03:59.9129117Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:34:01.2709392+05:30 [Information] Step 'Clean up Database' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "8be010c4-5ab6-4c0d-8b34-3cb06fb4ac51",
        "tables": {},
        "variables": {},
        "name": "Clean up Database",
        "description": "Tear down the database state",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "action": "cleanup_db"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:04:06 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"action\": \"cleanup_db\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "30",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620cb6-088e2ccf6e831f8c38f9c2a3"
            },
            "json": {
              "action": "cleanup_db"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 1356,
          "startTime": "2025-06-30T04:03:59.9129117Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:34:01.2712949+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:34:01.2715597+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:34:01.2716031+05:30 [Information] No performance data found for step 'Clean up Database' (http)
2025-06-30T09:34:01.2716576+05:30 [Information] [ OK       ]
2025-06-30T09:34:01.2721511+05:30 [Information] [----------] 1 step from Teardown (1359 ms total)
