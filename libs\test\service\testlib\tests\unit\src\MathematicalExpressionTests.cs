/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using Newtonsoft.Json.Linq;
using NUnit.Framework;
using System;
using System.Collections.Generic;

namespace TestLib.Tests;

[TestFixture]
public class MathematicalExpressionTests
{
    private TestContext _testContext;

    [SetUp]
    public void Setup()
    {
        _testContext = new TestContext();
        _testContext.UpsertName("Mathematical Expression Test");
        
        // Set up test variables
        _testContext.Variables.Upsert("baselineTime", "100");
        _testContext.Variables.Upsert("lightweightTime", "50");
        _testContext.Variables.Upsert("MaxRegressionPercent", "150");
        _testContext.Variables.Upsert("multiplier", "2.5");
        _testContext.Variables.Upsert("zero", "0");
        _testContext.Variables.Upsert("negative", "-10");
    }

    [TestCase("100 * 1.5", "150", TestName = "SimpleMultiplication")]
    [TestCase("100 + 50", "150", TestName = "SimpleAddition")]
    [TestCase("200 - 50", "150", TestName = "SimpleSubtraction")]
    [TestCase("300 / 2", "150", TestName = "SimpleDivision")]
    [TestCase("(100 + 50) * 2", "300", TestName = "ParenthesesExpression")]
    [TestCase("100 * 2 + 50", "250", TestName = "OperatorPrecedence")]
    [TestCase("100 + 50 * 2", "200", TestName = "OperatorPrecedenceReverse")]
    [TestCase("-100 + 250", "150", TestName = "UnaryMinus")]
    [TestCase("+100 + 50", "150", TestName = "UnaryPlus")]
    [TestCase("((100 + 50) * 2) / 2", "150", TestName = "NestedParentheses")]
    public void TestArithmeticExpressions(string expression, string expected)
    {
        // Act
        var result = ContextUtils.EvaluateMathematicalExpression(expression, _testContext);

        // Assert
        Assert.AreEqual(expected, result);
    }

    [TestCase("$var:baselineTime * 1.5", "150", TestName = "VariableMultiplication")]
    [TestCase("$var:baselineTime + $var:lightweightTime", "150", TestName = "TwoVariableAddition")]
    [TestCase("$var:baselineTime * $var:MaxRegressionPercent / 100", "150", TestName = "ComplexVariableExpression")]
    [TestCase("($var:baselineTime + $var:lightweightTime) * 2", "300", TestName = "VariableParentheses")]
    [TestCase("$var:baselineTime * $var:multiplier", "250", TestName = "DecimalVariableMultiplication")]
    [TestCase("$var:baselineTime + $var:negative", "90", TestName = "NegativeVariableAddition")]
    public void TestVariableExpressions(string expression, string expected)
    {
        // Act
        var result = ContextUtils.EvaluateMathematicalExpression(expression, _testContext);

        // Assert
        Assert.AreEqual(expected, result);
    }

    [TestCase("$var:nonexistent * 2", "$var:nonexistent * 2", TestName = "NonExistentVariable")]
    [TestCase("invalid expression", "invalid expression", TestName = "InvalidExpression")]
    [TestCase("100 / $var:zero", "100 / $var:zero", TestName = "DivisionByZeroVariable")]
    [TestCase("100 / 0", "100 / 0", TestName = "DivisionByZeroLiteral")]
    [TestCase("100 + (", "100 + (", TestName = "MismatchedParentheses")]
    [TestCase("100 abc 200", "100 abc 200", TestName = "InvalidCharacters")]
    public void TestErrorCases(string expression, string expected)
    {
        // Act
        var result = ContextUtils.EvaluateMathematicalExpression(expression, _testContext);

        // Assert - Should return original expression when evaluation fails
        Assert.AreEqual(expected, result);
    }

    [Test]
    public void TestStepSpecificVariables()
    {
        // Arrange - Create a step without output to make it the current step
        _testContext.AddStepInput("test_step", new JObject());
        _testContext.SetValueToStep("test_step", "variables", new JObject
        {
            ["stepValue"] = 75
        });
        // Don't add output to make this the current step (first step without output)

        // Act
        var result = ContextUtils.EvaluateMathematicalExpression("$var:stepValue * 2", _testContext);

        // Assert
        Assert.AreEqual("150", result);
    }

    [Test]
    public void TestComplexRealWorldExpression()
    {
        // Arrange - Simulate performance testing scenario
        _testContext.Variables.Upsert("baselineResponseTime", "120");
        _testContext.Variables.Upsert("maxRegressionPercent", "125");
        _testContext.Variables.Upsert("bufferTime", "10");

        // Act - Calculate maximum allowed response time: baseline * (1 + regression%) + buffer
        var result = ContextUtils.EvaluateMathematicalExpression(
            "$var:baselineResponseTime * $var:maxRegressionPercent / 100 + $var:bufferTime",
            _testContext);

        // Assert - 120 * 125 / 100 + 10 = 150 + 10 = 160
        Assert.AreEqual("160", result);
    }

    [Test]
    public void TestAPICallBaselineScalingEfficiencyExpression()
    {
        // Arrange - Test the specific expression: APICallBaseline * ScalingFactor / EfficiencyTarget
        _testContext.Variables.Upsert("APICallBaseline", "120");
        _testContext.Variables.Upsert("ScalingFactor", "2.0");
        _testContext.Variables.Upsert("EfficiencyTarget", "0.8");

        // Act - Calculate scaling efficiency threshold
        var result = ContextUtils.EvaluateMathematicalExpression(
            "APICallBaseline * $var:ScalingFactor / $var:EfficiencyTarget",
            _testContext);

        // Assert - 120 * 2.0 / 0.8 = 240 / 0.8 = 300
        Assert.AreEqual("300", result);
    }

    [Test]
    public void TestMultipleVariableReferencesWithDivision()
    {
        // Arrange - Test complex expression with multiple $var: references and division
        _testContext.Variables.Upsert("APICallBaseline", "100");
        _testContext.Variables.Upsert("ScalingFactor", "1.5");
        _testContext.Variables.Upsert("EfficiencyTarget", "0.75");

        // Act - Test the exact expression pattern from the user's question
        var result = ContextUtils.EvaluateMathematicalExpression(
            "$var:APICallBaseline * $var:ScalingFactor / $var:EfficiencyTarget",
            _testContext);

        // Assert - 100 * 1.5 / 0.75 = 150 / 0.75 = 200
        Assert.AreEqual("200", result);
    }

    [Test]
    public void TestPerformanceScalingCalculations()
    {
        // Arrange - Test various performance scaling scenarios
        _testContext.Variables.Upsert("APICallBaseline", "80");
        _testContext.Variables.Upsert("ScalingFactor", "3.0");
        _testContext.Variables.Upsert("EfficiencyTarget", "0.6");
        _testContext.Variables.Upsert("LoadMultiplier", "2.5");
        _testContext.Variables.Upsert("ConcurrencyPenalty", "1.2");

        // Test Case 1: Basic scaling efficiency
        var result1 = ContextUtils.EvaluateMathematicalExpression(
            "$var:APICallBaseline * $var:ScalingFactor / $var:EfficiencyTarget",
            _testContext);
        // 80 * 3.0 / 0.6 = 240 / 0.6 = 400
        Assert.AreEqual("400", result1);

        // Test Case 2: Complex scaling with load and concurrency
        var result2 = ContextUtils.EvaluateMathematicalExpression(
            "$var:APICallBaseline * $var:LoadMultiplier * $var:ConcurrencyPenalty / $var:EfficiencyTarget",
            _testContext);
        // 80 * 2.5 * 1.2 / 0.6 = 240 / 0.6 = 400
        Assert.AreEqual("400", result2);

        // Test Case 3: Scaling with parentheses for order of operations
        var result3 = ContextUtils.EvaluateMathematicalExpression(
            "($var:APICallBaseline * $var:ScalingFactor) / $var:EfficiencyTarget",
            _testContext);
        // (80 * 3.0) / 0.6 = 240 / 0.6 = 400
        Assert.AreEqual("400", result3);
    }

    [Test]
    public void TestDecimalDivisionPrecision()
    {
        // Arrange - Test decimal division precision
        _testContext.Variables.Upsert("APICallBaseline", "100");
        _testContext.Variables.Upsert("ScalingFactor", "2.0");
        _testContext.Variables.Upsert("EfficiencyTarget", "0.7");

        // Act - Test division that results in decimal
        var result = ContextUtils.EvaluateMathematicalExpression(
            "$var:APICallBaseline * $var:ScalingFactor / $var:EfficiencyTarget",
            _testContext);

        // Assert - 100 * 2.0 / 0.7 = 200 / 0.7 = 285.714285714286 (rounded to reasonable precision)
        var numericResult = double.Parse(result);
        Assert.That(numericResult, Is.EqualTo(285.714285714286).Within(0.000001));
    }
}
