/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using Newtonsoft.Json.Linq;
using NUnit.Framework;
using System;
using System.Collections.Generic;

namespace TestLib.Tests;

[TestFixture]
public class MathematicalExpressionTests
{
    private TestContext _testContext;

    [SetUp]
    public void Setup()
    {
        _testContext = new TestContext();
        _testContext.UpsertName("Mathematical Expression Test");
        
        // Set up test variables
        _testContext.Variables.Upsert("baselineTime", "100");
        _testContext.Variables.Upsert("lightweightTime", "50");
        _testContext.Variables.Upsert("MaxRegressionPercent", "150");
        _testContext.Variables.Upsert("multiplier", "2.5");
        _testContext.Variables.Upsert("zero", "0");
        _testContext.Variables.Upsert("negative", "-10");
    }

    [TestCase("100 * 1.5", "150", TestName = "SimpleMultiplication")]
    [TestCase("100 + 50", "150", TestName = "SimpleAddition")]
    [TestCase("200 - 50", "150", TestName = "SimpleSubtraction")]
    [TestCase("300 / 2", "150", TestName = "SimpleDivision")]
    [TestCase("(100 + 50) * 2", "300", TestName = "ParenthesesExpression")]
    [TestCase("100 * 2 + 50", "250", TestName = "OperatorPrecedence")]
    [TestCase("100 + 50 * 2", "200", TestName = "OperatorPrecedenceReverse")]
    [TestCase("-100 + 250", "150", TestName = "UnaryMinus")]
    [TestCase("+100 + 50", "150", TestName = "UnaryPlus")]
    [TestCase("((100 + 50) * 2) / 2", "150", TestName = "NestedParentheses")]
    public void TestArithmeticExpressions(string expression, string expected)
    {
        // Act
        var result = ContextUtils.EvaluateMathematicalExpression(expression, _testContext);

        // Assert
        Assert.AreEqual(expected, result);
    }

    [TestCase("$var:baselineTime * 1.5", "150", TestName = "VariableMultiplication")]
    [TestCase("$var:baselineTime + $var:lightweightTime", "150", TestName = "TwoVariableAddition")]
    [TestCase("$var:baselineTime * $var:MaxRegressionPercent / 100", "150", TestName = "ComplexVariableExpression")]
    [TestCase("($var:baselineTime + $var:lightweightTime) * 2", "300", TestName = "VariableParentheses")]
    [TestCase("$var:baselineTime * $var:multiplier", "250", TestName = "DecimalVariableMultiplication")]
    [TestCase("$var:baselineTime + $var:negative", "90", TestName = "NegativeVariableAddition")]
    public void TestVariableExpressions(string expression, string expected)
    {
        // Act
        var result = ContextUtils.EvaluateMathematicalExpression(expression, _testContext);

        // Assert
        Assert.AreEqual(expected, result);
    }

    [TestCase("$var:nonexistent * 2", "$var:nonexistent * 2", TestName = "NonExistentVariable")]
    [TestCase("invalid expression", "invalid expression", TestName = "InvalidExpression")]
    [TestCase("100 / $var:zero", "100 / $var:zero", TestName = "DivisionByZeroVariable")]
    [TestCase("100 / 0", "100 / 0", TestName = "DivisionByZeroLiteral")]
    [TestCase("100 + (", "100 + (", TestName = "MismatchedParentheses")]
    [TestCase("100 abc 200", "100 abc 200", TestName = "InvalidCharacters")]
    public void TestErrorCases(string expression, string expected)
    {
        // Act
        var result = ContextUtils.EvaluateMathematicalExpression(expression, _testContext);

        // Assert - Should return original expression when evaluation fails
        Assert.AreEqual(expected, result);
    }

    [Test]
    public void TestStepSpecificVariables()
    {
        // Arrange - Create a step without output to make it the current step
        _testContext.AddStepInput("test_step", new JObject());
        _testContext.SetValueToStep("test_step", "variables", new JObject
        {
            ["stepValue"] = 75
        });
        // Don't add output to make this the current step (first step without output)

        // Act
        var result = ContextUtils.EvaluateMathematicalExpression("$var:stepValue * 2", _testContext);

        // Assert
        Assert.AreEqual("150", result);
    }

    [Test]
    public void TestComplexRealWorldExpression()
    {
        // Arrange - Simulate performance testing scenario
        _testContext.Variables.Upsert("baselineResponseTime", "120");
        _testContext.Variables.Upsert("maxRegressionPercent", "125");
        _testContext.Variables.Upsert("bufferTime", "10");

        // Act - Calculate maximum allowed response time: baseline * (1 + regression%) + buffer
        var result = ContextUtils.EvaluateMathematicalExpression(
            "$var:baselineResponseTime * $var:maxRegressionPercent / 100 + $var:bufferTime", 
            _testContext);

        // Assert - 120 * 125 / 100 + 10 = 150 + 10 = 160
        Assert.AreEqual("160", result);
    }
}
