2025-06-30T09:38:12.4271144+05:30 [Information] [----------] 1 step from Chained Commands with Piping
2025-06-30T09:38:12.4281977+05:30 [Information] Set Value to Step 'bb2fe058-2692-4787-a82f-1cbc5f10d44b' name:
"Step pipe"
2025-06-30T09:38:12.4284842+05:30 [Information] Set Value to Step 'bb2fe058-2692-4787-a82f-1cbc5f10d44b' description:
"Echo and filter output using findstr"
2025-06-30T09:38:12.4286706+05:30 [Information] [ RUN      ] Chained Commands with Piping > Step pipe
2025-06-30T09:38:12.4293758+05:30 [Information] Executing single instance of step 'Step pipe'.
2025-06-30T09:38:12.4296253+05:30 [Information] Executing step 'Step pipe' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "bb2fe058-2692-4787-a82f-1cbc5f10d44b",
        "tables": {},
        "variables": {},
        "name": "Step pipe",
        "description": "Echo and filter output using findstr"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:38:12.4322132+05:30 [Information] Set Value to Step 'bb2fe058-2692-4787-a82f-1cbc5f10d44b' input:
{
  "command": "cmd.exe",
  "arguments": [
    "/c",
    "echo Hello | findstr Hello"
  ],
  "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
  "printOutput": false
}
2025-06-30T09:38:12.4700848+05:30 [Information] Set Value to Step 'bb2fe058-2692-4787-a82f-1cbc5f10d44b' output:
{
  "stdout": "Hello",
  "stderr": "",
  "exitCode": 0
}
2025-06-30T09:38:12.4703356+05:30 [Information] Set Value to Step 'bb2fe058-2692-4787-a82f-1cbc5f10d44b' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:08:12.4293698Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "cmd"
}
2025-06-30T09:38:12.4704773+05:30 [Information] Run Asserter : {"Expression1":{"Value":"Hello","ErrorMesage":null},"Expression2":{"JPath":"$curStep:$.output.stdout","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:38:12.4706387+05:30 [Information] Resolving path '$.output.stdout' using $curStep:
2025-06-30T09:38:12.4707254+05:30 [Information] Resolved JPath '$curStep:$.output.stdout' to 'Hello'
2025-06-30T09:38:12.4708262+05:30 [Information] Set Value to Step 'bb2fe058-2692-4787-a82f-1cbc5f10d44b' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 38,
  "startTime": "2025-06-30T04:08:12.4293698Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "cmd"
}
2025-06-30T09:38:12.4709297+05:30 [Information] Step 'Step pipe' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "bb2fe058-2692-4787-a82f-1cbc5f10d44b",
        "tables": {},
        "variables": {},
        "name": "Step pipe",
        "description": "Echo and filter output using findstr",
        "input": {
          "command": "cmd.exe",
          "arguments": [
            "/c",
            "echo Hello | findstr Hello"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Hello",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 38,
          "startTime": "2025-06-30T04:08:12.4293698Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:38:12.4710165+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:38:12.4710814+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:38:12.4711323+05:30 [Information] No performance data found for step 'Step pipe' (cmd)
2025-06-30T09:38:12.4711789+05:30 [Information] [ OK       ]
2025-06-30T09:38:12.4715035+05:30 [Information] [----------] 1 step from Chained Commands with Piping (43 ms total)
