2025-06-30T09:21:45.0769992+05:30 [Information] [----------] 1 step from Startup
2025-06-30T09:21:45.0791836+05:30 [Information] Set Value to Step '37ecec2c-a0b0-4c7d-817d-7d6208821226' name:
"Initialize Database"
2025-06-30T09:21:45.0794329+05:30 [Information] Set Value to Step '37ecec2c-a0b0-4c7d-817d-7d6208821226' description:
"Setup initial database state"
2025-06-30T09:21:45.0797169+05:30 [Information] [ RUN      ] Startup > Initialize Database
2025-06-30T09:21:45.0807516+05:30 [Information] Executing single instance of step 'Initialize Database'.
2025-06-30T09:21:45.0818674+05:30 [Information] Executing step 'Initialize Database' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "37ecec2c-a0b0-4c7d-817d-7d6208821226",
        "tables": {},
        "variables": {},
        "name": "Initialize Database",
        "description": "Setup initial database state"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:21:45.0835435+05:30 [Information] Processing $var: path 'baseUrl'
2025-06-30T09:21:45.0849632+05:30 [Information] Checking if 'baseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:21:45.0865085+05:30 [Information] Resolving path 'baseUrl' using global variables.
2025-06-30T09:21:45.0867108+05:30 [Information] Resolved JPath '$var:baseUrl' to 'https://httpbin.org'
2025-06-30T09:21:45.0868195+05:30 [Information] Resolved template pattern '{{$var:baseUrl}}' to 'https://httpbin.org' in '{{$var:baseUrl}}/post'
2025-06-30T09:21:45.0871022+05:30 [Information] Resolved template '{{$var:baseUrl}}/post' to 'https://httpbin.org/post'
2025-06-30T09:21:45.0892716+05:30 [Information] Set Value to Step '37ecec2c-a0b0-4c7d-817d-7d6208821226' input:
{
  "Method": "POST",
  "RequestUri": "https://httpbin.org/post",
  "Headers": {
    "Content-Type": "application/json"
  },
  "Content": {
    "action": "initialize_db"
  }
}
2025-06-30T09:21:46.4002037+05:30 [Information] Set Value to Step '37ecec2c-a0b0-4c7d-817d-7d6208821226' output:
{
  "statusCode": 200,
  "reasonPhrase": "OK",
  "headers": [
    {
      "Key": "Date",
      "Value": [
        "Mon, 30 Jun 2025 03:51:51 GMT"
      ]
    },
    {
      "Key": "Connection",
      "Value": [
        "keep-alive"
      ]
    },
    {
      "Key": "Server",
      "Value": [
        "gunicorn/19.9.0"
      ]
    },
    {
      "Key": "Access-Control-Allow-Origin",
      "Value": [
        "*"
      ]
    },
    {
      "Key": "Access-Control-Allow-Credentials",
      "Value": [
        "true"
      ]
    }
  ],
  "content": {
    "args": {},
    "data": "{\r\n  \"action\": \"initialize_db\"\r\n}",
    "files": {},
    "form": {},
    "headers": {
      "Content-Length": "33",
      "Content-Type": "application/json; charset=utf-8",
      "Host": "httpbin.org",
      "X-Amzn-Trace-Id": "Root=1-686209d7-402ca58b722dfe932274aed6"
    },
    "json": {
      "action": "initialize_db"
    },
    "origin": "**************",
    "url": "https://httpbin.org/post"
  }
}
2025-06-30T09:21:46.4007384+05:30 [Information] Resolving path 'output.content.json.action' using step input/output.
2025-06-30T09:21:46.4009399+05:30 [Information] Resolved JPath 'output.content.json.action' to 'initialize_db'
2025-06-30T09:21:46.4019757+05:30 [Information] Set Value to Step '37ecec2c-a0b0-4c7d-817d-7d6208821226' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T03:51:45.0807272Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:21:46.4044280+05:30 [Information] Run Asserter : {"Expression1":{"JPath":"$curStep:output.content.json.action","ErrorMesage":null},"Expression2":{"Value":null,"ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:21:46.4047605+05:30 [Information] Resolving path 'output.content.json.action' using $curStep:
2025-06-30T09:21:46.4048909+05:30 [Information] Resolved JPath '$curStep:output.content.json.action' to 'initialize_db'
2025-06-30T09:21:46.4062105+05:30 [Information] Error in EqualExpression: The method or operation is not implemented.
2025-06-30T09:21:46.4070998+05:30 [Information] Set Value to Step '37ecec2c-a0b0-4c7d-817d-7d6208821226' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 1316,
  "startTime": "2025-06-30T03:51:45.0807272Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:21:46.4072745+05:30 [Information] Step 'Initialize Database' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "37ecec2c-a0b0-4c7d-817d-7d6208821226",
        "tables": {},
        "variables": {},
        "name": "Initialize Database",
        "description": "Setup initial database state",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "action": "initialize_db"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 03:51:51 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"action\": \"initialize_db\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "33",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-686209d7-402ca58b722dfe932274aed6"
            },
            "json": {
              "action": "initialize_db"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 1316,
          "startTime": "2025-06-30T03:51:45.0807272Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:21:46.4074456+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:21:46.4075746+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:21:46.4076367+05:30 [Information] No performance data found for step 'Initialize Database' (http)
2025-06-30T09:21:46.4079343+05:30 [Information] [ OK       ]
2025-06-30T09:21:46.4091752+05:30 [Information] [----------] 1 step from Startup (1330 ms total)
