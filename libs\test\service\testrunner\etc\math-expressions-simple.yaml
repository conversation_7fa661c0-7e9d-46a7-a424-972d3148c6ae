Name: "Simple Mathematical Expressions Demo"
Description: "Demonstrates mathematical expression functionality using CMD steps for reliable timing"

Variables:
  BaselineThreshold: "100"
  MaxRegressionPercent: "150"  # 150% = 50% increase allowed
  BufferTime: "50"
  FastThreshold: "5000"  # 5 seconds - very generous for ping
  SlowThreshold: "10000"
  MathBaseline: "100"  # Use 100ms as baseline for mathematical demonstrations

Steps:
  # Step 1: Fast operation to establish baseline
  - Name: "Fast Baseline Operation"
    Type: "cmd"
    Input:
      Command: "ping"
      Arguments:
        - "127.0.0.1"
        - "-n"
        - "1"
    Execution:
      DelayMs: 50
    Asserters:
      # Should complete successfully
      - AssertEq:
          ConstExpr: 0
          JPathExpr: "$curStep:$.output.exitCode"
          ErrorMessage: "Ping command failed"

      # Validate performance data is available (can be 0 for very fast operations)
      - AssertGte:
          ConstExpr: 0
          JPathExpr: "$curStep:performance.executionTimeMs"
          ErrorMessage: "Performance data not available"
    Output:
      Store:
        # Store actual execution time (even if 0) for mathematical expressions
        actualBaseline: "performance.executionTimeMs"

  # Step 2: Test mathematical expression for regression threshold
  - Name: "Regression Threshold Test"
    Type: "cmd"
    Input:
      Command: "ping"
      Arguments:
        - "127.0.0.1"
        - "-n"
        - "2"
    Execution:
      DelayMs: 100
    Asserters:
      # Should complete successfully
      - AssertEq:
          ConstExpr: 0
          JPathExpr: "$curStep:$.output.exitCode"
          ErrorMessage: "Ping command failed"

      # Demonstrate mathematical expression: baseline * regression percentage
      # Using MathBaseline (100ms) for reliable mathematical demonstrations
      - AssertLt:
          ConstExpr: "{{$var:MathBaseline * $var:MaxRegressionPercent / 100}}"
          JPathExpr: "$curStep:performance.executionTimeMs"
          ErrorMessage: "Performance regression detected: {{$curStep:performance.executionTimeMs}}ms vs allowed {{$var:MathBaseline * $var:MaxRegressionPercent / 100}}ms ({{$var:MathBaseline}}ms * {{$var:MaxRegressionPercent}}% / 100)"

  # Step 3: Complex mathematical expression with buffer
  - Name: "Complex Expression Test"
    Type: "cmd"
    Input:
      Command: "ping"
      Arguments:
        - "127.0.0.1"
        - "-n"
        - "1"
    Execution:
      DelayMs: 150
    Asserters:
      # Should complete successfully
      - AssertEq:
          ConstExpr: 0
          JPathExpr: "$curStep:$.output.exitCode"
          ErrorMessage: "Ping command failed"
      
      # Complex expression: baseline * regression% + buffer
      - AssertLt:
          ConstExpr: "{{$var:MathBaseline * $var:MaxRegressionPercent / 100 + $var:BufferTime}}"
          JPathExpr: "$curStep:performance.executionTimeMs"
          ErrorMessage: "Complex threshold exceeded: {{$curStep:performance.executionTimeMs}}ms vs {{$var:MathBaseline * $var:MaxRegressionPercent / 100 + $var:BufferTime}}ms ({{$var:MathBaseline}} * {{$var:MaxRegressionPercent}}% / 100 + {{$var:BufferTime}})"

  # Step 4: Demonstrate parentheses and operator precedence
  - Name: "Operator Precedence Test"
    Type: "cmd"
    Input:
      Command: "echo"
      Arguments:
        - "Testing operator precedence"
    Execution:
      DelayMs: 75
    Asserters:
      # Should complete successfully
      - AssertEq:
          ConstExpr: 0
          JPathExpr: "$curStep:$.output.exitCode"
          ErrorMessage: "Echo command failed"
      
      # Expression with parentheses: (baseline + buffer) * 2
      - AssertLt:
          ConstExpr: "{{$var:(MathBaseline + $var:BufferTime) * 2}}"
          JPathExpr: "$curStep:performance.executionTimeMs"
          ErrorMessage: "Parentheses test failed: {{$curStep:performance.executionTimeMs}}ms vs {{$var:(MathBaseline + $var:BufferTime) * 2}}ms = ({{$var:MathBaseline}} + {{$var:BufferTime}}) * 2"

      # Expression without parentheses: baseline + buffer * 2 (different result due to operator precedence)
      - AssertLt:
          ConstExpr: "{{$var:MathBaseline + $var:BufferTime * 2}}"
          JPathExpr: "$curStep:performance.executionTimeMs"
          ErrorMessage: "Precedence test failed: {{$curStep:performance.executionTimeMs}}ms vs {{$var:MathBaseline + $var:BufferTime * 2}}ms = {{$var:MathBaseline}} + {{$var:BufferTime}} * 2"

  # Step 5: Performance budgeting scenario
  - Name: "Performance Budget Test"
    Variables:
      # Local variables for this step
      CriticalPath: "500"  # 500ms
      OptimizationFactor: "0.8"  # 20% improvement expected
    Type: "cmd"
    Input:
      Command: "ping"
      Arguments:
        - "127.0.0.1"
        - "-n"
        - "1"
    Execution:
      DelayMs: 200
    Asserters:
      # Should complete successfully
      - AssertEq:
          ConstExpr: 0
          JPathExpr: "$curStep:$.output.exitCode"
          ErrorMessage: "Ping command failed"
      
      # Budget calculation: critical path * optimization factor
      - AssertLt:
          ConstExpr: "{{$var:CriticalPath * $var:OptimizationFactor}}"
          JPathExpr: "$curStep:performance.executionTimeMs"
          ErrorMessage: "Performance budget exceeded: {{$curStep:performance.executionTimeMs}}ms vs budget {{$var:CriticalPath * $var:OptimizationFactor}}ms"
      
      # Fallback to absolute limit if optimization not achieved
      - AssertLt:
          ConstExpr: "{{$var:CriticalPath}}"
          JPathExpr: "$curStep:performance.executionTimeMs"
          ErrorMessage: "Absolute performance limit exceeded: {{$curStep:performance.executionTimeMs}}ms vs {{$var:CriticalPath}}ms"

  # Step 6: Demonstrate mathematical expressions in error messages
  - Name: "Error Message Demo"
    Type: "cmd"
    Input:
      Command: "echo"
      Arguments:
        - "Mathematical expressions work in error messages too!"
    Execution:
      DelayMs: 50
    Asserters:
      # Should complete successfully
      - AssertEq:
          ConstExpr: 0
          JPathExpr: "$curStep:$.output.exitCode"
          ErrorMessage: "Echo command failed"
      
      # This should work - valid expression demonstrating mathematical expressions in error messages
      - AssertLt:
          ConstExpr: "{{$var:MathBaseline * 3}}"
          JPathExpr: "$curStep:performance.executionTimeMs"
          ErrorMessage: "Performance test failed: {{$curStep:performance.executionTimeMs}}ms exceeded {{$var:MathBaseline * 3}}ms (3x baseline of {{$var:MathBaseline}}ms)"
