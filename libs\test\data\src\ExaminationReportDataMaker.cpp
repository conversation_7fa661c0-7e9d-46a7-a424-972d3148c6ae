/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

#include "ExaminationReportDataMaker.h"

namespace testdatamaker {

// -------------------------------------------------------------------------------------------------

std::optional<type::ExaminationReport>
ExaminationReportDataMaker::make(const examinationreportdatamaker_config_t& config) {
    if (!config.exists) {
        return std::nullopt;
    }

    switch (config.policy) {
    case coretypesdm::GeneratingPolicy::RANDOM:
        return makeRandom(config.params);
    case coretypesdm::GeneratingPolicy::BASED_ON:
        return makeBasedOn(config.seed, config.params);
    case coretypesdm::GeneratingPolicy::PARTIAL:
        return makeBasedOn(std::nullopt, config.params);
    default:
        return std::nullopt;
    }
}

// -------------------------------------------------------------------------------------------------

std::optional<type::ExaminationReport> 
ExaminationReportDataMaker::make(const erdmp::ExaminationReportDataMakerConfig& config) {
    return make(ProtoUtils::fromProtoConfig(config));
}

// -------------------------------------------------------------------------------------------------

type::ExaminationReport
ExaminationReportDataMaker::makeRandom(const erdmp::ExaminationReportDataMakerParams& params) {
    type::ExaminationReport report;

    if (params.has_revisionargs()) {
        *report.mutable_revision() = ProtoUtils::makeRevision(params.revisionargs());
    } else {
        *report.mutable_revision() = ProtoUtils::makeRevision(std::nullopt);
    }

    if (params.has_description()) {
        report.set_description(params.description());
    } else {
        report.set_description(RandomUtils::makeRandomString(30));
    }

    if (params.has_notesargs()) {
        ctu::assign(report.mutable_notes(), ProtoUtils::makeNotes(params.notesargs()));
    } else {
        ctu::assign(report.mutable_notes(), ProtoUtils::makeNotes(std::nullopt));
    }

    if (params.has_patientconfig()) {
        auto maybePatient = pddm::make(params.patientconfig());
        if (maybePatient.has_value()) {
            *report.mutable_patientref() = pddm::makeRef(maybePatient.value()).value();
        }
    } else {
        auto maybePatient = pddm::make(pddm::patientdatamaker_config_t{});
        if (maybePatient.has_value()) {
            *report.mutable_patientref() = pddm::makeRef(maybePatient.value()).value();
        }
    }

    type::cataract::EyeContext eyeContext = makeEyeContext(params.eyecontextargs());

    if (params.has_anteriorkeratometryconfig()) {
        auto maybeAnteriorKeratometry = KeratometryDataMaker::make(params.anteriorkeratometryconfig());
        if (maybeAnteriorKeratometry.has_value()) {
            *report.mutable_anteriorkeratometry() = maybeAnteriorKeratometry.value();
        }
    } else {
        auto maybeAnteriorKeratometry = KeratometryDataMaker::make(kdm::keratometrydatamaker_config_t{});
        if (maybeAnteriorKeratometry.has_value()) {
            *report.mutable_anteriorkeratometry() = maybeAnteriorKeratometry.value();
        }
    }

    if (params.has_posteriorkeratometryconfig()) {
        auto maybePosteriorKeratometry = KeratometryDataMaker::make(params.posteriorkeratometryconfig());
        if (maybePosteriorKeratometry.has_value()) {
            *report.mutable_posteriorkeratometry() = maybePosteriorKeratometry.value();
        }
    } else {
        auto maybePosteriorKeratometry = KeratometryDataMaker::make(kdm::keratometrydatamaker_config_t{});
        if (maybePosteriorKeratometry.has_value()) {
            *report.mutable_posteriorkeratometry() = maybePosteriorKeratometry.value();
        }
    }

    if (params.has_totalkeratometryconfig()) {
        auto maybeTotalKeratometry = KeratometryDataMaker::make(params.totalkeratometryconfig());
        if (maybeTotalKeratometry.has_value()) {
            *report.mutable_totalkeratometry() = maybeTotalKeratometry.value();
        }
    } else {
        auto maybeTotalKeratometry = KeratometryDataMaker::make(kdm::keratometrydatamaker_config_t{});
        if (maybeTotalKeratometry.has_value()) {
            *report.mutable_totalkeratometry() = maybeTotalKeratometry.value();
        }
    }

    //  TODO: ad support for alternativeAnteriorKeratometry

    if (params.has_axialbiometryconfig()) {
        auto maybeAxialBiometry = abdm::make(params.axialbiometryconfig());
        if (maybeAxialBiometry.has_value()) {
            *report.mutable_axialbiometry() = maybeAxialBiometry.value();
        }
    } else {
        auto maybeAxialBiometry = abdm::make(abdm::axialbiometrydatamaker_config_t{});
        if (maybeAxialBiometry.has_value()) {
            *report.mutable_axialbiometry() = maybeAxialBiometry.value();
        }
    }

    if (params.has_enfacebiometryconfig()) {
        auto maybeEnFaceBiometry = efbdm::make(params.enfacebiometryconfig());
        if (maybeEnFaceBiometry.has_value()) {
            *report.mutable_enfacebiometry() = maybeEnFaceBiometry.value();
        }
    } else {
        auto maybeEnFaceBiometry = efbdm::make(efbdm::enfacebiometrydatamaker_config_t{});
        if (maybeEnFaceBiometry.has_value()) {
            *report.mutable_enfacebiometry() = maybeEnFaceBiometry.value();
        }
    }

    if (params.has_refractionconfig()) {
        auto maybeRefraction = rdm::make(params.refractionconfig());
        if (maybeRefraction.has_value()) {
            *report.mutable_refraction() = maybeRefraction.value();
        }
    } else {
        auto maybeRefraction = rdm::make(rdm::refractiondatamaker_config_t{});
        if (maybeRefraction.has_value()) {
            *report.mutable_refraction() = maybeRefraction.value();
        }
    }

    if (params.has_referenceimage()) {
        *report.mutable_referenceimage() = params.referenceimage();
    } else {
        *report.mutable_referenceimage() = type::ReferenceImage{};
    }

    switch (params.device()) {
        case erdmp::DeviceEnum::METIOR:
        *report.mutable_metior() = makeMetior(params.metiorargs());
        break;
        case erdmp::DeviceEnum::ARGOS :
        *report.mutable_argos() = makeArgos(params.argosargs());
        break;
        case erdmp::DeviceEnum::NONE :
        break;
    }

    return report;
}

// -------------------------------------------------------------------------------------------------

type::ExaminationReport
ExaminationReportDataMaker::makeBasedOn(
    const std::optional<type::ExaminationReport>& seed, const erdmp::ExaminationReportDataMakerParams& params) {

    type::ExaminationReport report = seed.value_or(type::ExaminationReport{});

    if (params.has_revisionargs()) {
        *report.mutable_revision() = ProtoUtils::makeRevision(params.revisionargs());
    }

    if (params.has_description()) {
        report.set_description(params.description());
    }

    if (params.has_notesargs()) {
        ctu::assign(report.mutable_notes(), ProtoUtils::makeNotes(params.notesargs()));
    }

    if (params.has_patientconfig()) {
        auto config = params.patientconfig();
        *config.mutable_seed() = report.patientref().value();
        config.set_policy(coretypesdm::GeneratingPolicy::BASED_ON);
        auto maybePatient = pddm::make(config);
        if (maybePatient.has_value()) {
            *report.mutable_patientref() = pddm::makeRef(maybePatient.value()).value();
        }
    }

    if (params.has_eyecontextargs()) {
        *report.mutable_eyecontext() = makeEyeContext(params.eyecontextargs());
    }

    if (params.has_anteriorkeratometryconfig()) {
        auto config = params.anteriorkeratometryconfig();
        config.set_policy(coretypesdm::GeneratingPolicy::BASED_ON);
        *config.mutable_seed() = seed->anteriorkeratometry();

        auto maybeAnteriorKeratometry = KeratometryDataMaker::make(config);
        if (maybeAnteriorKeratometry.has_value()) {
            *report.mutable_anteriorkeratometry() = maybeAnteriorKeratometry.value();
        }
    }

    if (params.has_posteriorkeratometryconfig()) {
        auto config = params.posteriorkeratometryconfig();
        config.set_policy(coretypesdm::GeneratingPolicy::BASED_ON);
        *config.mutable_seed() = seed->posteriorkeratometry();

        auto maybePosteriorKeratometry = KeratometryDataMaker::make(config);
        if (maybePosteriorKeratometry.has_value()) {
            *report.mutable_posteriorkeratometry() = maybePosteriorKeratometry.value();
        }
    }

    if (params.has_totalkeratometryconfig()) {
        auto config = params.totalkeratometryconfig();
        config.set_policy(coretypesdm::GeneratingPolicy::BASED_ON);
        *config.mutable_seed() = seed->totalkeratometry();

        auto maybeTotalKeratometry = KeratometryDataMaker::make(config);
        if (maybeTotalKeratometry.has_value()) {
            *report.mutable_totalkeratometry() = maybeTotalKeratometry.value();
        }
    }

    //  TODO: ad support for alternativeAnteriorKeratometry

    if (params.has_axialbiometryconfig()) {
        auto config = params.axialbiometryconfig();
        config.set_policy(coretypesdm::GeneratingPolicy::BASED_ON);
        *config.mutable_seed() = seed->axialbiometry();

        auto maybeAxialBiometry = abdm::make(config);
        if (maybeAxialBiometry.has_value()) {
            *report.mutable_axialbiometry() = maybeAxialBiometry.value();
        }
    }

    if (params.has_enfacebiometryconfig()) {
        auto config = params.enfacebiometryconfig();
        config.set_policy(coretypesdm::GeneratingPolicy::BASED_ON);
        *config.mutable_seed() = seed->enfacebiometry();

        auto maybeEnFaceBiometry = efbdm::make(config);
        if (maybeEnFaceBiometry.has_value()) {
            *report.mutable_enfacebiometry() = maybeEnFaceBiometry.value();
        }
    }

    if (params.has_refractionconfig()) {
        auto config = params.refractionconfig();
        config.set_policy(coretypesdm::GeneratingPolicy::BASED_ON);
        *config.mutable_seed() = seed->refraction();

        auto maybeRefraction = rdm::make(config);
        if (maybeRefraction.has_value()) {
            *report.mutable_refraction() = maybeRefraction.value();
        }
    }

    if (params.has_referenceimage()) {
        *report.mutable_referenceimage() = params.referenceimage();
    }

    switch (params.device()) {
        case erdmp::DeviceEnum::METIOR :
        *report.mutable_metior() = makeMetior(params.metiorargs());
        break;
        case erdmp::DeviceEnum::ARGOS :
        *report.mutable_argos() = makeArgos(params.argosargs());
        break;
        case erdmp::DeviceEnum::NONE :
        break;
    }

    return report;
}

// -------------------------------------------------------------------------------------------------

std::vector<type::ExaminationReport>
ExaminationReportDataMaker::extractExaminationReportFromDb(const mpdb::InMultiProtobufDb& data) {
    return ProtoUtils::extractMessagesFromDb<type::ExaminationReport>(data);
}

// -------------------------------------------------------------------------------------------------

bool
ExaminationReportDataMaker::pairExaminationReports(
    type::ExaminationReport& _odExaminationReport,
    type::ExaminationReport& _osExaminationReport) {

    auto& odExamReportId = _odExaminationReport.revision().id();
    auto& osExamReportId = _osExaminationReport.revision().id();

    *_osExaminationReport.mutable_pairedexaminationid() = odExamReportId;
    *_odExaminationReport.mutable_pairedexaminationid() = osExamReportId;

    // TODO: Treat cases when it can fail and return false

    return true;
}

// -------------------------------------------------------------------------------------------------

type::cataract::EyeContext
ExaminationReportDataMaker::makeEyeContext(const std::optional<erdmp::EyeContextArgs>& makeEyeContextArgs) {

    // TODO: Check if data should be randomized and randomize it when we know the restrictions.
    // For now, just copy the data from seed
    if (makeEyeContextArgs.has_value()) {
        return makeEyeContextArgs.value().eyecontextseed();
    }

    return type::cataract::EyeContext();
}

// -------------------------------------------------------------------------------------------------

type::devices::ArgosExamination
ExaminationReportDataMaker::makeArgos(const std::optional<erdmp::ArgosArgs>& argosArgs) {

    // TODO: Check if data should be randomized and randomize it when we know the restrictions.
    // For now, just copy the data from seed
    if (argosArgs.has_value()) {
        return argosArgs.value().argosexaminationseed();
    }
    return type::devices::ArgosExamination();
}

// -------------------------------------------------------------------------------------------------

type::devices::MetiorExamination
ExaminationReportDataMaker::makeMetior(const  std::optional<erdmp::MetiorArgs>& metiorArgs) {

    // TODO: Check if data should be randomized and randomize it when we know the restrictions.
    // For now, just copy the data from seed
    if (metiorArgs.has_value()) {
        return metiorArgs.value().metiorexaminationseed();
    }
    return type::devices::MetiorExamination();
}

// -------------------------------------------------------------------------------------------------

} // testdatamaker
