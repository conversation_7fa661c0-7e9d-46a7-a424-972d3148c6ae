syntax = "proto3";

package testdatamaker.subjectiveexaminationreportdatamaker;

import "RefractionDataMaker.proto";
import "VisualAcuityDataMaker.proto";
import "ProtoUtils.proto";
import "PatientDataMaker.proto";
import "CoreTypesDataMaker.proto";
import "alcon/interop/type/Reports.proto";

message RefractionArgs {
    int32 count = 1;
    optional refractiondatamaker.RefractionConfig refractionConfig = 2;
}

message BcvasArgs {
    int32 count = 1;
    optional visualacuitydatamaker.VisualAcuityParams bcvaParams = 2;
}

message UcvasArgs {
    int32 count = 1;
    optional visualacuitydatamaker.VisualAcuityParams ucvaParams = 2;
}

message VisualAcuityAdditionalAssesmentArgs {
    int32 count = 1;
    optional visualacuitydatamaker.VisualAcuityParams visualAcuityParams = 2;
}

message SubjectiveExaminationReportParams {
    optional protoutils.RevisionArgs revisionArgs = 1;
    optional string description = 2;
    optional protoutils.NotesArgs notesArgs = 3;
    optional patientdatamaker.PatientParams patientArgs = 4;
    optional RefractionArgs refractionArgs = 5;
    optional BcvasArgs bcvaArgs = 6;
    optional UcvasArgs ucvaArgs = 7;
    optional VisualAcuityAdditionalAssesmentArgs visualAcuityAdditionalAssesmentArgs = 8;
}

message SubjectiveExaminationReportConfig {
  coretypes.GeneratingPolicy policy = 1;
  optional bool exists = 2;
  SubjectiveExaminationReportParams params = 3;
  alcon.interop.type.SubjectiveExaminationReport seed = 4;
}