2025-06-30T09:38:12.7206864+05:30 [Information] [----------] 1 step from Non-Zero Exit Code Test
2025-06-30T09:38:12.7215641+05:30 [Information] Set Value to Step 'ddaeb4d6-fd69-4f2c-8adf-e1adff6a6ef4' name:
"Step exit failure"
2025-06-30T09:38:12.7217108+05:30 [Information] Set Value to Step 'ddaeb4d6-fd69-4f2c-8adf-e1adff6a6ef4' description:
"Command exits with code 1"
2025-06-30T09:38:12.7217963+05:30 [Information] [ RUN      ] Non-Zero Exit Code Test > Step exit failure
2025-06-30T09:38:12.7221794+05:30 [Information] Executing single instance of step 'Step exit failure'.
2025-06-30T09:38:12.7223214+05:30 [Information] Executing step 'Step exit failure' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "ddaeb4d6-fd69-4f2c-8adf-e1adff6a6ef4",
        "tables": {},
        "variables": {},
        "name": "Step exit failure",
        "description": "Command exits with code 1"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:38:12.7225684+05:30 [Information] Set Value to Step 'ddaeb4d6-fd69-4f2c-8adf-e1adff6a6ef4' input:
{
  "command": "cmd.exe",
  "arguments": [
    "/c",
    "exit 1"
  ],
  "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
  "printOutput": false
}
2025-06-30T09:38:12.7564196+05:30 [Information] Set Value to Step 'ddaeb4d6-fd69-4f2c-8adf-e1adff6a6ef4' output:
{
  "stdout": "",
  "stderr": "",
  "exitCode": 1
}
2025-06-30T09:38:12.7565970+05:30 [Information] Set Value to Step 'ddaeb4d6-fd69-4f2c-8adf-e1adff6a6ef4' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:08:12.7221767Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "cmd"
}
2025-06-30T09:38:12.7567112+05:30 [Information] Run Asserter : {"Expression1":{"Value":"1","ErrorMesage":null},"Expression2":{"JPath":"$curStep:$.output.exitCode","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:38:12.7567873+05:30 [Information] Resolving path '$.output.exitCode' using $curStep:
2025-06-30T09:38:12.7568769+05:30 [Information] Resolved JPath '$curStep:$.output.exitCode' to '1'
2025-06-30T09:38:12.7570219+05:30 [Information] Set Value to Step 'ddaeb4d6-fd69-4f2c-8adf-e1adff6a6ef4' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 34,
  "startTime": "2025-06-30T04:08:12.7221767Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "cmd"
}
2025-06-30T09:38:12.7571194+05:30 [Information] Step 'Step exit failure' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "ddaeb4d6-fd69-4f2c-8adf-e1adff6a6ef4",
        "tables": {},
        "variables": {},
        "name": "Step exit failure",
        "description": "Command exits with code 1",
        "input": {
          "command": "cmd.exe",
          "arguments": [
            "/c",
            "exit 1"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "",
          "stderr": "",
          "exitCode": 1
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 34,
          "startTime": "2025-06-30T04:08:12.7221767Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:38:12.7571982+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:38:12.7572556+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:38:12.7572966+05:30 [Information] No performance data found for step 'Step exit failure' (cmd)
2025-06-30T09:38:12.7573396+05:30 [Information] [ OK       ]
2025-06-30T09:38:12.7576540+05:30 [Information] [----------] 1 step from Non-Zero Exit Code Test (36 ms total)
