2025-06-30T09:37:35.5182613+05:30 [Information] [----------] 1 step from Demo HTTP Table Test Iteration
2025-06-30T09:37:35.5228340+05:30 [Information] Default-mapped column 'User' to variable 'User' with value '{
  "Username": "<PERSON>",
  "Email": "<EMAIL>"
}'
2025-06-30T09:37:35.5229624+05:30 [Information] Default-mapped column 'OrgName' to variable 'OrgName' with value 'Org1'
2025-06-30T09:37:35.5230406+05:30 [Information] Processing $var: path 'User.Username'
2025-06-30T09:37:35.5230923+05:30 [Information] Checking if 'User.Username' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=True, result=False
2025-06-30T09:37:35.5231350+05:30 [Information] Resolving path 'User.Username' using global variables.
2025-06-30T09:37:35.5233611+05:30 [Information] Resolved JPath '$var:User.Username' to 'Tom'
2025-06-30T09:37:35.5234716+05:30 [Information] Resolved template pattern '{{$var:User.Username}}' to 'Tom' in '{{$var:User.Username}}'
2025-06-30T09:37:35.5235270+05:30 [Information] Resolved template '{{$var:User.Username}}' to 'Tom'
2025-06-30T09:37:35.5236254+05:30 [Information] Mapped column '{{$var:User.Username}}' to variable 'username' with value 'Tom'
2025-06-30T09:37:35.5236800+05:30 [Information] Processing $var: path 'User.Email'
2025-06-30T09:37:35.5237210+05:30 [Information] Checking if 'User.Email' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=True, result=False
2025-06-30T09:37:35.5237730+05:30 [Information] Resolving path 'User.Email' using global variables.
2025-06-30T09:37:35.5238506+05:30 [Information] Resolved JPath '$var:User.Email' to '<EMAIL>'
2025-06-30T09:37:35.5239458+05:30 [Information] Resolved template pattern '{{$var:User.Email}}' to '<EMAIL>' in '{{$var:User.Email}}'
2025-06-30T09:37:35.5240114+05:30 [Information] Resolved template '{{$var:User.Email}}' to '<EMAIL>'
2025-06-30T09:37:35.5241404+05:30 [Information] Mapped column '{{$var:User.Email}}' to variable 'email' with value '<EMAIL>'
2025-06-30T09:37:35.5243509+05:30 [Information] Test Iteration 1/2: Demo HTTP Table Test Iteration
2025-06-30T09:37:35.5244833+05:30 [Information] Processing $var: path 'username'
2025-06-30T09:37:35.5245279+05:30 [Information] Checking if 'username' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:35.5245677+05:30 [Information] Resolving path 'username' using global variables.
2025-06-30T09:37:35.5246075+05:30 [Information] Resolved JPath '$var:username' to 'Tom'
2025-06-30T09:37:35.5246608+05:30 [Information] Resolved template pattern '{{$var:username}}' to 'Tom' in 'Fetching user {{$var:username}} details'
2025-06-30T09:37:35.5247026+05:30 [Information] Resolved template 'Fetching user {{$var:username}} details' to 'Fetching user Tom details'
2025-06-30T09:37:35.5247730+05:30 [Information] Set Value to Step 'e21acc45-7e81-4365-a380-e8546e61c141' name:
"Fetching user Tom details"
2025-06-30T09:37:35.5248264+05:30 [Information] Set Value to Step 'e21acc45-7e81-4365-a380-e8546e61c141' description:
"Run step for each user"
2025-06-30T09:37:35.5248663+05:30 [Information] [ RUN      ] Demo HTTP Table Test Iteration > Fetching user Tom details
2025-06-30T09:37:35.5252399+05:30 [Information] Executing single instance of step 'Fetching user {{$var:username}} details'.
2025-06-30T09:37:35.5254654+05:30 [Information] Executing step 'Fetching user Tom details' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "e21acc45-7e81-4365-a380-e8546e61c141",
        "tables": {},
        "variables": {},
        "name": "Fetching user Tom details",
        "description": "Run step for each user"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaseUrl": "https://httpbin.org",
      "User": {
        "Username": "Tom",
        "Email": "<EMAIL>"
      },
      "OrgName": "Org1",
      "username": "Tom",
      "email": "<EMAIL>"
    },
    "tables": {
      "UserTable": [
        {
          "User": {
            "Username": "Tom",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org1"
        },
        {
          "User": {
            "Username": "John",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org2"
        }
      ]
    },
    "context": {
      "repeatForTable": "UserTable",
      "row": {
        "User": {
          "Username": "Tom",
          "Email": "<EMAIL>"
        },
        "OrgName": "Org1"
      },
      "rowIndex": 0
    }
  }
}
2025-06-30T09:37:35.5258199+05:30 [Information] Processing $var: path 'BaseUrl'
2025-06-30T09:37:35.5260072+05:30 [Information] Checking if 'BaseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:35.5262231+05:30 [Information] Resolving path 'BaseUrl' using global variables.
2025-06-30T09:37:35.5263081+05:30 [Information] Resolved JPath '$var:BaseUrl' to 'https://httpbin.org'
2025-06-30T09:37:35.5263594+05:30 [Information] Resolved template pattern '{{$var:BaseUrl}}' to 'https://httpbin.org' in '{{$var:BaseUrl}}/post'
2025-06-30T09:37:35.5264058+05:30 [Information] Resolved template '{{$var:BaseUrl}}/post' to 'https://httpbin.org/post'
2025-06-30T09:37:35.5264797+05:30 [Information] Processing $var: path 'username'
2025-06-30T09:37:35.5265221+05:30 [Information] Checking if 'username' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:35.5265711+05:30 [Information] Resolving path 'username' using global variables.
2025-06-30T09:37:35.5266427+05:30 [Information] Resolved JPath '$var:username' to 'Tom'
2025-06-30T09:37:35.5267146+05:30 [Information] Resolved template pattern '{{$var:username}}' to 'Tom' in '{
  "username": "{{$var:username}}",
  "email": "{{$var:email}}",
  "org": "{{$var:OrgName}}"
}'
2025-06-30T09:37:35.5267899+05:30 [Information] Processing $var: path 'email'
2025-06-30T09:37:35.5268640+05:30 [Information] Checking if 'email' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:35.5269533+05:30 [Information] Resolving path 'email' using global variables.
2025-06-30T09:37:35.5270514+05:30 [Information] Resolved JPath '$var:email' to '<EMAIL>'
2025-06-30T09:37:35.5271473+05:30 [Information] Resolved template pattern '{{$var:email}}' to '<EMAIL>' in '{
  "username": "{{$var:username}}",
  "email": "{{$var:email}}",
  "org": "{{$var:OrgName}}"
}'
2025-06-30T09:37:35.5272079+05:30 [Information] Processing $var: path 'OrgName'
2025-06-30T09:37:35.5272527+05:30 [Information] Checking if 'OrgName' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:35.5272924+05:30 [Information] Resolving path 'OrgName' using global variables.
2025-06-30T09:37:35.5273308+05:30 [Information] Resolved JPath '$var:OrgName' to 'Org1'
2025-06-30T09:37:35.5273665+05:30 [Information] Resolved template pattern '{{$var:OrgName}}' to 'Org1' in '{
  "username": "{{$var:username}}",
  "email": "{{$var:email}}",
  "org": "{{$var:OrgName}}"
}'
2025-06-30T09:37:35.5274055+05:30 [Information] Resolved template '{
  "username": "{{$var:username}}",
  "email": "{{$var:email}}",
  "org": "{{$var:OrgName}}"
}' to '{
  "username": "Tom",
  "email": "<EMAIL>",
  "org": "Org1"
}'
2025-06-30T09:37:35.5275002+05:30 [Information] Set Value to Step 'e21acc45-7e81-4365-a380-e8546e61c141' input:
{
  "Method": "POST",
  "RequestUri": "https://httpbin.org/post",
  "Headers": {
    "Content-Type": "application/json"
  },
  "Content": {
    "username": "Tom",
    "email": "<EMAIL>",
    "org": "Org1"
  }
}
2025-06-30T09:37:36.8556593+05:30 [Information] Set Value to Step 'e21acc45-7e81-4365-a380-e8546e61c141' output:
{
  "statusCode": 200,
  "reasonPhrase": "OK",
  "headers": [
    {
      "Key": "Date",
      "Value": [
        "Mon, 30 Jun 2025 04:07:42 GMT"
      ]
    },
    {
      "Key": "Connection",
      "Value": [
        "keep-alive"
      ]
    },
    {
      "Key": "Server",
      "Value": [
        "gunicorn/19.9.0"
      ]
    },
    {
      "Key": "Access-Control-Allow-Origin",
      "Value": [
        "*"
      ]
    },
    {
      "Key": "Access-Control-Allow-Credentials",
      "Value": [
        "true"
      ]
    }
  ],
  "content": {
    "args": {},
    "data": "{\r\n  \"username\": \"Tom\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"org\": \"Org1\"\r\n}",
    "files": {},
    "form": {},
    "headers": {
      "Content-Length": "74",
      "Content-Type": "application/json; charset=utf-8",
      "Host": "httpbin.org",
      "X-Amzn-Trace-Id": "Root=1-68620d8d-0530d8ce4e372a3836e592c8"
    },
    "json": {
      "email": "<EMAIL>",
      "org": "Org1",
      "username": "Tom"
    },
    "origin": "**************",
    "url": "https://httpbin.org/post"
  }
}
2025-06-30T09:37:36.8579196+05:30 [Information] Resolving path 'output.content.json.url' using step input/output.
2025-06-30T09:37:36.8587859+05:30 [Information] Resolved JPath 'output.content.json.url' to ''
2025-06-30T09:37:36.8596307+05:30 [Information] Set Value to Step 'e21acc45-7e81-4365-a380-e8546e61c141' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:07:35.5252377Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:37:36.8598076+05:30 [Information] Run Asserter : {"Expression1":{"Value":"200","ErrorMesage":null},"Expression2":{"JPath":"output.statusCode","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:37:36.8598643+05:30 [Information] Resolving path 'output.statusCode' using step input/output.
2025-06-30T09:37:36.8599164+05:30 [Information] Resolved JPath 'output.statusCode' to '200'
2025-06-30T09:37:36.8599676+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$test:row.User.Username}}","ErrorMesage":null},"Expression2":{"JPath":"output.content.json.username","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:37:36.8600155+05:30 [Information] Resolving path 'row.User.Username' using test context.
2025-06-30T09:37:36.8600720+05:30 [Information] Resolved JPath '$test:row.User.Username' to 'Tom'
2025-06-30T09:37:36.8601158+05:30 [Information] Resolved template pattern '{{$test:row.User.Username}}' to 'Tom' in '{{$test:row.User.Username}}'
2025-06-30T09:37:36.8602426+05:30 [Information] Resolved template '{{$test:row.User.Username}}' to 'Tom'
2025-06-30T09:37:36.8603615+05:30 [Information] Resolving path 'output.content.json.username' using step input/output.
2025-06-30T09:37:36.8604169+05:30 [Information] Resolved JPath 'output.content.json.username' to 'Tom'
2025-06-30T09:37:36.8604664+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$test:row.User.Email}}","ErrorMesage":null},"Expression2":{"JPath":"output.content.json.email","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:37:36.8605251+05:30 [Information] Resolving path 'row.User.Email' using test context.
2025-06-30T09:37:36.8605772+05:30 [Information] Resolved JPath '$test:row.User.Email' to '<EMAIL>'
2025-06-30T09:37:36.8606181+05:30 [Information] Resolved template pattern '{{$test:row.User.Email}}' to '<EMAIL>' in '{{$test:row.User.Email}}'
2025-06-30T09:37:36.8606760+05:30 [Information] Resolved template '{{$test:row.User.Email}}' to '<EMAIL>'
2025-06-30T09:37:36.8607364+05:30 [Information] Resolving path 'output.content.json.email' using step input/output.
2025-06-30T09:37:36.8607882+05:30 [Information] Resolved JPath 'output.content.json.email' to '<EMAIL>'
2025-06-30T09:37:36.8608308+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$test:row.OrgName}}","ErrorMesage":null},"Expression2":{"JPath":"output.content.json.org","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:37:36.8608860+05:30 [Information] Resolving path 'row.OrgName' using test context.
2025-06-30T09:37:36.8609912+05:30 [Information] Resolved JPath '$test:row.OrgName' to 'Org1'
2025-06-30T09:37:36.8610916+05:30 [Information] Resolved template pattern '{{$test:row.OrgName}}' to 'Org1' in '{{$test:row.OrgName}}'
2025-06-30T09:37:36.8611418+05:30 [Information] Resolved template '{{$test:row.OrgName}}' to 'Org1'
2025-06-30T09:37:36.8612127+05:30 [Information] Resolving path 'output.content.json.org' using step input/output.
2025-06-30T09:37:36.8614109+05:30 [Information] Resolved JPath 'output.content.json.org' to 'Org1'
2025-06-30T09:37:36.8616091+05:30 [Information] Set Value to Step 'e21acc45-7e81-4365-a380-e8546e61c141' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 1334,
  "startTime": "2025-06-30T04:07:35.5252377Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:37:36.8616948+05:30 [Information] Step 'Fetching user Tom details' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "e21acc45-7e81-4365-a380-e8546e61c141",
        "tables": {},
        "variables": {},
        "name": "Fetching user Tom details",
        "description": "Run step for each user",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "Tom",
            "email": "<EMAIL>",
            "org": "Org1"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:07:42 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"Tom\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"org\": \"Org1\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "74",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620d8d-0530d8ce4e372a3836e592c8"
            },
            "json": {
              "email": "<EMAIL>",
              "org": "Org1",
              "username": "Tom"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 1334,
          "startTime": "2025-06-30T04:07:35.5252377Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaseUrl": "https://httpbin.org",
      "User": {
        "Username": "Tom",
        "Email": "<EMAIL>"
      },
      "OrgName": "Org1",
      "username": "Tom",
      "email": "<EMAIL>"
    },
    "tables": {
      "UserTable": [
        {
          "User": {
            "Username": "Tom",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org1"
        },
        {
          "User": {
            "Username": "John",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org2"
        }
      ]
    },
    "context": {
      "repeatForTable": "UserTable",
      "row": {
        "User": {
          "Username": "Tom",
          "Email": "<EMAIL>"
        },
        "OrgName": "Org1"
      },
      "rowIndex": 0
    }
  }
}
2025-06-30T09:37:36.8618496+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:37:36.8619409+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:37:36.8619874+05:30 [Information] No performance data found for step 'Fetching user {{$var:username}} details' (http)
2025-06-30T09:37:36.8620277+05:30 [Information] [ OK       ]
2025-06-30T09:37:36.8624272+05:30 [Information] Default-mapped column 'User' to variable 'User' with value '{
  "Username": "John",
  "Email": "<EMAIL>"
}'
2025-06-30T09:37:36.8625328+05:30 [Information] Default-mapped column 'OrgName' to variable 'OrgName' with value 'Org2'
2025-06-30T09:37:36.8626234+05:30 [Information] Processing $var: path 'User.Username'
2025-06-30T09:37:36.8626928+05:30 [Information] Checking if 'User.Username' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=True, result=False
2025-06-30T09:37:36.8627701+05:30 [Information] Resolving path 'User.Username' using global variables.
2025-06-30T09:37:36.8628381+05:30 [Information] Resolved JPath '$var:User.Username' to 'John'
2025-06-30T09:37:36.8628994+05:30 [Information] Resolved template pattern '{{$var:User.Username}}' to 'John' in '{{$var:User.Username}}'
2025-06-30T09:37:36.8630842+05:30 [Information] Resolved template '{{$var:User.Username}}' to 'John'
2025-06-30T09:37:36.8632764+05:30 [Information] Mapped column '{{$var:User.Username}}' to variable 'username' with value 'John'
2025-06-30T09:37:36.8634107+05:30 [Information] Processing $var: path 'User.Email'
2025-06-30T09:37:36.8634936+05:30 [Information] Checking if 'User.Email' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=True, result=False
2025-06-30T09:37:36.8635516+05:30 [Information] Resolving path 'User.Email' using global variables.
2025-06-30T09:37:36.8636056+05:30 [Information] Resolved JPath '$var:User.Email' to '<EMAIL>'
2025-06-30T09:37:36.8636563+05:30 [Information] Resolved template pattern '{{$var:User.Email}}' to '<EMAIL>' in '{{$var:User.Email}}'
2025-06-30T09:37:36.8637076+05:30 [Information] Resolved template '{{$var:User.Email}}' to '<EMAIL>'
2025-06-30T09:37:36.8637906+05:30 [Information] Mapped column '{{$var:User.Email}}' to variable 'email' with value '<EMAIL>'
2025-06-30T09:37:36.8640199+05:30 [Information] Test Iteration 2/2: Demo HTTP Table Test Iteration
2025-06-30T09:37:36.8641726+05:30 [Information] Processing $var: path 'username'
2025-06-30T09:37:36.8642263+05:30 [Information] Checking if 'username' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:36.8642959+05:30 [Information] Resolving path 'username' using global variables.
2025-06-30T09:37:36.8644066+05:30 [Information] Resolved JPath '$var:username' to 'John'
2025-06-30T09:37:36.8644605+05:30 [Information] Resolved template pattern '{{$var:username}}' to 'John' in 'Fetching user {{$var:username}} details'
2025-06-30T09:37:36.8645064+05:30 [Information] Resolved template 'Fetching user {{$var:username}} details' to 'Fetching user John details'
2025-06-30T09:37:36.8645571+05:30 [Information] Set Value to Step '6b67ce16-182f-4113-b147-81f950eac16e' name:
"Fetching user John details"
2025-06-30T09:37:36.8646054+05:30 [Information] Set Value to Step '6b67ce16-182f-4113-b147-81f950eac16e' description:
"Run step for each user"
2025-06-30T09:37:36.8646503+05:30 [Information] [ RUN      ] Demo HTTP Table Test Iteration > Fetching user John details
2025-06-30T09:37:36.8649194+05:30 [Information] Executing single instance of step 'Fetching user {{$var:username}} details'.
2025-06-30T09:37:36.8650436+05:30 [Information] Executing step 'Fetching user John details' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "e21acc45-7e81-4365-a380-e8546e61c141",
        "tables": {},
        "variables": {},
        "name": "Fetching user Tom details",
        "description": "Run step for each user",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "Tom",
            "email": "<EMAIL>",
            "org": "Org1"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:07:42 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"Tom\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"org\": \"Org1\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "74",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620d8d-0530d8ce4e372a3836e592c8"
            },
            "json": {
              "email": "<EMAIL>",
              "org": "Org1",
              "username": "Tom"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 1334,
          "startTime": "2025-06-30T04:07:35.5252377Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      },
      {
        "id": "6b67ce16-182f-4113-b147-81f950eac16e",
        "tables": {},
        "variables": {},
        "name": "Fetching user John details",
        "description": "Run step for each user"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaseUrl": "https://httpbin.org",
      "User": {
        "Username": "John",
        "Email": "<EMAIL>"
      },
      "OrgName": "Org2",
      "username": "John",
      "email": "<EMAIL>"
    },
    "tables": {
      "UserTable": [
        {
          "User": {
            "Username": "Tom",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org1"
        },
        {
          "User": {
            "Username": "John",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org2"
        }
      ]
    },
    "context": {
      "repeatForTable": "UserTable",
      "row": {
        "User": {
          "Username": "John",
          "Email": "<EMAIL>"
        },
        "OrgName": "Org2"
      },
      "rowIndex": 1
    }
  }
}
2025-06-30T09:37:36.8651055+05:30 [Information] Processing $var: path 'BaseUrl'
2025-06-30T09:37:36.8651700+05:30 [Information] Checking if 'BaseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:36.8652133+05:30 [Information] Resolving path 'BaseUrl' using global variables.
2025-06-30T09:37:36.8652591+05:30 [Information] Resolved JPath '$var:BaseUrl' to 'https://httpbin.org'
2025-06-30T09:37:36.8653257+05:30 [Information] Resolved template pattern '{{$var:BaseUrl}}' to 'https://httpbin.org' in '{{$var:BaseUrl}}/post'
2025-06-30T09:37:36.8653660+05:30 [Information] Resolved template '{{$var:BaseUrl}}/post' to 'https://httpbin.org/post'
2025-06-30T09:37:36.8654237+05:30 [Information] Processing $var: path 'username'
2025-06-30T09:37:36.8654636+05:30 [Information] Checking if 'username' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:36.8655005+05:30 [Information] Resolving path 'username' using global variables.
2025-06-30T09:37:36.8655727+05:30 [Information] Resolved JPath '$var:username' to 'John'
2025-06-30T09:37:36.8656087+05:30 [Information] Resolved template pattern '{{$var:username}}' to 'John' in '{
  "username": "{{$var:username}}",
  "email": "{{$var:email}}",
  "org": "{{$var:OrgName}}"
}'
2025-06-30T09:37:36.8656454+05:30 [Information] Processing $var: path 'email'
2025-06-30T09:37:36.8656814+05:30 [Information] Checking if 'email' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:36.8657171+05:30 [Information] Resolving path 'email' using global variables.
2025-06-30T09:37:36.8657517+05:30 [Information] Resolved JPath '$var:email' to '<EMAIL>'
2025-06-30T09:37:36.8657861+05:30 [Information] Resolved template pattern '{{$var:email}}' to '<EMAIL>' in '{
  "username": "{{$var:username}}",
  "email": "{{$var:email}}",
  "org": "{{$var:OrgName}}"
}'
2025-06-30T09:37:36.8658219+05:30 [Information] Processing $var: path 'OrgName'
2025-06-30T09:37:36.8658569+05:30 [Information] Checking if 'OrgName' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:36.8658922+05:30 [Information] Resolving path 'OrgName' using global variables.
2025-06-30T09:37:36.8659280+05:30 [Information] Resolved JPath '$var:OrgName' to 'Org2'
2025-06-30T09:37:36.8659627+05:30 [Information] Resolved template pattern '{{$var:OrgName}}' to 'Org2' in '{
  "username": "{{$var:username}}",
  "email": "{{$var:email}}",
  "org": "{{$var:OrgName}}"
}'
2025-06-30T09:37:36.8659985+05:30 [Information] Resolved template '{
  "username": "{{$var:username}}",
  "email": "{{$var:email}}",
  "org": "{{$var:OrgName}}"
}' to '{
  "username": "John",
  "email": "<EMAIL>",
  "org": "Org2"
}'
2025-06-30T09:37:36.8660720+05:30 [Information] Set Value to Step '6b67ce16-182f-4113-b147-81f950eac16e' input:
{
  "Method": "POST",
  "RequestUri": "https://httpbin.org/post",
  "Headers": {
    "Content-Type": "application/json"
  },
  "Content": {
    "username": "John",
    "email": "<EMAIL>",
    "org": "Org2"
  }
}
2025-06-30T09:37:37.1280328+05:30 [Information] Set Value to Step '6b67ce16-182f-4113-b147-81f950eac16e' output:
{
  "statusCode": 200,
  "reasonPhrase": "OK",
  "headers": [
    {
      "Key": "Date",
      "Value": [
        "Mon, 30 Jun 2025 04:07:42 GMT"
      ]
    },
    {
      "Key": "Connection",
      "Value": [
        "keep-alive"
      ]
    },
    {
      "Key": "Server",
      "Value": [
        "gunicorn/19.9.0"
      ]
    },
    {
      "Key": "Access-Control-Allow-Origin",
      "Value": [
        "*"
      ]
    },
    {
      "Key": "Access-Control-Allow-Credentials",
      "Value": [
        "true"
      ]
    }
  ],
  "content": {
    "args": {},
    "data": "{\r\n  \"username\": \"John\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"org\": \"Org2\"\r\n}",
    "files": {},
    "form": {},
    "headers": {
      "Content-Length": "76",
      "Content-Type": "application/json; charset=utf-8",
      "Host": "httpbin.org",
      "X-Amzn-Trace-Id": "Root=1-68620d8e-5bf6ed2622866189514989f3"
    },
    "json": {
      "email": "<EMAIL>",
      "org": "Org2",
      "username": "John"
    },
    "origin": "**************",
    "url": "https://httpbin.org/post"
  }
}
2025-06-30T09:37:37.1293896+05:30 [Information] Resolving path 'output.content.json.url' using step input/output.
2025-06-30T09:37:37.1300923+05:30 [Information] Resolved JPath 'output.content.json.url' to ''
2025-06-30T09:37:37.1311893+05:30 [Information] Set Value to Step '6b67ce16-182f-4113-b147-81f950eac16e' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:07:36.8649173Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:37:37.1315633+05:30 [Information] Run Asserter : {"Expression1":{"Value":"200","ErrorMesage":null},"Expression2":{"JPath":"output.statusCode","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:37:37.1316132+05:30 [Information] Resolving path 'output.statusCode' using step input/output.
2025-06-30T09:37:37.1316691+05:30 [Information] Resolved JPath 'output.statusCode' to '200'
2025-06-30T09:37:37.1317177+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$test:row.User.Username}}","ErrorMesage":null},"Expression2":{"JPath":"output.content.json.username","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:37:37.1317754+05:30 [Information] Resolving path 'row.User.Username' using test context.
2025-06-30T09:37:37.1318150+05:30 [Information] Resolved JPath '$test:row.User.Username' to 'John'
2025-06-30T09:37:37.1318499+05:30 [Information] Resolved template pattern '{{$test:row.User.Username}}' to 'John' in '{{$test:row.User.Username}}'
2025-06-30T09:37:37.1318874+05:30 [Information] Resolved template '{{$test:row.User.Username}}' to 'John'
2025-06-30T09:37:37.1319648+05:30 [Information] Resolving path 'output.content.json.username' using step input/output.
2025-06-30T09:37:37.1320078+05:30 [Information] Resolved JPath 'output.content.json.username' to 'John'
2025-06-30T09:37:37.1320661+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$test:row.User.Email}}","ErrorMesage":null},"Expression2":{"JPath":"output.content.json.email","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:37:37.1321074+05:30 [Information] Resolving path 'row.User.Email' using test context.
2025-06-30T09:37:37.1321455+05:30 [Information] Resolved JPath '$test:row.User.Email' to '<EMAIL>'
2025-06-30T09:37:37.1321799+05:30 [Information] Resolved template pattern '{{$test:row.User.Email}}' to '<EMAIL>' in '{{$test:row.User.Email}}'
2025-06-30T09:37:37.1323398+05:30 [Information] Resolved template '{{$test:row.User.Email}}' to '<EMAIL>'
2025-06-30T09:37:37.1325561+05:30 [Information] Resolving path 'output.content.json.email' using step input/output.
2025-06-30T09:37:37.1326583+05:30 [Information] Resolved JPath 'output.content.json.email' to '<EMAIL>'
2025-06-30T09:37:37.1327347+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$test:row.OrgName}}","ErrorMesage":null},"Expression2":{"JPath":"output.content.json.org","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:37:37.1327964+05:30 [Information] Resolving path 'row.OrgName' using test context.
2025-06-30T09:37:37.1328382+05:30 [Information] Resolved JPath '$test:row.OrgName' to 'Org2'
2025-06-30T09:37:37.1328829+05:30 [Information] Resolved template pattern '{{$test:row.OrgName}}' to 'Org2' in '{{$test:row.OrgName}}'
2025-06-30T09:37:37.1329221+05:30 [Information] Resolved template '{{$test:row.OrgName}}' to 'Org2'
2025-06-30T09:37:37.1329947+05:30 [Information] Resolving path 'output.content.json.org' using step input/output.
2025-06-30T09:37:37.1330418+05:30 [Information] Resolved JPath 'output.content.json.org' to 'Org2'
2025-06-30T09:37:37.1334363+05:30 [Information] Set Value to Step '6b67ce16-182f-4113-b147-81f950eac16e' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 267,
  "startTime": "2025-06-30T04:07:36.8649173Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:37:37.1335686+05:30 [Information] Step 'Fetching user John details' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "e21acc45-7e81-4365-a380-e8546e61c141",
        "tables": {},
        "variables": {},
        "name": "Fetching user Tom details",
        "description": "Run step for each user",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "Tom",
            "email": "<EMAIL>",
            "org": "Org1"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:07:42 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"Tom\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"org\": \"Org1\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "74",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620d8d-0530d8ce4e372a3836e592c8"
            },
            "json": {
              "email": "<EMAIL>",
              "org": "Org1",
              "username": "Tom"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 1334,
          "startTime": "2025-06-30T04:07:35.5252377Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      },
      {
        "id": "6b67ce16-182f-4113-b147-81f950eac16e",
        "tables": {},
        "variables": {},
        "name": "Fetching user John details",
        "description": "Run step for each user",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "John",
            "email": "<EMAIL>",
            "org": "Org2"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:07:42 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"John\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"org\": \"Org2\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "76",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620d8e-5bf6ed2622866189514989f3"
            },
            "json": {
              "email": "<EMAIL>",
              "org": "Org2",
              "username": "John"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 267,
          "startTime": "2025-06-30T04:07:36.8649173Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaseUrl": "https://httpbin.org",
      "User": {
        "Username": "John",
        "Email": "<EMAIL>"
      },
      "OrgName": "Org2",
      "username": "John",
      "email": "<EMAIL>"
    },
    "tables": {
      "UserTable": [
        {
          "User": {
            "Username": "Tom",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org1"
        },
        {
          "User": {
            "Username": "John",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org2"
        }
      ]
    },
    "context": {
      "repeatForTable": "UserTable",
      "row": {
        "User": {
          "Username": "John",
          "Email": "<EMAIL>"
        },
        "OrgName": "Org2"
      },
      "rowIndex": 1
    }
  }
}
2025-06-30T09:37:37.1336336+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:37:37.1336800+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:37:37.1337170+05:30 [Information] No performance data found for step 'Fetching user {{$var:username}} details' (http)
2025-06-30T09:37:37.1337502+05:30 [Information] [ OK       ]
2025-06-30T09:37:37.1340129+05:30 [Information] [----------] 1 step from Demo HTTP Table Test Iteration (1611 ms total)
