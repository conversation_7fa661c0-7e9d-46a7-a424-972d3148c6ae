/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using Microsoft.Extensions.Logging;
using NUnit.Framework;
using System;
using System.IO;

namespace TestLib.Tests;

[TestFixture]
public class FileLoggerProviderTests
{
    private string _testDirectory;
    private string _logFilePath;
    private FileLoggerProvider _provider;

    [SetUp]
    public void Setup()
    {
        _testDirectory = Path.Combine(Path.GetTempPath(), Guid.NewGuid().ToString());
        Directory.CreateDirectory(_testDirectory);
        _logFilePath = Path.Combine(_testDirectory, "test.log");
        _provider = new FileLoggerProvider(_logFilePath);
    }

    [TearDown]
    public void TearDown()
    {
        _provider?.Dispose();
        if (Directory.Exists(_testDirectory))
        {
            Directory.Delete(_testDirectory, true);
        }
    }

    [Test]
    public void Constructor_CreatesFileLoggerProvider()
    {
        var provider = new FileLoggerProvider(_logFilePath);
        Assert.IsNotNull(provider);
    }

    [Test]
    public void CreateLogger_WithCategoryName_ReturnsFileLogger()
    {
        // Act
        var logger = _provider.CreateLogger("TestCategory");
        
        // Assert
        Assert.IsNotNull(logger);
        Assert.IsInstanceOf<FileLogger>(logger);
    }

    [Test]
    public void CreateLogger_WithDifferentCategoryNames_ReturnsDifferentLoggers()
    {
        // Act
        var logger1 = _provider.CreateLogger("Category1");
        var logger2 = _provider.CreateLogger("Category2");
        
        // Assert
        Assert.IsNotNull(logger1);
        Assert.IsNotNull(logger2);
        Assert.AreNotSame(logger1, logger2);
        Assert.IsInstanceOf<FileLogger>(logger1);
        Assert.IsInstanceOf<FileLogger>(logger2);
    }

    [Test]
    public void CreateLogger_WithSameCategoryName_ReturnsDifferentInstances()
    {
        // Act
        var logger1 = _provider.CreateLogger("SameCategory");
        var logger2 = _provider.CreateLogger("SameCategory");
        
        // Assert
        Assert.IsNotNull(logger1);
        Assert.IsNotNull(logger2);
        Assert.AreNotSame(logger1, logger2);
    }

    [Test]
    public void CreateLogger_WithNullCategoryName_ReturnsFileLogger()
    {
        // Act
        var logger = _provider.CreateLogger(null);
        
        // Assert
        Assert.IsNotNull(logger);
        Assert.IsInstanceOf<FileLogger>(logger);
    }

    [Test]
    public void CreateLogger_WithEmptyCategoryName_ReturnsFileLogger()
    {
        // Act
        var logger = _provider.CreateLogger(string.Empty);
        
        // Assert
        Assert.IsNotNull(logger);
        Assert.IsInstanceOf<FileLogger>(logger);
    }

    [Test]
    public void CreateLogger_LoggerWritesToCorrectFile()
    {
        // Arrange
        var logger = _provider.CreateLogger("TestCategory");
        var message = "Test message from provider logger";
        
        // Act
        logger.Log(LogLevel.Information, new EventId(1), message, null, (state, ex) => state.ToString());
        
        // Assert
        Assert.IsTrue(File.Exists(_logFilePath));
        var content = File.ReadAllText(_logFilePath);
        StringAssert.Contains(message, content);
        StringAssert.Contains("[Information]", content);
    }

    [Test]
    public void CreateLogger_MultipleLoggersWriteToSameFile()
    {
        // Arrange
        var logger1 = _provider.CreateLogger("Category1");
        var logger2 = _provider.CreateLogger("Category2");
        var message1 = "Message from logger 1";
        var message2 = "Message from logger 2";
        
        // Act
        logger1.Log(LogLevel.Information, new EventId(1), message1, null, (state, ex) => state.ToString());
        logger2.Log(LogLevel.Warning, new EventId(2), message2, null, (state, ex) => state.ToString());
        
        // Assert
        Assert.IsTrue(File.Exists(_logFilePath));
        var content = File.ReadAllText(_logFilePath);
        StringAssert.Contains(message1, content);
        StringAssert.Contains(message2, content);
        StringAssert.Contains("[Information]", content);
        StringAssert.Contains("[Warning]", content);
    }

    [Test]
    public void Dispose_DoesNotThrow()
    {
        // Act & Assert
        Assert.DoesNotThrow(() => _provider.Dispose());
    }

    [Test]
    public void Dispose_CanBeCalledMultipleTimes()
    {
        // Act & Assert
        Assert.DoesNotThrow(() => 
        {
            _provider.Dispose();
            _provider.Dispose();
            _provider.Dispose();
        });
    }

    [Test]
    public void CreateLogger_AfterDispose_StillWorks()
    {
        // Arrange
        _provider.Dispose();
        
        // Act
        var logger = _provider.CreateLogger("PostDisposeCategory");
        
        // Assert
        Assert.IsNotNull(logger);
        Assert.IsInstanceOf<FileLogger>(logger);
    }

    [Test]
    public void CreateLogger_WithLongCategoryName_ReturnsFileLogger()
    {
        // Arrange
        var longCategoryName = new string('A', 1000);
        
        // Act
        var logger = _provider.CreateLogger(longCategoryName);
        
        // Assert
        Assert.IsNotNull(logger);
        Assert.IsInstanceOf<FileLogger>(logger);
    }

    [Test]
    public void CreateLogger_WithSpecialCharactersInCategoryName_ReturnsFileLogger()
    {
        // Arrange
        var specialCategoryName = "Category.With.Dots-And_Underscores@#$%";
        
        // Act
        var logger = _provider.CreateLogger(specialCategoryName);
        
        // Assert
        Assert.IsNotNull(logger);
        Assert.IsInstanceOf<FileLogger>(logger);
    }

    [Test]
    public void Integration_WithLoggerFactory_WorksCorrectly()
    {
        // Arrange
        using var factory = LoggerFactory.Create(builder =>
        {
            builder.AddProvider(_provider);
        });
        
        var logger = factory.CreateLogger("IntegrationTest");
        var message = "Integration test message";
        
        // Act
        logger.LogInformation(message);
        
        // Assert
        Assert.IsTrue(File.Exists(_logFilePath));
        var content = File.ReadAllText(_logFilePath);
        StringAssert.Contains(message, content);
        StringAssert.Contains("[Information]", content);
    }
}
