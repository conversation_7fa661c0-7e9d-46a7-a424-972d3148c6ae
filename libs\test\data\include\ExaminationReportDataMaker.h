/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

#pragma once

#include "ProtoUtils.h"
#include "RandomUtils.h"
#include "PatientDataMaker.h"
#include "RefractionDataMaker.h"
#include "KeratometryDataMaker.h"
#include "AxialBiometryDataMaker.h"
#include "EnFaceBiometryDataMaker.h"
#include "../generated/ExaminationReportDataMaker.pb.h"

namespace testdatamaker {

namespace type = alcon::interop::type;
namespace erdmp = testdatamaker::examinationreportdatamaker;

using ctu = alcon::interop::type::CoreTypesUtil;
using pddm = testdatamaker::PatientDataMaker;
using rdm = testdatamaker::RefractionDataMaker;
using abdm = testdatamaker::AxialBiometryDataMaker;
using efbdm = testdatamaker::EnFaceBiometryDataMaker;
using kdm = testdatamaker::KeratometryDataMaker;

class ExaminationReportDataMaker {
public:
    using examinationreportdatamaker_config_t =
        dmb::datamaker_config_t<type::ExaminationReport, erdmp::ExaminationReportDataMakerParams>;
    using examinationreportdatamaker_config_Builder =
        dmb::datamaker_config_Builder<type::ExaminationReport, erdmp::ExaminationReportDataMakerParams>;

    static std::optional<type::ExaminationReport> make(const examinationreportdatamaker_config_t& config);
    static std::optional<type::ExaminationReport> make(const erdmp::ExaminationReportDataMakerConfig& config);
    static bool
        pairExaminationReports(type::ExaminationReport& _odExaminationReport, type::ExaminationReport& _osExaminationReport);
    static std::vector<type::ExaminationReport> extractExaminationReportFromDb(const mpdb::InMultiProtobufDb& data);

private:

    static type::ExaminationReport makeRandom(const erdmp::ExaminationReportDataMakerParams& params);
    static type::ExaminationReport makeBasedOn(
        const std::optional<type::ExaminationReport>& seed, const erdmp::ExaminationReportDataMakerParams& params);

    static type::cataract::EyeContext makeEyeContext(const std::optional<erdmp::EyeContextArgs>& makeEyeContextArgs);
    static type::devices::ArgosExamination makeArgos(const std::optional<erdmp::ArgosArgs>& argosArgs);
    static type::devices::MetiorExamination makeMetior(const  std::optional<erdmp::MetiorArgs>& metiorArgs);
};
} // testdatamaker
