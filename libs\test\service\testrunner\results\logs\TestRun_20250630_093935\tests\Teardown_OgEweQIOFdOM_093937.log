2025-06-30T09:39:37.9421579+05:30 [Information] [----------] 1 step from Teardown
2025-06-30T09:39:37.9427131+05:30 [Information] Set Value to Step 'b80972b9-b242-47cc-90d5-bece5cfd0485' name:
"Clean up Database"
2025-06-30T09:39:37.9428503+05:30 [Information] Set Value to Step 'b80972b9-b242-47cc-90d5-bece5cfd0485' description:
"Tear down the database state"
2025-06-30T09:39:37.9429119+05:30 [Information] [ RUN      ] Teardown > Clean up Database
2025-06-30T09:39:37.9431630+05:30 [Information] Executing single instance of step 'Clean up Database'.
2025-06-30T09:39:37.9432944+05:30 [Information] Executing step 'Clean up Database' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "b80972b9-b242-47cc-90d5-bece5cfd0485",
        "tables": {},
        "variables": {},
        "name": "Clean up Database",
        "description": "Tear down the database state"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:39:37.9471743+05:30 [Information] Processing $var: path 'baseUrl'
2025-06-30T09:39:37.9474778+05:30 [Information] Checking if 'baseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:39:37.9477170+05:30 [Information] Resolving path 'baseUrl' using global variables.
2025-06-30T09:39:37.9479185+05:30 [Information] Resolved JPath '$var:baseUrl' to 'https://httpbin.org'
2025-06-30T09:39:37.9480590+05:30 [Information] Resolved template pattern '{{$var:baseUrl}}' to 'https://httpbin.org' in '{{$var:baseUrl}}/post'
2025-06-30T09:39:37.9481786+05:30 [Information] Resolved template '{{$var:baseUrl}}/post' to 'https://httpbin.org/post'
2025-06-30T09:39:37.9484716+05:30 [Information] Set Value to Step 'b80972b9-b242-47cc-90d5-bece5cfd0485' input:
{
  "Method": "POST",
  "RequestUri": "https://httpbin.org/post",
  "Headers": {
    "Content-Type": "application/json"
  },
  "Content": {
    "action": "cleanup_db"
  }
}
2025-06-30T09:39:38.6979593+05:30 [Information] Set Value to Step 'b80972b9-b242-47cc-90d5-bece5cfd0485' output:
{
  "statusCode": 200,
  "reasonPhrase": "OK",
  "headers": [
    {
      "Key": "Date",
      "Value": [
        "Mon, 30 Jun 2025 04:09:44 GMT"
      ]
    },
    {
      "Key": "Connection",
      "Value": [
        "keep-alive"
      ]
    },
    {
      "Key": "Server",
      "Value": [
        "gunicorn/19.9.0"
      ]
    },
    {
      "Key": "Access-Control-Allow-Origin",
      "Value": [
        "*"
      ]
    },
    {
      "Key": "Access-Control-Allow-Credentials",
      "Value": [
        "true"
      ]
    }
  ],
  "content": {
    "args": {},
    "data": "{\r\n  \"action\": \"cleanup_db\"\r\n}",
    "files": {},
    "form": {},
    "headers": {
      "Content-Length": "30",
      "Content-Type": "application/json; charset=utf-8",
      "Host": "httpbin.org",
      "X-Amzn-Trace-Id": "Root=1-68620e08-7b09573e746c2e5751b9458d"
    },
    "json": {
      "action": "cleanup_db"
    },
    "origin": "**************",
    "url": "https://httpbin.org/post"
  }
}
2025-06-30T09:39:38.7003341+05:30 [Information] Set Value to Step 'b80972b9-b242-47cc-90d5-bece5cfd0485' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:09:37.9431592Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:39:38.7012115+05:30 [Information] Set Value to Step 'b80972b9-b242-47cc-90d5-bece5cfd0485' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 752,
  "startTime": "2025-06-30T04:09:37.9431592Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:39:38.7021178+05:30 [Information] Step 'Clean up Database' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "b80972b9-b242-47cc-90d5-bece5cfd0485",
        "tables": {},
        "variables": {},
        "name": "Clean up Database",
        "description": "Tear down the database state",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "action": "cleanup_db"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:09:44 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"action\": \"cleanup_db\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "30",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620e08-7b09573e746c2e5751b9458d"
            },
            "json": {
              "action": "cleanup_db"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 752,
          "startTime": "2025-06-30T04:09:37.9431592Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:39:38.7025671+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:39:38.7028696+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:39:38.7031564+05:30 [Information] No performance data found for step 'Clean up Database' (http)
2025-06-30T09:39:38.7033152+05:30 [Information] [ OK       ]
2025-06-30T09:39:38.7038330+05:30 [Information] [----------] 1 step from Teardown (761 ms total)
