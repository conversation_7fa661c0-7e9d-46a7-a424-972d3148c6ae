2025-06-30T09:01:35.4999020+05:30 [Information] [----------] 1 step from Startup
2025-06-30T09:01:35.5021441+05:30 [Information] Set Value to Step 'fc244819-8beb-422c-8a65-0fe0f0b7c62f' name:
"Initialize Database"
2025-06-30T09:01:35.5023726+05:30 [Information] Set Value to Step 'fc244819-8beb-422c-8a65-0fe0f0b7c62f' description:
"Setup initial database state"
2025-06-30T09:01:35.5025903+05:30 [Information] [ RUN      ] Startup > Initialize Database
2025-06-30T09:01:35.5036044+05:30 [Information] Executing single instance of step 'Initialize Database'.
2025-06-30T09:01:35.5047420+05:30 [Information] Executing step 'Initialize Database' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "fc244819-8beb-422c-8a65-0fe0f0b7c62f",
        "tables": {},
        "variables": {},
        "name": "Initialize Database",
        "description": "Setup initial database state"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:01:35.5065608+05:30 [Information] Processing $var: path 'baseUrl'
2025-06-30T09:01:35.5080723+05:30 [Information] Checking if 'baseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:01:35.5099074+05:30 [Information] Resolving path 'baseUrl' using global variables.
2025-06-30T09:01:35.5102210+05:30 [Information] Resolved JPath '$var:baseUrl' to 'https://httpbin.org'
2025-06-30T09:01:35.5103262+05:30 [Information] Resolved template pattern '{{$var:baseUrl}}' to 'https://httpbin.org' in '{{$var:baseUrl}}/post'
2025-06-30T09:01:35.5105948+05:30 [Information] Resolved template '{{$var:baseUrl}}/post' to 'https://httpbin.org/post'
2025-06-30T09:01:35.5128538+05:30 [Information] Set Value to Step 'fc244819-8beb-422c-8a65-0fe0f0b7c62f' input:
{
  "Method": "POST",
  "RequestUri": "https://httpbin.org/post",
  "Headers": {
    "Content-Type": "application/json"
  },
  "Content": {
    "action": "initialize_db"
  }
}
2025-06-30T09:01:36.7119260+05:30 [Information] Set Value to Step 'fc244819-8beb-422c-8a65-0fe0f0b7c62f' output:
{
  "statusCode": 200,
  "reasonPhrase": "OK",
  "headers": [
    {
      "Key": "Date",
      "Value": [
        "Mon, 30 Jun 2025 03:31:42 GMT"
      ]
    },
    {
      "Key": "Connection",
      "Value": [
        "keep-alive"
      ]
    },
    {
      "Key": "Server",
      "Value": [
        "gunicorn/19.9.0"
      ]
    },
    {
      "Key": "Access-Control-Allow-Origin",
      "Value": [
        "*"
      ]
    },
    {
      "Key": "Access-Control-Allow-Credentials",
      "Value": [
        "true"
      ]
    }
  ],
  "content": {
    "args": {},
    "data": "{\r\n  \"action\": \"initialize_db\"\r\n}",
    "files": {},
    "form": {},
    "headers": {
      "Content-Length": "33",
      "Content-Type": "application/json; charset=utf-8",
      "Host": "httpbin.org",
      "X-Amzn-Trace-Id": "Root=1-6862051e-16f574f6756a8ef72371c894"
    },
    "json": {
      "action": "initialize_db"
    },
    "origin": "**************",
    "url": "https://httpbin.org/post"
  }
}
2025-06-30T09:01:36.7125863+05:30 [Information] Resolving path 'output.content.json.action' using step input/output.
2025-06-30T09:01:36.7127663+05:30 [Information] Resolved JPath 'output.content.json.action' to 'initialize_db'
2025-06-30T09:01:36.7137863+05:30 [Information] Set Value to Step 'fc244819-8beb-422c-8a65-0fe0f0b7c62f' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T03:31:35.5035797Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:01:36.7162320+05:30 [Information] Run Asserter : {"Expression1":{"JPath":"$curStep:output.content.json.action","ErrorMesage":null},"Expression2":{"Value":null,"ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:01:36.7165601+05:30 [Information] Resolving path 'output.content.json.action' using $curStep:
2025-06-30T09:01:36.7166710+05:30 [Information] Resolved JPath '$curStep:output.content.json.action' to 'initialize_db'
2025-06-30T09:01:36.7179675+05:30 [Information] Error in EqualExpression: The method or operation is not implemented.
2025-06-30T09:01:36.7188681+05:30 [Information] Set Value to Step 'fc244819-8beb-422c-8a65-0fe0f0b7c62f' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 1205,
  "startTime": "2025-06-30T03:31:35.5035797Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:01:36.7190423+05:30 [Information] Step 'Initialize Database' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "fc244819-8beb-422c-8a65-0fe0f0b7c62f",
        "tables": {},
        "variables": {},
        "name": "Initialize Database",
        "description": "Setup initial database state",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "action": "initialize_db"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 03:31:42 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"action\": \"initialize_db\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "33",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-6862051e-16f574f6756a8ef72371c894"
            },
            "json": {
              "action": "initialize_db"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 1205,
          "startTime": "2025-06-30T03:31:35.5035797Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:01:36.7192280+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:01:36.7193287+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:01:36.7193890+05:30 [Information] No performance data found for step 'Initialize Database' (http)
2025-06-30T09:01:36.7196902+05:30 [Information] [ OK       ]
2025-06-30T09:01:36.7208391+05:30 [Information] [----------] 1 step from Startup (1219 ms total)
