2025-06-30T09:03:39.2861596+05:30 [Information] [----------] 1 step from Startup
2025-06-30T09:03:39.2884242+05:30 [Information] Set Value to Step '36c4ef09-706d-4b17-b236-d30216288067' name:
"Initialize Database"
2025-06-30T09:03:39.2886724+05:30 [Information] Set Value to Step '36c4ef09-706d-4b17-b236-d30216288067' description:
"Setup initial database state"
2025-06-30T09:03:39.2889144+05:30 [Information] [ RUN      ] Startup > Initialize Database
2025-06-30T09:03:39.2899420+05:30 [Information] Executing single instance of step 'Initialize Database'.
2025-06-30T09:03:39.2910809+05:30 [Information] Executing step 'Initialize Database' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "36c4ef09-706d-4b17-b236-d30216288067",
        "tables": {},
        "variables": {},
        "name": "Initialize Database",
        "description": "Setup initial database state"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:03:39.2928056+05:30 [Information] Processing $var: path 'baseUrl'
2025-06-30T09:03:39.2942348+05:30 [Information] Checking if 'baseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:03:39.2957690+05:30 [Information] Resolving path 'baseUrl' using global variables.
2025-06-30T09:03:39.2959757+05:30 [Information] Resolved JPath '$var:baseUrl' to 'https://httpbin.org'
2025-06-30T09:03:39.2960920+05:30 [Information] Resolved template pattern '{{$var:baseUrl}}' to 'https://httpbin.org' in '{{$var:baseUrl}}/post'
2025-06-30T09:03:39.2963237+05:30 [Information] Resolved template '{{$var:baseUrl}}/post' to 'https://httpbin.org/post'
2025-06-30T09:03:39.2985272+05:30 [Information] Set Value to Step '36c4ef09-706d-4b17-b236-d30216288067' input:
{
  "Method": "POST",
  "RequestUri": "https://httpbin.org/post",
  "Headers": {
    "Content-Type": "application/json"
  },
  "Content": {
    "action": "initialize_db"
  }
}
2025-06-30T09:03:40.5434687+05:30 [Information] Set Value to Step '36c4ef09-706d-4b17-b236-d30216288067' output:
{
  "statusCode": 200,
  "reasonPhrase": "OK",
  "headers": [
    {
      "Key": "Date",
      "Value": [
        "Mon, 30 Jun 2025 03:33:45 GMT"
      ]
    },
    {
      "Key": "Connection",
      "Value": [
        "keep-alive"
      ]
    },
    {
      "Key": "Server",
      "Value": [
        "gunicorn/19.9.0"
      ]
    },
    {
      "Key": "Access-Control-Allow-Origin",
      "Value": [
        "*"
      ]
    },
    {
      "Key": "Access-Control-Allow-Credentials",
      "Value": [
        "true"
      ]
    }
  ],
  "content": {
    "args": {},
    "data": "{\r\n  \"action\": \"initialize_db\"\r\n}",
    "files": {},
    "form": {},
    "headers": {
      "Content-Length": "33",
      "Content-Type": "application/json; charset=utf-8",
      "Host": "httpbin.org",
      "X-Amzn-Trace-Id": "Root=1-68620599-62a4959f0b936f460415ae94"
    },
    "json": {
      "action": "initialize_db"
    },
    "origin": "**************",
    "url": "https://httpbin.org/post"
  }
}
2025-06-30T09:03:40.5440459+05:30 [Information] Resolving path 'output.content.json.action' using step input/output.
2025-06-30T09:03:40.5442403+05:30 [Information] Resolved JPath 'output.content.json.action' to 'initialize_db'
2025-06-30T09:03:40.5452550+05:30 [Information] Set Value to Step '36c4ef09-706d-4b17-b236-d30216288067' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T03:33:39.2899161Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:03:40.5482159+05:30 [Information] Run Asserter : {"Expression1":{"JPath":"$curStep:output.content.json.action","ErrorMesage":null},"Expression2":{"Value":null,"ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:03:40.5486941+05:30 [Information] Resolving path 'output.content.json.action' using $curStep:
2025-06-30T09:03:40.5488679+05:30 [Information] Resolved JPath '$curStep:output.content.json.action' to 'initialize_db'
2025-06-30T09:03:40.5504105+05:30 [Information] Error in EqualExpression: The method or operation is not implemented.
2025-06-30T09:03:40.5516585+05:30 [Information] Set Value to Step '36c4ef09-706d-4b17-b236-d30216288067' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 1251,
  "startTime": "2025-06-30T03:33:39.2899161Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:03:40.5519225+05:30 [Information] Step 'Initialize Database' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "36c4ef09-706d-4b17-b236-d30216288067",
        "tables": {},
        "variables": {},
        "name": "Initialize Database",
        "description": "Setup initial database state",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "action": "initialize_db"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 03:33:45 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"action\": \"initialize_db\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "33",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620599-62a4959f0b936f460415ae94"
            },
            "json": {
              "action": "initialize_db"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 1251,
          "startTime": "2025-06-30T03:33:39.2899161Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:03:40.5521496+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:03:40.5522813+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:03:40.5523504+05:30 [Information] No performance data found for step 'Initialize Database' (http)
2025-06-30T09:03:40.5526764+05:30 [Information] [ OK       ]
2025-06-30T09:03:40.5538071+05:30 [Information] [----------] 1 step from Startup (1266 ms total)
