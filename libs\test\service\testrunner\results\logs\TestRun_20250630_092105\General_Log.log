2025-06-30T09:21:05.2724661+05:30 [Information] Global variables set to: baseUrl: https://httpbin.org, serverIp: 127.0.0.1, serverPort: 60606
2025-06-30T09:21:05.2890220+05:30 [Information] Loading tests from D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc with pattern *math-expressions-simple.yaml
2025-06-30T09:21:05.2929103+05:30 [Information] Loading test from D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc\math-expressions-simple.yaml
2025-06-30T09:21:05.3051991+05:30 [Information] Global variables set to: 
2025-06-30T09:21:05.3062636+05:30 [Information] [==========] Running 1 test from Yaml files
2025-06-30T09:21:05.3074262+05:30 [Information] [-------------------------------------    Startup     -------------------------------------]
2025-06-30T09:21:05.3271784+05:30 [Information] [----------] 1 step from Startup
2025-06-30T09:21:05.3303621+05:30 [Information] [ RUN      ] Startup > Initialize Database
2025-06-30T09:21:06.5306129+05:30 [Information] [ OK       ]
2025-06-30T09:21:06.5318309+05:30 [Information] [----------] 1 step from Startup (1203 ms total)
2025-06-30T09:21:06.5340617+05:30 [Information] Global variables set to: curDir: D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc, baseUrl: https://httpbin.org, serverIp: 127.0.0.1, serverPort: 60606, dbInitStatus: initialize_db
2025-06-30T09:21:06.5346016+05:30 [Information] [------------------------------------- Test Execution -------------------------------------]
2025-06-30T09:21:06.5357789+05:30 [Information] [----------] 6 steps from Simple Mathematical Expressions Demo
2025-06-30T09:21:06.5371781+05:30 [Information] [ RUN      ] Simple Mathematical Expressions Demo > Fast Baseline Operation
2025-06-30T09:21:06.6281940+05:30 [Information] [ OK       ]
2025-06-30T09:21:06.6286289+05:30 [Information] [ RUN      ] Simple Mathematical Expressions Demo > Regression Threshold Test
2025-06-30T09:21:07.8965026+05:30 [Error] Assert Failure : Less(150, 0)
2025-06-30T09:21:07.8972864+05:30 [Error] Performance regression detected: {{$curStep:performance.executionTimeMs}}ms vs allowed {{$var:MathBaseline * $var:MaxRegressionPercent / 100}}ms ({{$var:MathBaseline}}ms * {{$var:MaxRegressionPercent}}% / 100)
2025-06-30T09:21:07.8973911+05:30 [Error] [ FAILED   ]
2025-06-30T09:21:07.8978513+05:30 [Information] [----------] 6 steps from Simple Mathematical Expressions Demo (1361 ms total)
2025-06-30T09:21:07.8983077+05:30 [Information] [-------------------------------------    Teardown    -------------------------------------]
2025-06-30T09:21:07.8995629+05:30 [Information] [----------] 1 step from Teardown
2025-06-30T09:21:07.9001785+05:30 [Information] [ RUN      ] Teardown > Clean up Database
2025-06-30T09:21:09.2960653+05:30 [Information] [ OK       ]
2025-06-30T09:21:09.2991522+05:30 [Information] [----------] 1 step from Teardown (1398 ms total)
2025-06-30T09:21:09.3009845+05:30 [Information] Global variables set to: curDir: D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc, baseUrl: https://httpbin.org, serverIp: 127.0.0.1, serverPort: 60606, dbInitStatus: initialize_db
2025-06-30T09:21:09.3011181+05:30 [Information] [-------------------------------------  Test Results  -------------------------------------]
2025-06-30T09:21:09.3019678+05:30 [Information] [==========] 1 test from Yaml files ran. (3996 ms total)
2025-06-30T09:21:09.3032850+05:30 [Information] [  PASSED  ] 0 tests.
2025-06-30T09:21:09.3035561+05:30 [Error] [  FAILED  ] 1 test, listed below:
2025-06-30T09:21:09.3042232+05:30 [Error] [  FAILED  ] Simple Mathematical Expressions Demo
