Name: "Mathematical Expressions Demo"
Description: "Demonstrates the new mathematical expression functionality in performance testing scenarios"

Variables:
  BaseUrl: "https://httpbin.org"  # Use httpbin for testing
  BaselineResponseTime: "120"
  MaxRegressionPercent: "150"  # 150% = 50% increase allowed
  BufferTime: "10"
  LightweightThreshold: "50"
  ComplexThreshold: "200"
  SLALimit: "500"

Steps:
  # Step 1: Establish baseline performance
  - Name: "Baseline Performance Test"
    Input:
      Method: "GET"
      RequestUri: "{{$var:BaseUrl}}/delay/1"  # 1 second delay endpoint
    Execution:
      DelayMs: 100
    Asserters:
      # Should be reasonably fast (allowing for 1s delay + overhead)
      - AssertLt:
          ConstExpr: 2000  # 2 seconds max for 1s delay endpoint
          JPathExpr: "$curStep:performance.executionTimeMs"
          ErrorMessage: "Baseline too slow: {{$curStep:performance.executionTimeMs}}ms > 2000ms"
    Output:
      Store:
        actualBaseline: "performance.executionTimeMs"

  # Step 2: Test with mathematical expression for regression threshold
  - Name: "Regression Threshold Test"
    Input:
      Method: "GET"
      RequestUri: "{{$var:BaseUrl}}/delay/2"  # 2 second delay endpoint
    Execution:
      DelayMs: 100
    Asserters:
      # Should not exceed baseline * regression percentage
      - AssertLt:
          ConstExpr: "{{$var:actualBaseline * $var:MaxRegressionPercent / 100}}"
          JPathExpr: "$curStep:performance.executionTimeMs"
          ErrorMessage: "Performance regression detected: {{$curStep:performance.executionTimeMs}}ms vs allowed {{$var:actualBaseline * $var:MaxRegressionPercent / 100}}ms"

      # Should also meet absolute SLA
      - AssertLt:
          ConstExpr: "{{$var:SLALimit}}"
          JPathExpr: "$curStep:performance.executionTimeMs"
          ErrorMessage: "SLA violation: {{$curStep:performance.executionTimeMs}}ms > {{$var:SLALimit}}ms"

  # Step 3: Complex mathematical expression with buffer
  - Name: "Complex Expression Test"
    Input:
      Method: "POST"
      RequestUri: "{{$var:BaseUrl}}/post"
      Content: '{"data": "sample"}'
    Execution:
      DelayMs: 150
    Asserters:
      # Complex expression: baseline * regression% + buffer
      - AssertLt:
          ConstExpr: "{{$var:actualBaseline * $var:MaxRegressionPercent / 100 + $var:BufferTime}}"
          JPathExpr: "$curStep:performance.executionTimeMs"
          ErrorMessage: "Complex threshold exceeded: {{$curStep:performance.executionTimeMs}}ms vs {{$var:actualBaseline * $var:MaxRegressionPercent / 100 + $var:BufferTime}}ms"

  # Step 4: Demonstrate parentheses and operator precedence
  - Name: "Operator Precedence Test"
    Input:
      Method: "GET"
      RequestUri: "{{$var:BaseUrl}}/get"
    Execution:
      DelayMs: 75
    Asserters:
      # Expression with parentheses: (baseline + buffer) * 2
      - AssertLt:
          ConstExpr: "{{$var:(actualBaseline + $var:BufferTime) * 2}}"
          JPathExpr: "$curStep:performance.executionTimeMs"
          ErrorMessage: "Parentheses test failed: {{$curStep:performance.executionTimeMs}}ms vs {{$var:(actualBaseline + $var:BufferTime) * 2}}ms"

      # Expression without parentheses: baseline + buffer * 2 (different result)
      - AssertLt:
          ConstExpr: "{{$var:actualBaseline + $var:BufferTime * 2}}"
          JPathExpr: "$curStep:performance.executionTimeMs"
          ErrorMessage: "Precedence test failed: {{$curStep:performance.executionTimeMs}}ms vs {{$var:actualBaseline + $var:BufferTime * 2}}ms"

  # Step 5: Real-world performance budgeting scenario
  - Name: "Performance Budget Test"
    Variables:
      # Local variables for this step
      CriticalPath: "3000"  # 3 seconds
      OptimizationFactor: "0.8"  # 20% improvement expected
    Input:
      Method: "GET"
      RequestUri: "{{$var:BaseUrl}}/delay/1"
    Execution:
      DelayMs: 200
    Asserters:
      # Budget calculation: critical path * optimization factor
      - AssertLt:
          ConstExpr: "{{$var:CriticalPath * $var:OptimizationFactor}}"
          JPathExpr: "$curStep:performance.executionTimeMs"
          ErrorMessage: "Performance budget exceeded: {{$curStep:performance.executionTimeMs}}ms vs budget {{$var:CriticalPath * $var:OptimizationFactor}}ms"

      # Fallback to absolute limit if optimization not achieved
      - AssertLt:
          ConstExpr: "{{$var:CriticalPath}}"
          JPathExpr: "$curStep:performance.executionTimeMs"
          ErrorMessage: "Absolute performance limit exceeded: {{$curStep:performance.executionTimeMs}}ms vs {{$var:CriticalPath}}ms"

  # Step 6: Demonstrate error handling with invalid expressions
  - Name: "Error Handling Demo"
    Input:
      Method: "GET"
      RequestUri: "{{$var:BaseUrl}}/get"
    Execution:
      DelayMs: 50
    Asserters:
      # This should work - valid expression
      - AssertLt:
          ConstExpr: "{{$var:actualBaseline * 2}}"
          JPathExpr: "$curStep:performance.executionTimeMs"
          ErrorMessage: "Valid expression test failed"

      # This should gracefully handle non-existent variable
      - AssertLt:
          ConstExpr: 10000  # Fallback to literal value (10 seconds)
          JPathExpr: "$curStep:performance.executionTimeMs"
          ErrorMessage: "Fallback test failed"
