2025-06-30T09:37:32.5117329+05:30 [Information] [----------] 1 step from Docker PS Test
2025-06-30T09:37:32.5123155+05:30 [Information] Set Value to Step 'f9bc3a7c-36c8-47ee-9414-8e8d7051f240' name:
"Step list containers"
2025-06-30T09:37:32.5124418+05:30 [Information] Set Value to Step 'f9bc3a7c-36c8-47ee-9414-8e8d7051f240' description:
"Run docker ps command"
2025-06-30T09:37:32.5125244+05:30 [Information] [ RUN      ] Docker PS Test > Step list containers
2025-06-30T09:37:32.5128839+05:30 [Information] Executing single instance of step 'Step list containers'.
2025-06-30T09:37:32.5130539+05:30 [Information] Executing step 'Step list containers' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "f9bc3a7c-36c8-47ee-9414-8e8d7051f240",
        "tables": {},
        "variables": {},
        "name": "Step list containers",
        "description": "Run docker ps command"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:37:32.5133209+05:30 [Information] Set Value to Step 'f9bc3a7c-36c8-47ee-9414-8e8d7051f240' input:
{
  "command": "docker",
  "arguments": [
    "ps",
    "--no-trunc",
    "--filter",
    "status=running"
  ],
  "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
  "printOutput": false
}
2025-06-30T09:37:32.6870489+05:30 [Information] Set Value to Step 'f9bc3a7c-36c8-47ee-9414-8e8d7051f240' output:
{
  "stdout": "",
  "stderr": "error during connect: Get \"http://%2F%2F.%2Fpipe%2FdockerDesktopLinuxEngine/v1.45/containers/json?filters=%7B%22status%22%3A%7B%22running%22%3Atrue%7D%7D\": open //./pipe/dockerDesktopLinuxEngine: The system cannot find the file specified.",
  "exitCode": 1
}
2025-06-30T09:37:32.6872131+05:30 [Information] Set Value to Step 'f9bc3a7c-36c8-47ee-9414-8e8d7051f240' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:07:32.5128816Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "cmd"
}
2025-06-30T09:37:32.6880447+05:30 [Information] Run Asserter : {"Expression1":{"JPath":"$curStep:$.output.stdout","ErrorMesage":null},"Expression2":{"Value":"CONTAINER ID","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:37:32.6881863+05:30 [Information] Resolving path '$.output.stdout' using $curStep:
2025-06-30T09:37:32.6882629+05:30 [Information] Resolved JPath '$curStep:$.output.stdout' to ''
2025-06-30T09:37:32.6886838+05:30 [Error] Asserter error : {"Expression1":{"JPath":"$curStep:$.output.stdout","ErrorMesage":null},"Expression2":{"Value":"CONTAINER ID","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:37:32.6894030+05:30 [Error] Assert Failure :  Contains CONTAINER ID
2025-06-30T09:37:32.6896850+05:30 [Information] Failed to execute step 'Step list containers': Contain assertion failed: Expression1 should contain Expression2.
Expression1: JsonPath('$curStep:$.output.stdout') resolved to 
Expression2: Constant('CONTAINER ID') resolved to CONTAINER ID
2025-06-30T09:37:32.6897729+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:37:32.6898418+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:37:32.6898875+05:30 [Information] No performance data found for step 'Step list containers' (cmd)
2025-06-30T09:37:32.6900831+05:30 [Error] Contain assertion failed: Expression1 should contain Expression2.
Expression1: JsonPath('$curStep:$.output.stdout') resolved to 
Expression2: Constant('CONTAINER ID') resolved to CONTAINER ID
2025-06-30T09:37:32.6903375+05:30 [Error] [ FAILED   ]
2025-06-30T09:37:32.6907930+05:30 [Information] [----------] 1 step from Docker PS Test (178 ms total)
