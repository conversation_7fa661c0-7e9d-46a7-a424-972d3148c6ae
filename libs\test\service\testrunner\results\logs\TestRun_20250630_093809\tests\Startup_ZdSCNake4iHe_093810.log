2025-06-30T09:38:10.0519383+05:30 [Information] [----------] 1 step from Startup
2025-06-30T09:38:10.0546256+05:30 [Information] Set Value to Step '7037243d-79cf-4f6c-996f-7b367aa1c2f9' name:
"Initialize Database"
2025-06-30T09:38:10.0549375+05:30 [Information] Set Value to Step '7037243d-79cf-4f6c-996f-7b367aa1c2f9' description:
"Setup initial database state"
2025-06-30T09:38:10.0552097+05:30 [Information] [ RUN      ] Startup > Initialize Database
2025-06-30T09:38:10.0564573+05:30 [Information] Executing single instance of step 'Initialize Database'.
2025-06-30T09:38:10.0577148+05:30 [Information] Executing step 'Initialize Database' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "7037243d-79cf-4f6c-996f-7b367aa1c2f9",
        "tables": {},
        "variables": {},
        "name": "Initialize Database",
        "description": "Setup initial database state"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:38:10.0594554+05:30 [Information] Processing $var: path 'baseUrl'
2025-06-30T09:38:10.0612030+05:30 [Information] Checking if 'baseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:10.0637470+05:30 [Information] Resolving path 'baseUrl' using global variables.
2025-06-30T09:38:10.0640921+05:30 [Information] Resolved JPath '$var:baseUrl' to 'https://httpbin.org'
2025-06-30T09:38:10.0642159+05:30 [Information] Resolved template pattern '{{$var:baseUrl}}' to 'https://httpbin.org' in '{{$var:baseUrl}}/post'
2025-06-30T09:38:10.0644831+05:30 [Information] Resolved template '{{$var:baseUrl}}/post' to 'https://httpbin.org/post'
2025-06-30T09:38:10.0662584+05:30 [Information] Set Value to Step '7037243d-79cf-4f6c-996f-7b367aa1c2f9' input:
{
  "Method": "POST",
  "RequestUri": "https://httpbin.org/post",
  "Headers": {
    "Content-Type": "application/json"
  },
  "Content": {
    "action": "initialize_db"
  }
}
2025-06-30T09:38:11.6623217+05:30 [Information] Set Value to Step '7037243d-79cf-4f6c-996f-7b367aa1c2f9' output:
{
  "statusCode": 200,
  "reasonPhrase": "OK",
  "headers": [
    {
      "Key": "Date",
      "Value": [
        "Mon, 30 Jun 2025 04:08:17 GMT"
      ]
    },
    {
      "Key": "Connection",
      "Value": [
        "keep-alive"
      ]
    },
    {
      "Key": "Server",
      "Value": [
        "gunicorn/19.9.0"
      ]
    },
    {
      "Key": "Access-Control-Allow-Origin",
      "Value": [
        "*"
      ]
    },
    {
      "Key": "Access-Control-Allow-Credentials",
      "Value": [
        "true"
      ]
    }
  ],
  "content": {
    "args": {},
    "data": "{\r\n  \"action\": \"initialize_db\"\r\n}",
    "files": {},
    "form": {},
    "headers": {
      "Content-Length": "33",
      "Content-Type": "application/json; charset=utf-8",
      "Host": "httpbin.org",
      "X-Amzn-Trace-Id": "Root=1-68620db1-3183fc7b4c0d7f6e4770366d"
    },
    "json": {
      "action": "initialize_db"
    },
    "origin": "**************",
    "url": "https://httpbin.org/post"
  }
}
2025-06-30T09:38:11.6629234+05:30 [Information] Resolving path 'output.content.json.action' using step input/output.
2025-06-30T09:38:11.6631518+05:30 [Information] Resolved JPath 'output.content.json.action' to 'initialize_db'
2025-06-30T09:38:11.6642392+05:30 [Information] Set Value to Step '7037243d-79cf-4f6c-996f-7b367aa1c2f9' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:08:10.0564276Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:38:11.6672652+05:30 [Information] Run Asserter : {"Expression1":{"JPath":"$curStep:output.content.json.action","ErrorMesage":null},"Expression2":{"Value":null,"ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:38:11.6677372+05:30 [Information] Resolving path 'output.content.json.action' using $curStep:
2025-06-30T09:38:11.6679035+05:30 [Information] Resolved JPath '$curStep:output.content.json.action' to 'initialize_db'
2025-06-30T09:38:11.6695384+05:30 [Information] Error in EqualExpression: The method or operation is not implemented.
2025-06-30T09:38:11.6712200+05:30 [Information] Set Value to Step '7037243d-79cf-4f6c-996f-7b367aa1c2f9' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 1603,
  "startTime": "2025-06-30T04:08:10.0564276Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:38:11.6715860+05:30 [Information] Step 'Initialize Database' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "7037243d-79cf-4f6c-996f-7b367aa1c2f9",
        "tables": {},
        "variables": {},
        "name": "Initialize Database",
        "description": "Setup initial database state",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "action": "initialize_db"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:08:17 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"action\": \"initialize_db\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "33",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620db1-3183fc7b4c0d7f6e4770366d"
            },
            "json": {
              "action": "initialize_db"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 1603,
          "startTime": "2025-06-30T04:08:10.0564276Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:38:11.6719179+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:38:11.6721197+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:38:11.6722238+05:30 [Information] No performance data found for step 'Initialize Database' (http)
2025-06-30T09:38:11.6727545+05:30 [Information] [ OK       ]
2025-06-30T09:38:11.6740460+05:30 [Information] [----------] 1 step from Startup (1620 ms total)
