Name: "Performance Testing Examples"
Description: "Demonstrates how to use TestRunner as a performance testing tool with realistic DelayMs usage and performance patterns"

# Note: This example demonstrates performance testing patterns and DelayMs functionality.
# HTTP step performance timing may show 0ms in current implementation for very fast operations.
# The examples focus on demonstrating realistic performance testing patterns, DelayMs usage,
# and how to structure performance tests for production use.

Variables:
  BaseUrl: "https://httpbin.org"
  MaxResponseTime: 3000      # 3 seconds - realistic for external API
  FastResponseTime: 1000     # 1 second - fast response threshold
  SlowResponseTime: 2000     # 2 seconds - acceptable slow response
  DatabaseTimeout: 5000      # 5 seconds - database operation timeout
  ServiceStartupTime: 2000   # 2 seconds - service startup time
  authToken: "sample_auth_token_for_rpc_example"

Steps:
  # Example 1: Realistic HTTP Performance Threshold Testing
  - Name: "HTTP Response Time Test"
    Description: "Validate that HTTP requests complete within realistic time limits"
    Input:
      Method: "GET"
      RequestUri: "{{$var:BaseUrl}}/json"  # Use reliable JSON endpoint
      Headers:
        Accept: "application/json"
    Execution:
      DelayMs: 100  # Small delay to simulate real-world request spacing
    Asserters:
      # Validate response is successful
      - AssertEq:
          ConstExpr: 200
          JPathExpr: "$curStep:$.output.statusCode"

      # Performance validation - validate timing data is available
      - AssertGte:
          ConstExpr: 0  # Should be non-negative (allowing for very fast operations)
          JPathExpr: "$curStep:performance.executionTimeMs"
          ErrorMessage: "Performance timing should be non-negative"

      # Note: In current implementation, HTTP timing may show 0ms for very fast operations
      # This demonstrates the pattern for realistic performance validation

      # Validate that performance metadata is available
      - AssertEq:
          ConstExpr: "http"
          JPathExpr: "$curStep:performance.stepType"

  # Example 2: Realistic Performance Regression Testing with Baseline
  - Name: "Performance Baseline Establishment"
    Description: "Establish performance baseline for regression testing"
    Input:
      Method: "GET"
      RequestUri: "{{$var:BaseUrl}}/json"
      Headers:
        Accept: "application/json"
    Execution:
      DelayMs: 200  # Allow system to stabilize before baseline measurement
    Output:
      Store:
        baselineResponseTime: "performance.executionTimeMs"
    Asserters:
      # Functional validation
      - AssertEq:
          ConstExpr: 200
          JPathExpr: "$curStep:$.output.statusCode"

      # Baseline should be measurable (non-negative)
      - AssertGte:
          ConstExpr: 0
          JPathExpr: "$curStep:performance.executionTimeMs"
          ErrorMessage: "Baseline performance timing should be non-negative"

  - Name: "Performance Regression Check"
    Description: "Ensure performance hasn't degraded beyond 150% of baseline"
    Input:
      Method: "GET"
      RequestUri: "{{$var:BaseUrl}}/json"
      Headers:
        Accept: "application/json"
    Execution:
      DelayMs: 50  # Small delay between requests
    Asserters:
      # Functional validation
      - AssertEq:
          ConstExpr: 200
          JPathExpr: "$curStep:$.output.statusCode"

      # Performance regression check - validate timing is available
      - AssertGte:
          ConstExpr: 0
          JPathExpr: "$curStep:performance.executionTimeMs"
          ErrorMessage: "Performance timing should be non-negative"

      # Note: This demonstrates regression testing pattern
      # In production, you would compare against stored baseline values

  # Example 3: Realistic Load Testing Simulation with Controlled Timing
  - Name: "Load Testing with Controlled Request Pacing"
    Description: "Simulate realistic load with controlled request timing"
    Input:
      Method: "GET"
      RequestUri: "{{$var:BaseUrl}}/json"
      Headers:
        Accept: "application/json"
        User-Agent: "TestRunner-LoadTest/1.0"
    Execution:
      DelayMs: 250  # 250ms delay to simulate realistic user behavior and request pacing
    Asserters:
      # Request should succeed
      - AssertEq:
          ConstExpr: 200
          JPathExpr: "$curStep:$.output.statusCode"

      # Should have measurable performance data
      - AssertGte:
          ConstExpr: 0
          JPathExpr: "$curStep:performance.executionTimeMs"
          ErrorMessage: "Load test performance timing should be non-negative"

      # Track assertion overhead - should be minimal
      - AssertGte:
          ConstExpr: 0   # Assertions should be non-negative
          JPathExpr: "$curStep:performance.assertionTimeMs"
          ErrorMessage: "Assertion processing timing should be non-negative"

      # Validate request completed successfully
      - AssertNotNull:
          JPathExpr: "$curStep:$.output.content"
          ErrorMessage: "No response content received"

  # Example 4: Realistic Performance Comparison Testing
  - Name: "Fast Endpoint Baseline"
    Description: "Establish baseline performance for lightweight endpoint"
    Input:
      Method: "GET"
      RequestUri: "{{$var:BaseUrl}}/uuid"  # UUID generation is typically fast
    Execution:
      DelayMs: 300  # Allow system to stabilize
    Output:
      Store:
        fastEndpointTime: "performance.executionTimeMs"
    Asserters:
      - AssertEq:
          ConstExpr: 200
          JPathExpr: "$curStep:$.output.statusCode"

      # Fast endpoint should have measurable performance data
      - AssertGte:
          ConstExpr: 0
          JPathExpr: "$curStep:performance.executionTimeMs"
          ErrorMessage: "Fast endpoint performance timing should be non-negative"

  - Name: "Complex Endpoint Comparison"
    Description: "Compare complex endpoint against fast baseline"
    Input:
      Method: "GET"
      RequestUri: "{{$var:BaseUrl}}/headers"  # Headers endpoint does more processing
    Execution:
      DelayMs: 100  # Small delay between comparisons
    Asserters:
      - AssertEq:
          ConstExpr: 200
          JPathExpr: "$curStep:$.output.statusCode"

      # Complex endpoint should have measurable performance data
      - AssertGte:
          ConstExpr: 0
          JPathExpr: "$curStep:performance.executionTimeMs"
          ErrorMessage: "Complex endpoint performance timing should be non-negative"

      # Note: This demonstrates performance comparison pattern
      # In production, you would compare actual timing values

  # Example 5: RPC Performance Testing (when RPC steps are available)
  # Note: This example requires RPC infrastructure to be available
  # Comment out this step if RPC testing is not needed
  # - Name: "RPC Performance Test"
  #   Description: "Example of RPC performance testing with detailed metrics"
  #   Type: "rpc"
  #   Input:
  #     ProtoReference: >-
  #       {
  #         "userId": {
  #           "value": "test_user",
  #           "assigner": 1
  #         },
  #         "token": "{{$var:authToken}}"
  #       }
  #     ProtoTypeName: "alcon.interop.service.core.iam.GetUserReq"
  #   Asserters:
  #     # Functional validation
  #     - AssertNotNull:
  #         JPathExpr: "$curStep:$.output.User"
  #
  #     # RPC-specific performance metrics (available when using RPC steps)
  #     - AssertLt:
  #         ConstExpr: 1000
  #         JPathExpr: "$curStep:performanceData.requestTimeMs"
  #
  #     - AssertLt:
  #         ConstExpr: 500
  #         JPathExpr: "$curStep:performanceData.networkTimeMs"
  #
  #     - AssertNotNull:
  #         JPathExpr: "$curStep:performanceData.serviceName"

  # Example 6: Realistic Service Warm-up Testing
  - Name: "Service Warm-up Performance Test"
    Description: "Test service warm-up with realistic performance expectations"
    Input:
      Method: "GET"
      RequestUri: "{{$var:BaseUrl}}/json"
    Execution:
      DelayMs: "{{$var:ServiceStartupTime}}"  # Wait for service startup
      MaxRetries: 5
      RetryDelayMs: 1000  # Wait 1 second between warm-up attempts
      RetryUntil:
        # Keep retrying until performance data is available
        - ExpectGte:
            ConstExpr: 0  # Should have non-negative timing
            JPathExpr: "$curStep:performance.executionTimeMs"
        # And response is successful
        - ExpectEq:
            ConstExpr: 200
            JPathExpr: "$curStep:$.output.statusCode"
    Asserters:
      # Final assertion - service should be responding
      - AssertGte:
          ConstExpr: 0
          JPathExpr: "$curStep:performance.executionTimeMs"
          ErrorMessage: "Service warm-up performance timing should be non-negative"

  - Name: "Performance Stability Test"
    Description: "Ensure consistent performance over multiple attempts"
    Input:
      Method: "GET"
      RequestUri: "{{$var:BaseUrl}}/json"
    Execution:
      DelayMs: 500  # Initial delay to ensure clean start
      MaxRetries: 8
      RetryDelayMs: 200  # Short delay between stability checks
      RetryUntil:
        # Retry until we get stable responses
        - ExpectGte:
            ConstExpr: 0  # Should have non-negative timing
            JPathExpr: "$curStep:performance.executionTimeMs"
        # And assertion processing is available
        - ExpectGte:
            ConstExpr: 0    # Assertions should be non-negative
            JPathExpr: "$curStep:performance.assertionTimeMs"
        # And response is successful
        - ExpectEq:
            ConstExpr: 200
            JPathExpr: "$curStep:$.output.statusCode"
    Asserters:
      - AssertGte:
          ConstExpr: 0
          JPathExpr: "$curStep:performance.executionTimeMs"
          ErrorMessage: "Performance stability timing should be non-negative"

  - Name: "Performance Recovery Test"
    Description: "Test system recovery after simulated load with realistic expectations"
    Input:
      Method: "GET"
      RequestUri: "{{$var:BaseUrl}}/json"
    Execution:
      DelayMs: 3000  # Wait 3 seconds to simulate post-load recovery period
      MaxRetries: 6
      RetryDelayMs: 2000  # Wait 2 seconds between recovery attempts
      RequestTimeoutMs: "{{$var:DatabaseTimeout}}"  # Use realistic timeout
      RetryUntil:
        # Keep trying until performance data is available
        - ExpectGte:
            ConstExpr: 0  # Should have non-negative timing
            JPathExpr: "$curStep:performance.executionTimeMs"
        - ExpectEq:
            ConstExpr: 200
            JPathExpr: "$curStep:$.output.statusCode"
    Asserters:
      - AssertGte:
          ConstExpr: 0
          JPathExpr: "$curStep:performance.executionTimeMs"
          ErrorMessage: "System recovery performance timing should be non-negative"

      # Validate that recovery completed successfully
      - AssertEq:
          ConstExpr: 200
          JPathExpr: "$curStep:$.output.statusCode"
          ErrorMessage: "System recovery failed - HTTP status not 200"

  # Example 7: Realistic Database Connection Simulation
  - Name: "Database Connection Simulation Performance Test"
    Description: "Simulate realistic database connection timing with controlled delay"
    Type: "cmd"
    Input:
      Command: "timeout"
      Arguments:
        - "/t"
        - "2"  # Simulate 2-second database connection
        - "/nobreak"
    Execution:
      DelayMs: 500  # Simulate application startup delay before DB connection
    Asserters:
      # Functional validation - connection should succeed
      - AssertEq:
          ConstExpr: 0
          JPathExpr: "$curStep:$.output.exitCode"
          ErrorMessage: "Database connection simulation failed"

      # Performance validation - validate that timing data is available
      - AssertGte:
          ConstExpr: 0  # Should be non-negative
          JPathExpr: "$curStep:performance.executionTimeMs"

      # Validate performance metadata
      - AssertEq:
          ConstExpr: "cmd"
          JPathExpr: "$curStep:performance.stepType"

  - Name: "System Information Performance Test"
    Description: "Test system information gathering performance with realistic expectations"
    Type: "cmd"
    Input:
      Command: "systeminfo"
      Arguments:
        - "/fo"
        - "csv"
    Execution:
      DelayMs: 1000  # Allow system to stabilize before heavy system call
    Asserters:
      # Functional validation
      - AssertEq:
          ConstExpr: 0
          JPathExpr: "$curStep:$.output.exitCode"
          ErrorMessage: "System information gathering failed"

      # Performance validation - validate timing data availability
      - AssertGte:
          ConstExpr: 0  # Should be non-negative
          JPathExpr: "$curStep:performance.executionTimeMs"

      # Validate performance metadata
      - AssertNotNull:
          JPathExpr: "$curStep:performance.startTime"
          ErrorMessage: "Performance start time not recorded"

  - Name: "Directory Listing Performance Test"
    Description: "Demonstrate performance testing pattern for file system operations"
    Type: "cmd"
    Input:
      Command: "dir"
      Arguments:
        - "C:\\Windows"
        - "/b"
    Asserters:
      # Functional validation
      - AssertEq:
          ConstExpr: 0
          JPathExpr: "$curStep:$.output.exitCode"

      # Performance validation - validate timing data availability
      - AssertGte:
          ConstExpr: 0  # Should be non-negative
          JPathExpr: "$curStep:performance.executionTimeMs"

      # Validate performance metadata
      - AssertNotNull:
          JPathExpr: "$curStep:performance.endTime"

  # Example 8: Realistic Network Service Availability Testing
  - Name: "Network Service Availability Performance Test"
    Description: "Test network service availability with realistic timing and retry logic"
    Type: "cmd"
    Input:
      Command: "ping"
      Arguments:
        - "*******"  # Google DNS - reliable external service
        - "-n"
        - "1"
    Execution:
      DelayMs: 1500      # Wait 1.5 seconds before starting (simulate network initialization)
      MaxRetries: 4
      RetryDelayMs: 1000  # Wait 1 second between network attempts
      RetryUntil:
        # Retry until network service responds successfully
        - ExpectEq:
            ConstExpr: 0
            JPathExpr: "$curStep:$.output.exitCode"
        # And performance data is available
        - ExpectGte:
            ConstExpr: 0  # Should be non-negative
            JPathExpr: "$curStep:performance.executionTimeMs"
    Asserters:
      - AssertEq:
          ConstExpr: 0
          JPathExpr: "$curStep:$.output.exitCode"
          ErrorMessage: "Network service unavailable after retries"

      - AssertGte:
          ConstExpr: 0  # Should be non-negative
          JPathExpr: "$curStep:performance.executionTimeMs"

  # Example 9: Advanced Performance Testing with Controlled Timing
  - Name: "API Rate Limiting Compliance Test"
    Description: "Test API rate limiting compliance with controlled request timing"
    Input:
      Method: "GET"
      RequestUri: "{{$var:BaseUrl}}/json"
      Headers:
        Accept: "application/json"
        X-Test-Type: "rate-limit-test"
    Execution:
      DelayMs: 2000  # Wait 2 seconds to respect rate limits (30 requests/minute = 2s interval)
    Asserters:
      - AssertEq:
          ConstExpr: 200
          JPathExpr: "$curStep:$.output.statusCode"
          ErrorMessage: "API request failed - possible rate limiting"

      # Should have measurable performance data
      - AssertGte:
          ConstExpr: 0
          JPathExpr: "$curStep:performance.executionTimeMs"
          ErrorMessage: "API rate limiting test performance timing should be non-negative"

  # Example 10: Performance Baseline and Comparison Testing
  - Name: "Performance Baseline - Fast Operation"
    Description: "Establish baseline for fast operations using echo"
    Type: "cmd"
    Input:
      Command: "echo"
      Arguments:
        - "Fast operation completed"
    Output:
      Store:
        fastOperationTime: "performance.executionTimeMs"
    Asserters:
      - AssertEq:
          ConstExpr: 0
          JPathExpr: "$curStep:$.output.exitCode"
      - AssertGte:
          ConstExpr: 0
          JPathExpr: "$curStep:performance.executionTimeMs"

  - Name: "Performance Comparison - Slower Operation"
    Description: "Compare against baseline to detect performance regression"
    Type: "cmd"
    Input:
      Command: "timeout"
      Arguments:
        - "/t"
        - "1"
        - "/nobreak"
    Asserters:
      - AssertEq:
          ConstExpr: 0
          JPathExpr: "$curStep:$.output.exitCode"

      # Performance validation - validate timing data availability
      - AssertGte:
          ConstExpr: 0  # Should be non-negative
          JPathExpr: "$curStep:performance.executionTimeMs"

      # Validate performance metadata
      - AssertEq:
          ConstExpr: "cmd"
          JPathExpr: "$curStep:performance.stepType"

  - Name: "Process Enumeration Performance Test"
    Description: "Test process enumeration performance"
    Type: "cmd"
    Input:
      Command: "tasklist"
      Arguments:
        - "/fo"
        - "csv"
    Asserters:
      - AssertEq:
          ConstExpr: 0
          JPathExpr: "$curStep:$.output.exitCode"

      # Performance validation - validate timing data availability
      - AssertGte:
          ConstExpr: 0  # Should be non-negative
          JPathExpr: "$curStep:performance.executionTimeMs"

      # Validate performance metadata
      - AssertNotNull:
          JPathExpr: "$curStep:performance.stepType"

  # Example 11: Mathematical Expressions in Performance Testing
  - Name: "Mathematical Expressions Demo"
    Description: "Demonstrates using mathematical expressions for dynamic performance thresholds"
    Variables:
      # Performance baselines and factors
      BaselineResponseTime: "500"    # 500ms baseline
      MaxRegressionPercent: "120"    # Allow 20% regression (120% of baseline)
      BufferTime: "100"              # 100ms buffer for network variance
      LoadFactor: "1.5"              # 50% load increase factor
      P95Multiplier: "2.0"           # P95 should be 2x average

      # Business thresholds
      UserToleranceMs: "2000"        # 2 seconds max user tolerance
      SLAThreshold: "3000"           # 3 seconds SLA limit
    Input:
      Method: "GET"
      RequestUri: "{{$var:BaseUrl}}/delay/1"  # 1 second delay endpoint
    Execution:
      DelayMs: 200  # Realistic delay between requests
    Asserters:
      # Basic success validation
      - AssertEq:
          ConstExpr: 200
          JPathExpr: "$curStep:$.output.statusCode"
          ErrorMessage: "HTTP request should succeed"

      # Mathematical Expression 1: Regression threshold calculation
      # Formula: baseline * regression_percent / 100
      - AssertLt:
          ConstExpr: "{{$var:BaselineResponseTime * $var:MaxRegressionPercent / 100}}"
          JPathExpr: "$curStep:performance.executionTimeMs"
          ErrorMessage: "Performance regression detected: {{$curStep:performance.executionTimeMs}}ms exceeds {{$var:BaselineResponseTime * $var:MaxRegressionPercent / 100}}ms ({{$var:BaselineResponseTime}}ms * {{$var:MaxRegressionPercent}}% / 100)"

      # Mathematical Expression 2: Complex threshold with buffer
      # Formula: (baseline * load_factor) + buffer
      - AssertLt:
          ConstExpr: "{{$var:BaselineResponseTime * $var:LoadFactor + $var:BufferTime}}"
          JPathExpr: "$curStep:performance.executionTimeMs"
          ErrorMessage: "Load test threshold exceeded: {{$curStep:performance.executionTimeMs}}ms vs {{$var:BaselineResponseTime * $var:LoadFactor + $var:BufferTime}}ms"

      # Mathematical Expression 3: Parentheses for order of operations
      # Formula: (baseline + buffer) * load_factor
      - AssertLt:
          ConstExpr: "{{$var:(BaselineResponseTime + $var:BufferTime) * $var:LoadFactor}}"
          JPathExpr: "$curStep:performance.executionTimeMs"
          ErrorMessage: "Complex calculation failed: {{$curStep:performance.executionTimeMs}}ms vs {{$var:(BaselineResponseTime + $var:BufferTime) * $var:LoadFactor}}ms"

      # Mathematical Expression 4: P95 performance calculation
      # Formula: baseline * P95_multiplier
      - AssertLt:
          ConstExpr: "{{$var:BaselineResponseTime * $var:P95Multiplier}}"
          JPathExpr: "$curStep:performance.executionTimeMs"
          ErrorMessage: "P95 performance target missed: {{$curStep:performance.executionTimeMs}}ms exceeds {{$var:BaselineResponseTime * $var:P95Multiplier}}ms"

      # Mathematical Expression 5: Business rule validation
      # Formula: user_tolerance - safety_margin (10% of tolerance)
      - AssertLt:
          ConstExpr: "{{$var:UserToleranceMs - $var:UserToleranceMs * 0.1}}"
          JPathExpr: "$curStep:performance.executionTimeMs"
          ErrorMessage: "User experience threshold exceeded: {{$curStep:performance.executionTimeMs}}ms vs {{$var:UserToleranceMs - $var:UserToleranceMs * 0.1}}ms (90% of user tolerance)"

      # Mathematical Expression 6: Absolute SLA validation
      - AssertLt:
          ConstExpr: "{{$var:SLAThreshold}}"
          JPathExpr: "$curStep:performance.executionTimeMs"
          ErrorMessage: "SLA violation: {{$curStep:performance.executionTimeMs}}ms exceeds {{$var:SLAThreshold}}ms SLA limit"
    Output:
      Store:
        # Store actual performance for use in subsequent calculations
        actualResponseTime: "performance.executionTimeMs"

# Table data for load testing simulation
Tables:
  - Name: "RequestTable"
    Data:
      - RequestId: "REQ001"
        Method: "GET"
        Endpoint: "/json"
      - RequestId: "REQ002"
        Method: "GET"
        Endpoint: "/uuid"
      - RequestId: "REQ003"
        Method: "GET"
        Endpoint: "/ip"
      - RequestId: "REQ004"
        Method: "GET"
        Endpoint: "/json"
      - RequestId: "REQ005"
        Method: "GET"
        Endpoint: "/headers"