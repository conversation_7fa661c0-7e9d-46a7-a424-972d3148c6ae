2025-06-30T08:57:33.4442666+05:30 [Information] [----------] 1 step from Teardown
2025-06-30T08:57:33.4450105+05:30 [Information] Set Value to Step '9d631c18-e188-4672-aeca-d0c3c1acb130' name:
"Clean up Database"
2025-06-30T08:57:33.4451524+05:30 [Information] Set Value to Step '9d631c18-e188-4672-aeca-d0c3c1acb130' description:
"Tear down the database state"
2025-06-30T08:57:33.4452243+05:30 [Information] [ RUN      ] Teardown > Clean up Database
2025-06-30T08:57:33.4454716+05:30 [Information] Executing single instance of step 'Clean up Database'.
2025-06-30T08:57:33.4456477+05:30 [Information] Executing step 'Clean up Database' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "9d631c18-e188-4672-aeca-d0c3c1acb130",
        "tables": {},
        "variables": {},
        "name": "Clean up Database",
        "description": "Tear down the database state"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T08:57:33.4458709+05:30 [Information] Processing $var: path 'baseUrl'
2025-06-30T08:57:33.4460016+05:30 [Information] Checking if 'baseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T08:57:33.4460810+05:30 [Information] Resolving path 'baseUrl' using global variables.
2025-06-30T08:57:33.4461472+05:30 [Information] Resolved JPath '$var:baseUrl' to 'https://httpbin.org'
2025-06-30T08:57:33.4462251+05:30 [Information] Resolved template pattern '{{$var:baseUrl}}' to 'https://httpbin.org' in '{{$var:baseUrl}}/post'
2025-06-30T08:57:33.4462938+05:30 [Information] Resolved template '{{$var:baseUrl}}/post' to 'https://httpbin.org/post'
2025-06-30T08:57:33.4464756+05:30 [Information] Set Value to Step '9d631c18-e188-4672-aeca-d0c3c1acb130' input:
{
  "Method": "POST",
  "RequestUri": "https://httpbin.org/post",
  "Headers": {
    "Content-Type": "application/json"
  },
  "Content": {
    "action": "cleanup_db"
  }
}
2025-06-30T08:57:34.1882498+05:30 [Information] Set Value to Step '9d631c18-e188-4672-aeca-d0c3c1acb130' output:
{
  "statusCode": 200,
  "reasonPhrase": "OK",
  "headers": [
    {
      "Key": "Date",
      "Value": [
        "Mon, 30 Jun 2025 03:27:39 GMT"
      ]
    },
    {
      "Key": "Connection",
      "Value": [
        "keep-alive"
      ]
    },
    {
      "Key": "Server",
      "Value": [
        "gunicorn/19.9.0"
      ]
    },
    {
      "Key": "Access-Control-Allow-Origin",
      "Value": [
        "*"
      ]
    },
    {
      "Key": "Access-Control-Allow-Credentials",
      "Value": [
        "true"
      ]
    }
  ],
  "content": {
    "args": {},
    "data": "{\r\n  \"action\": \"cleanup_db\"\r\n}",
    "files": {},
    "form": {},
    "headers": {
      "Content-Length": "30",
      "Content-Type": "application/json; charset=utf-8",
      "Host": "httpbin.org",
      "X-Amzn-Trace-Id": "Root=1-6862042b-16f529091c54c650150500c6"
    },
    "json": {
      "action": "cleanup_db"
    },
    "origin": "**************",
    "url": "https://httpbin.org/post"
  }
}
2025-06-30T08:57:34.1887437+05:30 [Information] Set Value to Step '9d631c18-e188-4672-aeca-d0c3c1acb130' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T03:27:33.4454679Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T08:57:34.1892213+05:30 [Information] Set Value to Step '9d631c18-e188-4672-aeca-d0c3c1acb130' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 742,
  "startTime": "2025-06-30T03:27:33.4454679Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T08:57:34.1895381+05:30 [Information] Step 'Clean up Database' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "9d631c18-e188-4672-aeca-d0c3c1acb130",
        "tables": {},
        "variables": {},
        "name": "Clean up Database",
        "description": "Tear down the database state",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "action": "cleanup_db"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 03:27:39 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"action\": \"cleanup_db\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "30",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-6862042b-16f529091c54c650150500c6"
            },
            "json": {
              "action": "cleanup_db"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 742,
          "startTime": "2025-06-30T03:27:33.4454679Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T08:57:34.1896962+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T08:57:34.1898258+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T08:57:34.1900260+05:30 [Information] No performance data found for step 'Clean up Database' (http)
2025-06-30T08:57:34.1901238+05:30 [Information] [ OK       ]
2025-06-30T08:57:34.1905332+05:30 [Information] [----------] 1 step from Teardown (745 ms total)
