2025-06-30T09:38:16.1211721+05:30 [Information] [----------] 1 step from Demo HTTP Table Test Iteration
2025-06-30T09:38:16.1224942+05:30 [Information] Default-mapped column 'User' to variable 'User' with value '{
  "Username": "<PERSON>",
  "Email": "<EMAIL>"
}'
2025-06-30T09:38:16.1226195+05:30 [Information] Default-mapped column 'OrgName' to variable 'OrgName' with value 'Org1'
2025-06-30T09:38:16.1226943+05:30 [Information] Processing $var: path 'User.Username'
2025-06-30T09:38:16.1227448+05:30 [Information] Checking if 'User.Username' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=True, result=False
2025-06-30T09:38:16.1227879+05:30 [Information] Resolving path 'User.Username' using global variables.
2025-06-30T09:38:16.1261232+05:30 [Information] Resolved JPath '$var:User.Username' to 'Tom'
2025-06-30T09:38:16.1262251+05:30 [Information] Resolved template pattern '{{$var:User.Username}}' to 'Tom' in '{{$var:User.Username}}'
2025-06-30T09:38:16.1263004+05:30 [Information] Resolved template '{{$var:User.Username}}' to 'Tom'
2025-06-30T09:38:16.1263851+05:30 [Information] Mapped column '{{$var:User.Username}}' to variable 'username' with value 'Tom'
2025-06-30T09:38:16.1264388+05:30 [Information] Processing $var: path 'User.Email'
2025-06-30T09:38:16.1265651+05:30 [Information] Checking if 'User.Email' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=True, result=False
2025-06-30T09:38:16.1266601+05:30 [Information] Resolving path 'User.Email' using global variables.
2025-06-30T09:38:16.1267108+05:30 [Information] Resolved JPath '$var:User.Email' to '<EMAIL>'
2025-06-30T09:38:16.1267558+05:30 [Information] Resolved template pattern '{{$var:User.Email}}' to '<EMAIL>' in '{{$var:User.Email}}'
2025-06-30T09:38:16.1267966+05:30 [Information] Resolved template '{{$var:User.Email}}' to '<EMAIL>'
2025-06-30T09:38:16.1268754+05:30 [Information] Mapped column '{{$var:User.Email}}' to variable 'email' with value '<EMAIL>'
2025-06-30T09:38:16.1271379+05:30 [Information] Test Iteration 1/2: Demo HTTP Table Test Iteration
2025-06-30T09:38:16.1273438+05:30 [Information] Processing $var: path 'username'
2025-06-30T09:38:16.1274615+05:30 [Information] Checking if 'username' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:16.1275149+05:30 [Information] Resolving path 'username' using global variables.
2025-06-30T09:38:16.1275527+05:30 [Information] Resolved JPath '$var:username' to 'Tom'
2025-06-30T09:38:16.1276028+05:30 [Information] Resolved template pattern '{{$var:username}}' to 'Tom' in 'Fetching user {{$var:username}} details'
2025-06-30T09:38:16.1276450+05:30 [Information] Resolved template 'Fetching user {{$var:username}} details' to 'Fetching user Tom details'
2025-06-30T09:38:16.1276960+05:30 [Information] Set Value to Step '881d007c-6e3f-47c6-8767-36a1ce8d3c58' name:
"Fetching user Tom details"
2025-06-30T09:38:16.1277494+05:30 [Information] Set Value to Step '881d007c-6e3f-47c6-8767-36a1ce8d3c58' description:
"Run step for each user"
2025-06-30T09:38:16.1277893+05:30 [Information] [ RUN      ] Demo HTTP Table Test Iteration > Fetching user Tom details
2025-06-30T09:38:16.1281248+05:30 [Information] Executing single instance of step 'Fetching user {{$var:username}} details'.
2025-06-30T09:38:16.1282369+05:30 [Information] Executing step 'Fetching user Tom details' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "881d007c-6e3f-47c6-8767-36a1ce8d3c58",
        "tables": {},
        "variables": {},
        "name": "Fetching user Tom details",
        "description": "Run step for each user"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaseUrl": "https://httpbin.org",
      "User": {
        "Username": "Tom",
        "Email": "<EMAIL>"
      },
      "OrgName": "Org1",
      "username": "Tom",
      "email": "<EMAIL>"
    },
    "tables": {
      "UserTable": [
        {
          "User": {
            "Username": "Tom",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org1"
        },
        {
          "User": {
            "Username": "John",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org2"
        }
      ]
    },
    "context": {
      "repeatForTable": "UserTable",
      "row": {
        "User": {
          "Username": "Tom",
          "Email": "<EMAIL>"
        },
        "OrgName": "Org1"
      },
      "rowIndex": 0
    }
  }
}
2025-06-30T09:38:16.1283242+05:30 [Information] Processing $var: path 'BaseUrl'
2025-06-30T09:38:16.1283799+05:30 [Information] Checking if 'BaseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:16.1284385+05:30 [Information] Resolving path 'BaseUrl' using global variables.
2025-06-30T09:38:16.1284915+05:30 [Information] Resolved JPath '$var:BaseUrl' to 'https://httpbin.org'
2025-06-30T09:38:16.1286255+05:30 [Information] Resolved template pattern '{{$var:BaseUrl}}' to 'https://httpbin.org' in '{{$var:BaseUrl}}/post'
2025-06-30T09:38:16.1288123+05:30 [Information] Resolved template '{{$var:BaseUrl}}/post' to 'https://httpbin.org/post'
2025-06-30T09:38:16.1289585+05:30 [Information] Processing $var: path 'username'
2025-06-30T09:38:16.1290438+05:30 [Information] Checking if 'username' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:16.1291198+05:30 [Information] Resolving path 'username' using global variables.
2025-06-30T09:38:16.1293379+05:30 [Information] Resolved JPath '$var:username' to 'Tom'
2025-06-30T09:38:16.1295423+05:30 [Information] Resolved template pattern '{{$var:username}}' to 'Tom' in '{
  "username": "{{$var:username}}",
  "email": "{{$var:email}}",
  "org": "{{$var:OrgName}}"
}'
2025-06-30T09:38:16.1296583+05:30 [Information] Processing $var: path 'email'
2025-06-30T09:38:16.1298453+05:30 [Information] Checking if 'email' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:16.1305376+05:30 [Information] Resolving path 'email' using global variables.
2025-06-30T09:38:16.1308543+05:30 [Information] Resolved JPath '$var:email' to '<EMAIL>'
2025-06-30T09:38:16.1311038+05:30 [Information] Resolved template pattern '{{$var:email}}' to '<EMAIL>' in '{
  "username": "{{$var:username}}",
  "email": "{{$var:email}}",
  "org": "{{$var:OrgName}}"
}'
2025-06-30T09:38:16.1313031+05:30 [Information] Processing $var: path 'OrgName'
2025-06-30T09:38:16.1315361+05:30 [Information] Checking if 'OrgName' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:16.1318599+05:30 [Information] Resolving path 'OrgName' using global variables.
2025-06-30T09:38:16.1320466+05:30 [Information] Resolved JPath '$var:OrgName' to 'Org1'
2025-06-30T09:38:16.1323375+05:30 [Information] Resolved template pattern '{{$var:OrgName}}' to 'Org1' in '{
  "username": "{{$var:username}}",
  "email": "{{$var:email}}",
  "org": "{{$var:OrgName}}"
}'
2025-06-30T09:38:16.1325724+05:30 [Information] Resolved template '{
  "username": "{{$var:username}}",
  "email": "{{$var:email}}",
  "org": "{{$var:OrgName}}"
}' to '{
  "username": "Tom",
  "email": "<EMAIL>",
  "org": "Org1"
}'
2025-06-30T09:38:16.1328417+05:30 [Information] Set Value to Step '881d007c-6e3f-47c6-8767-36a1ce8d3c58' input:
{
  "Method": "POST",
  "RequestUri": "https://httpbin.org/post",
  "Headers": {
    "Content-Type": "application/json"
  },
  "Content": {
    "username": "Tom",
    "email": "<EMAIL>",
    "org": "Org1"
  }
}
2025-06-30T09:38:16.9023087+05:30 [Information] Set Value to Step '881d007c-6e3f-47c6-8767-36a1ce8d3c58' output:
{
  "statusCode": 200,
  "reasonPhrase": "OK",
  "headers": [
    {
      "Key": "Date",
      "Value": [
        "Mon, 30 Jun 2025 04:08:22 GMT"
      ]
    },
    {
      "Key": "Connection",
      "Value": [
        "keep-alive"
      ]
    },
    {
      "Key": "Server",
      "Value": [
        "gunicorn/19.9.0"
      ]
    },
    {
      "Key": "Access-Control-Allow-Origin",
      "Value": [
        "*"
      ]
    },
    {
      "Key": "Access-Control-Allow-Credentials",
      "Value": [
        "true"
      ]
    }
  ],
  "content": {
    "args": {},
    "data": "{\r\n  \"username\": \"Tom\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"org\": \"Org1\"\r\n}",
    "files": {},
    "form": {},
    "headers": {
      "Content-Length": "74",
      "Content-Type": "application/json; charset=utf-8",
      "Host": "httpbin.org",
      "X-Amzn-Trace-Id": "Root=1-68620db6-27b56e9149d4563c1f76e549"
    },
    "json": {
      "email": "<EMAIL>",
      "org": "Org1",
      "username": "Tom"
    },
    "origin": "**************",
    "url": "https://httpbin.org/post"
  }
}
2025-06-30T09:38:16.9040075+05:30 [Information] Resolving path 'output.content.json.url' using step input/output.
2025-06-30T09:38:16.9045512+05:30 [Information] Resolved JPath 'output.content.json.url' to ''
2025-06-30T09:38:16.9053193+05:30 [Information] Set Value to Step '881d007c-6e3f-47c6-8767-36a1ce8d3c58' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:08:16.1281225Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:38:16.9057903+05:30 [Information] Run Asserter : {"Expression1":{"Value":"200","ErrorMesage":null},"Expression2":{"JPath":"output.statusCode","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:38:16.9060255+05:30 [Information] Resolving path 'output.statusCode' using step input/output.
2025-06-30T09:38:16.9065698+05:30 [Information] Resolved JPath 'output.statusCode' to '200'
2025-06-30T09:38:16.9068734+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$test:row.User.Username}}","ErrorMesage":null},"Expression2":{"JPath":"output.content.json.username","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:38:16.9070608+05:30 [Information] Resolving path 'row.User.Username' using test context.
2025-06-30T09:38:16.9072032+05:30 [Information] Resolved JPath '$test:row.User.Username' to 'Tom'
2025-06-30T09:38:16.9073999+05:30 [Information] Resolved template pattern '{{$test:row.User.Username}}' to 'Tom' in '{{$test:row.User.Username}}'
2025-06-30T09:38:16.9075210+05:30 [Information] Resolved template '{{$test:row.User.Username}}' to 'Tom'
2025-06-30T09:38:16.9077278+05:30 [Information] Resolving path 'output.content.json.username' using step input/output.
2025-06-30T09:38:16.9078561+05:30 [Information] Resolved JPath 'output.content.json.username' to 'Tom'
2025-06-30T09:38:16.9079842+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$test:row.User.Email}}","ErrorMesage":null},"Expression2":{"JPath":"output.content.json.email","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:38:16.9080958+05:30 [Information] Resolving path 'row.User.Email' using test context.
2025-06-30T09:38:16.9082032+05:30 [Information] Resolved JPath '$test:row.User.Email' to '<EMAIL>'
2025-06-30T09:38:16.9083003+05:30 [Information] Resolved template pattern '{{$test:row.User.Email}}' to '<EMAIL>' in '{{$test:row.User.Email}}'
2025-06-30T09:38:16.9084052+05:30 [Information] Resolved template '{{$test:row.User.Email}}' to '<EMAIL>'
2025-06-30T09:38:16.9085614+05:30 [Information] Resolving path 'output.content.json.email' using step input/output.
2025-06-30T09:38:16.9087996+05:30 [Information] Resolved JPath 'output.content.json.email' to '<EMAIL>'
2025-06-30T09:38:16.9089152+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$test:row.OrgName}}","ErrorMesage":null},"Expression2":{"JPath":"output.content.json.org","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:38:16.9090794+05:30 [Information] Resolving path 'row.OrgName' using test context.
2025-06-30T09:38:16.9091794+05:30 [Information] Resolved JPath '$test:row.OrgName' to 'Org1'
2025-06-30T09:38:16.9092640+05:30 [Information] Resolved template pattern '{{$test:row.OrgName}}' to 'Org1' in '{{$test:row.OrgName}}'
2025-06-30T09:38:16.9093493+05:30 [Information] Resolved template '{{$test:row.OrgName}}' to 'Org1'
2025-06-30T09:38:16.9095041+05:30 [Information] Resolving path 'output.content.json.org' using step input/output.
2025-06-30T09:38:16.9095910+05:30 [Information] Resolved JPath 'output.content.json.org' to 'Org1'
2025-06-30T09:38:16.9096912+05:30 [Information] Set Value to Step '881d007c-6e3f-47c6-8767-36a1ce8d3c58' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 776,
  "startTime": "2025-06-30T04:08:16.1281225Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:38:16.9097924+05:30 [Information] Step 'Fetching user Tom details' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "881d007c-6e3f-47c6-8767-36a1ce8d3c58",
        "tables": {},
        "variables": {},
        "name": "Fetching user Tom details",
        "description": "Run step for each user",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "Tom",
            "email": "<EMAIL>",
            "org": "Org1"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:08:22 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"Tom\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"org\": \"Org1\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "74",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620db6-27b56e9149d4563c1f76e549"
            },
            "json": {
              "email": "<EMAIL>",
              "org": "Org1",
              "username": "Tom"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 776,
          "startTime": "2025-06-30T04:08:16.1281225Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaseUrl": "https://httpbin.org",
      "User": {
        "Username": "Tom",
        "Email": "<EMAIL>"
      },
      "OrgName": "Org1",
      "username": "Tom",
      "email": "<EMAIL>"
    },
    "tables": {
      "UserTable": [
        {
          "User": {
            "Username": "Tom",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org1"
        },
        {
          "User": {
            "Username": "John",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org2"
        }
      ]
    },
    "context": {
      "repeatForTable": "UserTable",
      "row": {
        "User": {
          "Username": "Tom",
          "Email": "<EMAIL>"
        },
        "OrgName": "Org1"
      },
      "rowIndex": 0
    }
  }
}
2025-06-30T09:38:16.9098930+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:38:16.9099679+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:38:16.9100363+05:30 [Information] No performance data found for step 'Fetching user {{$var:username}} details' (http)
2025-06-30T09:38:16.9101080+05:30 [Information] [ OK       ]
2025-06-30T09:38:16.9107410+05:30 [Information] Default-mapped column 'User' to variable 'User' with value '{
  "Username": "John",
  "Email": "<EMAIL>"
}'
2025-06-30T09:38:16.9108608+05:30 [Information] Default-mapped column 'OrgName' to variable 'OrgName' with value 'Org2'
2025-06-30T09:38:16.9109196+05:30 [Information] Processing $var: path 'User.Username'
2025-06-30T09:38:16.9109819+05:30 [Information] Checking if 'User.Username' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=True, result=False
2025-06-30T09:38:16.9110259+05:30 [Information] Resolving path 'User.Username' using global variables.
2025-06-30T09:38:16.9110658+05:30 [Information] Resolved JPath '$var:User.Username' to 'John'
2025-06-30T09:38:16.9111785+05:30 [Information] Resolved template pattern '{{$var:User.Username}}' to 'John' in '{{$var:User.Username}}'
2025-06-30T09:38:16.9112614+05:30 [Information] Resolved template '{{$var:User.Username}}' to 'John'
2025-06-30T09:38:16.9113401+05:30 [Information] Mapped column '{{$var:User.Username}}' to variable 'username' with value 'John'
2025-06-30T09:38:16.9113900+05:30 [Information] Processing $var: path 'User.Email'
2025-06-30T09:38:16.9114299+05:30 [Information] Checking if 'User.Email' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=True, result=False
2025-06-30T09:38:16.9114697+05:30 [Information] Resolving path 'User.Email' using global variables.
2025-06-30T09:38:16.9115079+05:30 [Information] Resolved JPath '$var:User.Email' to '<EMAIL>'
2025-06-30T09:38:16.9115450+05:30 [Information] Resolved template pattern '{{$var:User.Email}}' to '<EMAIL>' in '{{$var:User.Email}}'
2025-06-30T09:38:16.9116043+05:30 [Information] Resolved template '{{$var:User.Email}}' to '<EMAIL>'
2025-06-30T09:38:16.9116768+05:30 [Information] Mapped column '{{$var:User.Email}}' to variable 'email' with value '<EMAIL>'
2025-06-30T09:38:16.9119388+05:30 [Information] Test Iteration 2/2: Demo HTTP Table Test Iteration
2025-06-30T09:38:16.9121239+05:30 [Information] Processing $var: path 'username'
2025-06-30T09:38:16.9121722+05:30 [Information] Checking if 'username' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:16.9122146+05:30 [Information] Resolving path 'username' using global variables.
2025-06-30T09:38:16.9122525+05:30 [Information] Resolved JPath '$var:username' to 'John'
2025-06-30T09:38:16.9122885+05:30 [Information] Resolved template pattern '{{$var:username}}' to 'John' in 'Fetching user {{$var:username}} details'
2025-06-30T09:38:16.9123275+05:30 [Information] Resolved template 'Fetching user {{$var:username}} details' to 'Fetching user John details'
2025-06-30T09:38:16.9123714+05:30 [Information] Set Value to Step 'f61dab35-f89b-41e7-9868-64ac10b8f6c9' name:
"Fetching user John details"
2025-06-30T09:38:16.9124117+05:30 [Information] Set Value to Step 'f61dab35-f89b-41e7-9868-64ac10b8f6c9' description:
"Run step for each user"
2025-06-30T09:38:16.9124482+05:30 [Information] [ RUN      ] Demo HTTP Table Test Iteration > Fetching user John details
2025-06-30T09:38:16.9128568+05:30 [Information] Executing single instance of step 'Fetching user {{$var:username}} details'.
2025-06-30T09:38:16.9129775+05:30 [Information] Executing step 'Fetching user John details' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "881d007c-6e3f-47c6-8767-36a1ce8d3c58",
        "tables": {},
        "variables": {},
        "name": "Fetching user Tom details",
        "description": "Run step for each user",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "Tom",
            "email": "<EMAIL>",
            "org": "Org1"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:08:22 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"Tom\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"org\": \"Org1\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "74",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620db6-27b56e9149d4563c1f76e549"
            },
            "json": {
              "email": "<EMAIL>",
              "org": "Org1",
              "username": "Tom"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 776,
          "startTime": "2025-06-30T04:08:16.1281225Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      },
      {
        "id": "f61dab35-f89b-41e7-9868-64ac10b8f6c9",
        "tables": {},
        "variables": {},
        "name": "Fetching user John details",
        "description": "Run step for each user"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaseUrl": "https://httpbin.org",
      "User": {
        "Username": "John",
        "Email": "<EMAIL>"
      },
      "OrgName": "Org2",
      "username": "John",
      "email": "<EMAIL>"
    },
    "tables": {
      "UserTable": [
        {
          "User": {
            "Username": "Tom",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org1"
        },
        {
          "User": {
            "Username": "John",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org2"
        }
      ]
    },
    "context": {
      "repeatForTable": "UserTable",
      "row": {
        "User": {
          "Username": "John",
          "Email": "<EMAIL>"
        },
        "OrgName": "Org2"
      },
      "rowIndex": 1
    }
  }
}
2025-06-30T09:38:16.9130426+05:30 [Information] Processing $var: path 'BaseUrl'
2025-06-30T09:38:16.9131019+05:30 [Information] Checking if 'BaseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:16.9131450+05:30 [Information] Resolving path 'BaseUrl' using global variables.
2025-06-30T09:38:16.9131814+05:30 [Information] Resolved JPath '$var:BaseUrl' to 'https://httpbin.org'
2025-06-30T09:38:16.9132196+05:30 [Information] Resolved template pattern '{{$var:BaseUrl}}' to 'https://httpbin.org' in '{{$var:BaseUrl}}/post'
2025-06-30T09:38:16.9132569+05:30 [Information] Resolved template '{{$var:BaseUrl}}/post' to 'https://httpbin.org/post'
2025-06-30T09:38:16.9133142+05:30 [Information] Processing $var: path 'username'
2025-06-30T09:38:16.9133529+05:30 [Information] Checking if 'username' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:16.9133909+05:30 [Information] Resolving path 'username' using global variables.
2025-06-30T09:38:16.9134288+05:30 [Information] Resolved JPath '$var:username' to 'John'
2025-06-30T09:38:16.9134808+05:30 [Information] Resolved template pattern '{{$var:username}}' to 'John' in '{
  "username": "{{$var:username}}",
  "email": "{{$var:email}}",
  "org": "{{$var:OrgName}}"
}'
2025-06-30T09:38:16.9135261+05:30 [Information] Processing $var: path 'email'
2025-06-30T09:38:16.9135638+05:30 [Information] Checking if 'email' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:16.9136016+05:30 [Information] Resolving path 'email' using global variables.
2025-06-30T09:38:16.9136397+05:30 [Information] Resolved JPath '$var:email' to '<EMAIL>'
2025-06-30T09:38:16.9137022+05:30 [Information] Resolved template pattern '{{$var:email}}' to '<EMAIL>' in '{
  "username": "{{$var:username}}",
  "email": "{{$var:email}}",
  "org": "{{$var:OrgName}}"
}'
2025-06-30T09:38:16.9137408+05:30 [Information] Processing $var: path 'OrgName'
2025-06-30T09:38:16.9137724+05:30 [Information] Checking if 'OrgName' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:16.9138051+05:30 [Information] Resolving path 'OrgName' using global variables.
2025-06-30T09:38:16.9138378+05:30 [Information] Resolved JPath '$var:OrgName' to 'Org2'
2025-06-30T09:38:16.9138684+05:30 [Information] Resolved template pattern '{{$var:OrgName}}' to 'Org2' in '{
  "username": "{{$var:username}}",
  "email": "{{$var:email}}",
  "org": "{{$var:OrgName}}"
}'
2025-06-30T09:38:16.9139078+05:30 [Information] Resolved template '{
  "username": "{{$var:username}}",
  "email": "{{$var:email}}",
  "org": "{{$var:OrgName}}"
}' to '{
  "username": "John",
  "email": "<EMAIL>",
  "org": "Org2"
}'
2025-06-30T09:38:16.9140290+05:30 [Information] Set Value to Step 'f61dab35-f89b-41e7-9868-64ac10b8f6c9' input:
{
  "Method": "POST",
  "RequestUri": "https://httpbin.org/post",
  "Headers": {
    "Content-Type": "application/json"
  },
  "Content": {
    "username": "John",
    "email": "<EMAIL>",
    "org": "Org2"
  }
}
2025-06-30T09:38:17.4587676+05:30 [Information] Set Value to Step 'f61dab35-f89b-41e7-9868-64ac10b8f6c9' output:
{
  "statusCode": 200,
  "reasonPhrase": "OK",
  "headers": [
    {
      "Key": "Date",
      "Value": [
        "Mon, 30 Jun 2025 04:08:22 GMT"
      ]
    },
    {
      "Key": "Connection",
      "Value": [
        "keep-alive"
      ]
    },
    {
      "Key": "Server",
      "Value": [
        "gunicorn/19.9.0"
      ]
    },
    {
      "Key": "Access-Control-Allow-Origin",
      "Value": [
        "*"
      ]
    },
    {
      "Key": "Access-Control-Allow-Credentials",
      "Value": [
        "true"
      ]
    }
  ],
  "content": {
    "args": {},
    "data": "{\r\n  \"username\": \"John\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"org\": \"Org2\"\r\n}",
    "files": {},
    "form": {},
    "headers": {
      "Content-Length": "76",
      "Content-Type": "application/json; charset=utf-8",
      "Host": "httpbin.org",
      "X-Amzn-Trace-Id": "Root=1-68620db6-4f44d48409a4be574c6e7824"
    },
    "json": {
      "email": "<EMAIL>",
      "org": "Org2",
      "username": "John"
    },
    "origin": "**************",
    "url": "https://httpbin.org/post"
  }
}
2025-06-30T09:38:17.4600516+05:30 [Information] Resolving path 'output.content.json.url' using step input/output.
2025-06-30T09:38:17.4605641+05:30 [Information] Resolved JPath 'output.content.json.url' to ''
2025-06-30T09:38:17.4610936+05:30 [Information] Set Value to Step 'f61dab35-f89b-41e7-9868-64ac10b8f6c9' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:08:16.9128545Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:38:17.4614389+05:30 [Information] Run Asserter : {"Expression1":{"Value":"200","ErrorMesage":null},"Expression2":{"JPath":"output.statusCode","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:38:17.4616819+05:30 [Information] Resolving path 'output.statusCode' using step input/output.
2025-06-30T09:38:17.4619386+05:30 [Information] Resolved JPath 'output.statusCode' to '200'
2025-06-30T09:38:17.4621652+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$test:row.User.Username}}","ErrorMesage":null},"Expression2":{"JPath":"output.content.json.username","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:38:17.4623724+05:30 [Information] Resolving path 'row.User.Username' using test context.
2025-06-30T09:38:17.4625504+05:30 [Information] Resolved JPath '$test:row.User.Username' to 'John'
2025-06-30T09:38:17.4626977+05:30 [Information] Resolved template pattern '{{$test:row.User.Username}}' to 'John' in '{{$test:row.User.Username}}'
2025-06-30T09:38:17.4628499+05:30 [Information] Resolved template '{{$test:row.User.Username}}' to 'John'
2025-06-30T09:38:17.4631428+05:30 [Information] Resolving path 'output.content.json.username' using step input/output.
2025-06-30T09:38:17.4633164+05:30 [Information] Resolved JPath 'output.content.json.username' to 'John'
2025-06-30T09:38:17.4634888+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$test:row.User.Email}}","ErrorMesage":null},"Expression2":{"JPath":"output.content.json.email","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:38:17.4636413+05:30 [Information] Resolving path 'row.User.Email' using test context.
2025-06-30T09:38:17.4636867+05:30 [Information] Resolved JPath '$test:row.User.Email' to '<EMAIL>'
2025-06-30T09:38:17.4637223+05:30 [Information] Resolved template pattern '{{$test:row.User.Email}}' to '<EMAIL>' in '{{$test:row.User.Email}}'
2025-06-30T09:38:17.4637761+05:30 [Information] Resolved template '{{$test:row.User.Email}}' to '<EMAIL>'
2025-06-30T09:38:17.4638313+05:30 [Information] Resolving path 'output.content.json.email' using step input/output.
2025-06-30T09:38:17.4638731+05:30 [Information] Resolved JPath 'output.content.json.email' to '<EMAIL>'
2025-06-30T09:38:17.4639256+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$test:row.OrgName}}","ErrorMesage":null},"Expression2":{"JPath":"output.content.json.org","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:38:17.4639701+05:30 [Information] Resolving path 'row.OrgName' using test context.
2025-06-30T09:38:17.4640091+05:30 [Information] Resolved JPath '$test:row.OrgName' to 'Org2'
2025-06-30T09:38:17.4640451+05:30 [Information] Resolved template pattern '{{$test:row.OrgName}}' to 'Org2' in '{{$test:row.OrgName}}'
2025-06-30T09:38:17.4640814+05:30 [Information] Resolved template '{{$test:row.OrgName}}' to 'Org2'
2025-06-30T09:38:17.4641399+05:30 [Information] Resolving path 'output.content.json.org' using step input/output.
2025-06-30T09:38:17.4641813+05:30 [Information] Resolved JPath 'output.content.json.org' to 'Org2'
2025-06-30T09:38:17.4642317+05:30 [Information] Set Value to Step 'f61dab35-f89b-41e7-9868-64ac10b8f6c9' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 550,
  "startTime": "2025-06-30T04:08:16.9128545Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:38:17.4643221+05:30 [Information] Step 'Fetching user John details' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "881d007c-6e3f-47c6-8767-36a1ce8d3c58",
        "tables": {},
        "variables": {},
        "name": "Fetching user Tom details",
        "description": "Run step for each user",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "Tom",
            "email": "<EMAIL>",
            "org": "Org1"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:08:22 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"Tom\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"org\": \"Org1\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "74",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620db6-27b56e9149d4563c1f76e549"
            },
            "json": {
              "email": "<EMAIL>",
              "org": "Org1",
              "username": "Tom"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 776,
          "startTime": "2025-06-30T04:08:16.1281225Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      },
      {
        "id": "f61dab35-f89b-41e7-9868-64ac10b8f6c9",
        "tables": {},
        "variables": {},
        "name": "Fetching user John details",
        "description": "Run step for each user",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "John",
            "email": "<EMAIL>",
            "org": "Org2"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:08:22 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"John\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"org\": \"Org2\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "76",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620db6-4f44d48409a4be574c6e7824"
            },
            "json": {
              "email": "<EMAIL>",
              "org": "Org2",
              "username": "John"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 550,
          "startTime": "2025-06-30T04:08:16.9128545Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaseUrl": "https://httpbin.org",
      "User": {
        "Username": "John",
        "Email": "<EMAIL>"
      },
      "OrgName": "Org2",
      "username": "John",
      "email": "<EMAIL>"
    },
    "tables": {
      "UserTable": [
        {
          "User": {
            "Username": "Tom",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org1"
        },
        {
          "User": {
            "Username": "John",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org2"
        }
      ]
    },
    "context": {
      "repeatForTable": "UserTable",
      "row": {
        "User": {
          "Username": "John",
          "Email": "<EMAIL>"
        },
        "OrgName": "Org2"
      },
      "rowIndex": 1
    }
  }
}
2025-06-30T09:38:17.4643761+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:38:17.4644211+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:38:17.4644571+05:30 [Information] No performance data found for step 'Fetching user {{$var:username}} details' (http)
2025-06-30T09:38:17.4644927+05:30 [Information] [ OK       ]
2025-06-30T09:38:17.4646992+05:30 [Information] [----------] 1 step from Demo HTTP Table Test Iteration (1343 ms total)
