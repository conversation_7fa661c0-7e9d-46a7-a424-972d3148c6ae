2025-06-30T09:38:14.3208509+05:30 [Information] [----------] 1 step from Demo HTTP Table Step Iteration
2025-06-30T09:38:14.3217012+05:30 [Information] Set Value to Step '6d575721-b2a2-4730-a9d9-c4124ec42f3f' name:
"Run for Each User"
2025-06-30T09:38:14.3222074+05:30 [Information] Set Value to Step '6d575721-b2a2-4730-a9d9-c4124ec42f3f' description:
"Run step for each user"
2025-06-30T09:38:14.3225365+05:30 [Information] [ RUN      ] Demo HTTP Table Step Iteration > Run for Each User
2025-06-30T09:38:14.3243457+05:30 [Information] Executing step 'Run for Each User' for row 1/2 in table 'UserTable'.
2025-06-30T09:38:14.3250440+05:30 [Information] Default-mapped column 'User' to variable 'User' with value '{
  "Username": "Tom",
  "Email": "<EMAIL>"
}'
2025-06-30T09:38:14.3252908+05:30 [Information] Default-mapped column 'OrgName' to variable 'OrgName' with value 'Org1'
2025-06-30T09:38:14.3257043+05:30 [Information] Processing $var: path 'User.Username'
2025-06-30T09:38:14.3258274+05:30 [Information] Checking if 'User.Username' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=True, result=False
2025-06-30T09:38:14.3258978+05:30 [Information] Resolved path 'User.Username' in step-specific variables.
2025-06-30T09:38:14.3259760+05:30 [Information] Resolved JPath '$var:User.Username' to 'Tom'
2025-06-30T09:38:14.3260189+05:30 [Information] Resolved template pattern '{{$var:User.Username}}' to 'Tom' in '{{$var:User.Username}}'
2025-06-30T09:38:14.3260660+05:30 [Information] Resolved template '{{$var:User.Username}}' to 'Tom'
2025-06-30T09:38:14.3261704+05:30 [Information] Mapped column '{{$var:User.Username}}' to variable 'username' with value 'Tom'
2025-06-30T09:38:14.3262489+05:30 [Information] Processing $var: path 'User.Email'
2025-06-30T09:38:14.3262996+05:30 [Information] Checking if 'User.Email' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=True, result=False
2025-06-30T09:38:14.3263511+05:30 [Information] Resolved path 'User.Email' in step-specific variables.
2025-06-30T09:38:14.3263941+05:30 [Information] Resolved JPath '$var:User.Email' to '<EMAIL>'
2025-06-30T09:38:14.3264324+05:30 [Information] Resolved template pattern '{{$var:User.Email}}' to '<EMAIL>' in '{{$var:User.Email}}'
2025-06-30T09:38:14.3264749+05:30 [Information] Resolved template '{{$var:User.Email}}' to '<EMAIL>'
2025-06-30T09:38:14.3265513+05:30 [Information] Mapped column '{{$var:User.Email}}' to variable 'email' with value '<EMAIL>'
2025-06-30T09:38:14.3267124+05:30 [Information] Processing $var: path 'username'
2025-06-30T09:38:14.3267882+05:30 [Information] Checking if 'username' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:14.3268383+05:30 [Information] Resolved path 'username' in step-specific variables.
2025-06-30T09:38:14.3268802+05:30 [Information] Resolved JPath '$var:username' to 'Tom'
2025-06-30T09:38:14.3269162+05:30 [Information] Resolved template pattern '{{$var:username}}' to 'Tom' in 'Fetching user {{$var:username}} details'
2025-06-30T09:38:14.3269845+05:30 [Information] Resolved template 'Fetching user {{$var:username}} details' to 'Fetching user Tom details'
2025-06-30T09:38:14.3273808+05:30 [Information] Step Iteration 1/2: Fetching user Tom details
2025-06-30T09:38:14.3275477+05:30 [Information] Executing step 'Fetching user Tom details' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "6d575721-b2a2-4730-a9d9-c4124ec42f3f",
        "tables": {},
        "variables": {
          "User": {
            "Username": "Tom",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org1",
          "username": "Tom",
          "email": "<EMAIL>"
        },
        "name": "Run for Each User",
        "description": "Run step for each user",
        "context": {
          "repeatForTable": "UserTable",
          "row": {
            "User": {
              "Username": "Tom",
              "Email": "<EMAIL>"
            },
            "OrgName": "Org1"
          },
          "rowIndex": 0
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaseUrl": "https://httpbin.org"
    },
    "tables": {
      "UserTable": [
        {
          "User": {
            "Username": "Tom",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org1"
        },
        {
          "User": {
            "Username": "John",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org2"
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:38:14.3276232+05:30 [Information] Processing $var: path 'BaseUrl'
2025-06-30T09:38:14.3276791+05:30 [Information] Checking if 'BaseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:14.3277294+05:30 [Information] Resolving path 'BaseUrl' using global variables.
2025-06-30T09:38:14.3277793+05:30 [Information] Resolved JPath '$var:BaseUrl' to 'https://httpbin.org'
2025-06-30T09:38:14.3278275+05:30 [Information] Resolved template pattern '{{$var:BaseUrl}}' to 'https://httpbin.org' in '{{$var:BaseUrl}}/post'
2025-06-30T09:38:14.3278732+05:30 [Information] Resolved template '{{$var:BaseUrl}}/post' to 'https://httpbin.org/post'
2025-06-30T09:38:14.3279514+05:30 [Information] Processing $var: path 'username'
2025-06-30T09:38:14.3280340+05:30 [Information] Checking if 'username' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:14.3281434+05:30 [Information] Resolved path 'username' in step-specific variables.
2025-06-30T09:38:14.3282022+05:30 [Information] Resolved JPath '$var:username' to 'Tom'
2025-06-30T09:38:14.3282489+05:30 [Information] Resolved template pattern '{{$var:username}}' to 'Tom' in '{
  "username": "{{$var:username}}",
  "email": "{{$var:email}}",
  "org": "{{$var:OrgName}}"
}'
2025-06-30T09:38:14.3282964+05:30 [Information] Processing $var: path 'email'
2025-06-30T09:38:14.3283360+05:30 [Information] Checking if 'email' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:14.3283764+05:30 [Information] Resolved path 'email' in step-specific variables.
2025-06-30T09:38:14.3284415+05:30 [Information] Resolved JPath '$var:email' to '<EMAIL>'
2025-06-30T09:38:14.3284860+05:30 [Information] Resolved template pattern '{{$var:email}}' to '<EMAIL>' in '{
  "username": "{{$var:username}}",
  "email": "{{$var:email}}",
  "org": "{{$var:OrgName}}"
}'
2025-06-30T09:38:14.3285309+05:30 [Information] Processing $var: path 'OrgName'
2025-06-30T09:38:14.3285773+05:30 [Information] Checking if 'OrgName' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:14.3286699+05:30 [Information] Resolved path 'OrgName' in step-specific variables.
2025-06-30T09:38:14.3287579+05:30 [Information] Resolved JPath '$var:OrgName' to 'Org1'
2025-06-30T09:38:14.3288410+05:30 [Information] Resolved template pattern '{{$var:OrgName}}' to 'Org1' in '{
  "username": "{{$var:username}}",
  "email": "{{$var:email}}",
  "org": "{{$var:OrgName}}"
}'
2025-06-30T09:38:14.3291442+05:30 [Information] Resolved template '{
  "username": "{{$var:username}}",
  "email": "{{$var:email}}",
  "org": "{{$var:OrgName}}"
}' to '{
  "username": "Tom",
  "email": "<EMAIL>",
  "org": "Org1"
}'
2025-06-30T09:38:14.3294746+05:30 [Information] Set Value to Step '6d575721-b2a2-4730-a9d9-c4124ec42f3f' input:
{
  "Method": "POST",
  "RequestUri": "https://httpbin.org/post",
  "Headers": {
    "Content-Type": "application/json"
  },
  "Content": {
    "username": "Tom",
    "email": "<EMAIL>",
    "org": "Org1"
  }
}
2025-06-30T09:38:15.1000668+05:30 [Information] Set Value to Step '6d575721-b2a2-4730-a9d9-c4124ec42f3f' output:
{
  "statusCode": 200,
  "reasonPhrase": "OK",
  "headers": [
    {
      "Key": "Date",
      "Value": [
        "Mon, 30 Jun 2025 04:08:20 GMT"
      ]
    },
    {
      "Key": "Connection",
      "Value": [
        "keep-alive"
      ]
    },
    {
      "Key": "Server",
      "Value": [
        "gunicorn/19.9.0"
      ]
    },
    {
      "Key": "Access-Control-Allow-Origin",
      "Value": [
        "*"
      ]
    },
    {
      "Key": "Access-Control-Allow-Credentials",
      "Value": [
        "true"
      ]
    }
  ],
  "content": {
    "args": {},
    "data": "{\r\n  \"username\": \"Tom\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"org\": \"Org1\"\r\n}",
    "files": {},
    "form": {},
    "headers": {
      "Content-Length": "74",
      "Content-Type": "application/json; charset=utf-8",
      "Host": "httpbin.org",
      "X-Amzn-Trace-Id": "Root=1-68620db4-135deb815d28eea849c1acca"
    },
    "json": {
      "email": "<EMAIL>",
      "org": "Org1",
      "username": "Tom"
    },
    "origin": "**************",
    "url": "https://httpbin.org/post"
  }
}
2025-06-30T09:38:15.1007969+05:30 [Information] Resolving path 'output.content.json.url' using step input/output.
2025-06-30T09:38:15.1009298+05:30 [Information] Resolved JPath 'output.content.json.url' to ''
2025-06-30T09:38:15.1013375+05:30 [Information] Set Value to Step '6d575721-b2a2-4730-a9d9-c4124ec42f3f' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:08:14.3232502Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:38:15.1015312+05:30 [Information] Run Asserter : {"Expression1":{"Value":"200","ErrorMesage":null},"Expression2":{"JPath":"output.statusCode","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:38:15.1016022+05:30 [Information] Resolving path 'output.statusCode' using step input/output.
2025-06-30T09:38:15.1016575+05:30 [Information] Resolved JPath 'output.statusCode' to '200'
2025-06-30T09:38:15.1017163+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$step:row.User.Username}}","ErrorMesage":null},"Expression2":{"JPath":"output.content.json.username","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:38:15.1017774+05:30 [Information] Resolved path 'row.User.Username' in step-specific context.
2025-06-30T09:38:15.1018196+05:30 [Information] Resolved JPath '$step:row.User.Username' to 'Tom'
2025-06-30T09:38:15.1018556+05:30 [Information] Resolved template pattern '{{$step:row.User.Username}}' to 'Tom' in '{{$step:row.User.Username}}'
2025-06-30T09:38:15.1018991+05:30 [Information] Resolved template '{{$step:row.User.Username}}' to 'Tom'
2025-06-30T09:38:15.1019822+05:30 [Information] Resolving path 'output.content.json.username' using step input/output.
2025-06-30T09:38:15.1020310+05:30 [Information] Resolved JPath 'output.content.json.username' to 'Tom'
2025-06-30T09:38:15.1020856+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$step:row.User.Email}}","ErrorMesage":null},"Expression2":{"JPath":"output.content.json.email","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:38:15.1021358+05:30 [Information] Resolved path 'row.User.Email' in step-specific context.
2025-06-30T09:38:15.1021760+05:30 [Information] Resolved JPath '$step:row.User.Email' to '<EMAIL>'
2025-06-30T09:38:15.1022101+05:30 [Information] Resolved template pattern '{{$step:row.User.Email}}' to '<EMAIL>' in '{{$step:row.User.Email}}'
2025-06-30T09:38:15.1022506+05:30 [Information] Resolved template '{{$step:row.User.Email}}' to '<EMAIL>'
2025-06-30T09:38:15.1023173+05:30 [Information] Resolving path 'output.content.json.email' using step input/output.
2025-06-30T09:38:15.1023606+05:30 [Information] Resolved JPath 'output.content.json.email' to '<EMAIL>'
2025-06-30T09:38:15.1024108+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$step:row.OrgName}}","ErrorMesage":null},"Expression2":{"JPath":"output.content.json.org","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:38:15.1024748+05:30 [Information] Resolved path 'row.OrgName' in step-specific context.
2025-06-30T09:38:15.1025173+05:30 [Information] Resolved JPath '$step:row.OrgName' to 'Org1'
2025-06-30T09:38:15.1025535+05:30 [Information] Resolved template pattern '{{$step:row.OrgName}}' to 'Org1' in '{{$step:row.OrgName}}'
2025-06-30T09:38:15.1025926+05:30 [Information] Resolved template '{{$step:row.OrgName}}' to 'Org1'
2025-06-30T09:38:15.1026566+05:30 [Information] Resolving path 'output.content.json.org' using step input/output.
2025-06-30T09:38:15.1026992+05:30 [Information] Resolved JPath 'output.content.json.org' to 'Org1'
2025-06-30T09:38:15.1027612+05:30 [Information] Set Value to Step '6d575721-b2a2-4730-a9d9-c4124ec42f3f' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 773,
  "startTime": "2025-06-30T04:08:14.3232502Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:38:15.1028215+05:30 [Information] Step 'Fetching user Tom details' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "6d575721-b2a2-4730-a9d9-c4124ec42f3f",
        "tables": {},
        "variables": {
          "User": {
            "Username": "Tom",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org1",
          "username": "Tom",
          "email": "<EMAIL>"
        },
        "name": "Run for Each User",
        "description": "Run step for each user",
        "context": {
          "repeatForTable": "UserTable",
          "row": {
            "User": {
              "Username": "Tom",
              "Email": "<EMAIL>"
            },
            "OrgName": "Org1"
          },
          "rowIndex": 0
        },
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "Tom",
            "email": "<EMAIL>",
            "org": "Org1"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:08:20 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"Tom\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"org\": \"Org1\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "74",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620db4-135deb815d28eea849c1acca"
            },
            "json": {
              "email": "<EMAIL>",
              "org": "Org1",
              "username": "Tom"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 773,
          "startTime": "2025-06-30T04:08:14.3232502Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaseUrl": "https://httpbin.org"
    },
    "tables": {
      "UserTable": [
        {
          "User": {
            "Username": "Tom",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org1"
        },
        {
          "User": {
            "Username": "John",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org2"
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:38:15.1028746+05:30 [Information] Executing step 'Run for Each User' for row 2/2 in table 'UserTable'.
2025-06-30T09:38:15.1029609+05:30 [Information] Default-mapped column 'User' to variable 'User' with value '{
  "Username": "John",
  "Email": "<EMAIL>"
}'
2025-06-30T09:38:15.1030124+05:30 [Information] Default-mapped column 'OrgName' to variable 'OrgName' with value 'Org2'
2025-06-30T09:38:15.1030675+05:30 [Information] Processing $var: path 'User.Username'
2025-06-30T09:38:15.1031122+05:30 [Information] Checking if 'User.Username' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=True, result=False
2025-06-30T09:38:15.1031515+05:30 [Information] Resolved path 'User.Username' in step-specific variables.
2025-06-30T09:38:15.1031894+05:30 [Information] Resolved JPath '$var:User.Username' to 'John'
2025-06-30T09:38:15.1032247+05:30 [Information] Resolved template pattern '{{$var:User.Username}}' to 'John' in '{{$var:User.Username}}'
2025-06-30T09:38:15.1032629+05:30 [Information] Resolved template '{{$var:User.Username}}' to 'John'
2025-06-30T09:38:15.1033321+05:30 [Information] Mapped column '{{$var:User.Username}}' to variable 'username' with value 'John'
2025-06-30T09:38:15.1033783+05:30 [Information] Processing $var: path 'User.Email'
2025-06-30T09:38:15.1034168+05:30 [Information] Checking if 'User.Email' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=True, result=False
2025-06-30T09:38:15.1034555+05:30 [Information] Resolved path 'User.Email' in step-specific variables.
2025-06-30T09:38:15.1035017+05:30 [Information] Resolved JPath '$var:User.Email' to '<EMAIL>'
2025-06-30T09:38:15.1035394+05:30 [Information] Resolved template pattern '{{$var:User.Email}}' to '<EMAIL>' in '{{$var:User.Email}}'
2025-06-30T09:38:15.1035811+05:30 [Information] Resolved template '{{$var:User.Email}}' to '<EMAIL>'
2025-06-30T09:38:15.1036417+05:30 [Information] Mapped column '{{$var:User.Email}}' to variable 'email' with value '<EMAIL>'
2025-06-30T09:38:15.1036891+05:30 [Information] Processing $var: path 'username'
2025-06-30T09:38:15.1037301+05:30 [Information] Checking if 'username' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:15.1037686+05:30 [Information] Resolved path 'username' in step-specific variables.
2025-06-30T09:38:15.1038082+05:30 [Information] Resolved JPath '$var:username' to 'John'
2025-06-30T09:38:15.1038426+05:30 [Information] Resolved template pattern '{{$var:username}}' to 'John' in 'Fetching user {{$var:username}} details'
2025-06-30T09:38:15.1038808+05:30 [Information] Resolved template 'Fetching user {{$var:username}} details' to 'Fetching user John details'
2025-06-30T09:38:15.1040505+05:30 [Information] Step Iteration 2/2: Fetching user John details
2025-06-30T09:38:15.1041903+05:30 [Information] Executing step 'Fetching user John details' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "6d575721-b2a2-4730-a9d9-c4124ec42f3f",
        "tables": {},
        "variables": {
          "User": {
            "Username": "John",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org2",
          "username": "John",
          "email": "<EMAIL>"
        },
        "name": "Run for Each User",
        "description": "Run step for each user",
        "context": {
          "repeatForTable": "UserTable",
          "row": {
            "User": {
              "Username": "John",
              "Email": "<EMAIL>"
            },
            "OrgName": "Org2"
          },
          "rowIndex": 1
        },
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "Tom",
            "email": "<EMAIL>",
            "org": "Org1"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:08:20 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"Tom\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"org\": \"Org1\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "74",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620db4-135deb815d28eea849c1acca"
            },
            "json": {
              "email": "<EMAIL>",
              "org": "Org1",
              "username": "Tom"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 773,
          "startTime": "2025-06-30T04:08:14.3232502Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaseUrl": "https://httpbin.org"
    },
    "tables": {
      "UserTable": [
        {
          "User": {
            "Username": "Tom",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org1"
        },
        {
          "User": {
            "Username": "John",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org2"
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:38:15.1042524+05:30 [Information] Processing $var: path 'BaseUrl'
2025-06-30T09:38:15.1043029+05:30 [Information] Checking if 'BaseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:15.1043438+05:30 [Information] Resolving path 'BaseUrl' using global variables.
2025-06-30T09:38:15.1043841+05:30 [Information] Resolved JPath '$var:BaseUrl' to 'https://httpbin.org'
2025-06-30T09:38:15.1044206+05:30 [Information] Resolved template pattern '{{$var:BaseUrl}}' to 'https://httpbin.org' in '{{$var:BaseUrl}}/post'
2025-06-30T09:38:15.1044598+05:30 [Information] Resolved template '{{$var:BaseUrl}}/post' to 'https://httpbin.org/post'
2025-06-30T09:38:15.1045337+05:30 [Information] Processing $var: path 'username'
2025-06-30T09:38:15.1045844+05:30 [Information] Checking if 'username' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:15.1046499+05:30 [Information] Resolved path 'username' in step-specific variables.
2025-06-30T09:38:15.1046904+05:30 [Information] Resolved JPath '$var:username' to 'John'
2025-06-30T09:38:15.1047270+05:30 [Information] Resolved template pattern '{{$var:username}}' to 'John' in '{
  "username": "{{$var:username}}",
  "email": "{{$var:email}}",
  "org": "{{$var:OrgName}}"
}'
2025-06-30T09:38:15.1047676+05:30 [Information] Processing $var: path 'email'
2025-06-30T09:38:15.1048067+05:30 [Information] Checking if 'email' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:15.1048446+05:30 [Information] Resolved path 'email' in step-specific variables.
2025-06-30T09:38:15.1048811+05:30 [Information] Resolved JPath '$var:email' to '<EMAIL>'
2025-06-30T09:38:15.1049161+05:30 [Information] Resolved template pattern '{{$var:email}}' to '<EMAIL>' in '{
  "username": "{{$var:username}}",
  "email": "{{$var:email}}",
  "org": "{{$var:OrgName}}"
}'
2025-06-30T09:38:15.1049546+05:30 [Information] Processing $var: path 'OrgName'
2025-06-30T09:38:15.1049905+05:30 [Information] Checking if 'OrgName' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:15.1050293+05:30 [Information] Resolved path 'OrgName' in step-specific variables.
2025-06-30T09:38:15.1050669+05:30 [Information] Resolved JPath '$var:OrgName' to 'Org2'
2025-06-30T09:38:15.1051165+05:30 [Information] Resolved template pattern '{{$var:OrgName}}' to 'Org2' in '{
  "username": "{{$var:username}}",
  "email": "{{$var:email}}",
  "org": "{{$var:OrgName}}"
}'
2025-06-30T09:38:15.1051572+05:30 [Information] Resolved template '{
  "username": "{{$var:username}}",
  "email": "{{$var:email}}",
  "org": "{{$var:OrgName}}"
}' to '{
  "username": "John",
  "email": "<EMAIL>",
  "org": "Org2"
}'
2025-06-30T09:38:15.1052286+05:30 [Information] Set Value to Step '6d575721-b2a2-4730-a9d9-c4124ec42f3f' input:
{
  "Method": "POST",
  "RequestUri": "https://httpbin.org/post",
  "Headers": {
    "Content-Type": "application/json"
  },
  "Content": {
    "username": "John",
    "email": "<EMAIL>",
    "org": "Org2"
  }
}
2025-06-30T09:38:16.1143317+05:30 [Information] Set Value to Step '6d575721-b2a2-4730-a9d9-c4124ec42f3f' output:
{
  "statusCode": 200,
  "reasonPhrase": "OK",
  "headers": [
    {
      "Key": "Date",
      "Value": [
        "Mon, 30 Jun 2025 04:08:21 GMT"
      ]
    },
    {
      "Key": "Connection",
      "Value": [
        "keep-alive"
      ]
    },
    {
      "Key": "Server",
      "Value": [
        "gunicorn/19.9.0"
      ]
    },
    {
      "Key": "Access-Control-Allow-Origin",
      "Value": [
        "*"
      ]
    },
    {
      "Key": "Access-Control-Allow-Credentials",
      "Value": [
        "true"
      ]
    }
  ],
  "content": {
    "args": {},
    "data": "{\r\n  \"username\": \"John\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"org\": \"Org2\"\r\n}",
    "files": {},
    "form": {},
    "headers": {
      "Content-Length": "76",
      "Content-Type": "application/json; charset=utf-8",
      "Host": "httpbin.org",
      "X-Amzn-Trace-Id": "Root=1-68620db4-25d7814d37d739131603eca3"
    },
    "json": {
      "email": "<EMAIL>",
      "org": "Org2",
      "username": "John"
    },
    "origin": "**************",
    "url": "https://httpbin.org/post"
  }
}
2025-06-30T09:38:16.1151194+05:30 [Information] Resolving path 'output.content.json.url' using step input/output.
2025-06-30T09:38:16.1155036+05:30 [Information] Resolved JPath 'output.content.json.url' to ''
2025-06-30T09:38:16.1159695+05:30 [Information] Set Value to Step '6d575721-b2a2-4730-a9d9-c4124ec42f3f' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:08:14.3232502Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:38:16.1165562+05:30 [Information] Run Asserter : {"Expression1":{"Value":"200","ErrorMesage":null},"Expression2":{"JPath":"output.statusCode","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:38:16.1168174+05:30 [Information] Resolving path 'output.statusCode' using step input/output.
2025-06-30T09:38:16.1169597+05:30 [Information] Resolved JPath 'output.statusCode' to '200'
2025-06-30T09:38:16.1171038+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$step:row.User.Username}}","ErrorMesage":null},"Expression2":{"JPath":"output.content.json.username","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:38:16.1172443+05:30 [Information] Resolved path 'row.User.Username' in step-specific context.
2025-06-30T09:38:16.1173418+05:30 [Information] Resolved JPath '$step:row.User.Username' to 'John'
2025-06-30T09:38:16.1174306+05:30 [Information] Resolved template pattern '{{$step:row.User.Username}}' to 'John' in '{{$step:row.User.Username}}'
2025-06-30T09:38:16.1175264+05:30 [Information] Resolved template '{{$step:row.User.Username}}' to 'John'
2025-06-30T09:38:16.1176969+05:30 [Information] Resolving path 'output.content.json.username' using step input/output.
2025-06-30T09:38:16.1177955+05:30 [Information] Resolved JPath 'output.content.json.username' to 'John'
2025-06-30T09:38:16.1178970+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$step:row.User.Email}}","ErrorMesage":null},"Expression2":{"JPath":"output.content.json.email","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:38:16.1180019+05:30 [Information] Resolved path 'row.User.Email' in step-specific context.
2025-06-30T09:38:16.1180977+05:30 [Information] Resolved JPath '$step:row.User.Email' to '<EMAIL>'
2025-06-30T09:38:16.1181863+05:30 [Information] Resolved template pattern '{{$step:row.User.Email}}' to '<EMAIL>' in '{{$step:row.User.Email}}'
2025-06-30T09:38:16.1182812+05:30 [Information] Resolved template '{{$step:row.User.Email}}' to '<EMAIL>'
2025-06-30T09:38:16.1184144+05:30 [Information] Resolving path 'output.content.json.email' using step input/output.
2025-06-30T09:38:16.1185040+05:30 [Information] Resolved JPath 'output.content.json.email' to '<EMAIL>'
2025-06-30T09:38:16.1185965+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$step:row.OrgName}}","ErrorMesage":null},"Expression2":{"JPath":"output.content.json.org","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:38:16.1186808+05:30 [Information] Resolved path 'row.OrgName' in step-specific context.
2025-06-30T09:38:16.1187556+05:30 [Information] Resolved JPath '$step:row.OrgName' to 'Org2'
2025-06-30T09:38:16.1188180+05:30 [Information] Resolved template pattern '{{$step:row.OrgName}}' to 'Org2' in '{{$step:row.OrgName}}'
2025-06-30T09:38:16.1188848+05:30 [Information] Resolved template '{{$step:row.OrgName}}' to 'Org2'
2025-06-30T09:38:16.1189839+05:30 [Information] Resolving path 'output.content.json.org' using step input/output.
2025-06-30T09:38:16.1190632+05:30 [Information] Resolved JPath 'output.content.json.org' to 'Org2'
2025-06-30T09:38:16.1191550+05:30 [Information] Set Value to Step '6d575721-b2a2-4730-a9d9-c4124ec42f3f' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 1013,
  "startTime": "2025-06-30T04:08:14.3232502Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:38:16.1192632+05:30 [Information] Step 'Fetching user John details' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "6d575721-b2a2-4730-a9d9-c4124ec42f3f",
        "tables": {},
        "variables": {
          "User": {
            "Username": "John",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org2",
          "username": "John",
          "email": "<EMAIL>"
        },
        "name": "Run for Each User",
        "description": "Run step for each user",
        "context": {
          "repeatForTable": "UserTable",
          "row": {
            "User": {
              "Username": "John",
              "Email": "<EMAIL>"
            },
            "OrgName": "Org2"
          },
          "rowIndex": 1
        },
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "username": "John",
            "email": "<EMAIL>",
            "org": "Org2"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:08:21 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"username\": \"John\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"org\": \"Org2\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "76",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620db4-25d7814d37d739131603eca3"
            },
            "json": {
              "email": "<EMAIL>",
              "org": "Org2",
              "username": "John"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 1013,
          "startTime": "2025-06-30T04:08:14.3232502Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaseUrl": "https://httpbin.org"
    },
    "tables": {
      "UserTable": [
        {
          "User": {
            "Username": "Tom",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org1"
        },
        {
          "User": {
            "Username": "John",
            "Email": "<EMAIL>"
          },
          "OrgName": "Org2"
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:38:16.1193563+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:38:16.1194323+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:38:16.1194955+05:30 [Information] No performance data found for step 'Run for Each User' (http)
2025-06-30T09:38:16.1195586+05:30 [Information] [ OK       ]
2025-06-30T09:38:16.1199707+05:30 [Information] [----------] 1 step from Demo HTTP Table Step Iteration (1798 ms total)
