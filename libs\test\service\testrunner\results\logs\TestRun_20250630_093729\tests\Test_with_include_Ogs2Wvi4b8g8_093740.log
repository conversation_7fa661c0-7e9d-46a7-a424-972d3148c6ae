2025-06-30T09:37:40.1439744+05:30 [Information] [----------] 2 steps from Test with include
2025-06-30T09:37:40.1444727+05:30 [Information] Set Value to Step 'a6733c09-cc0d-4515-8b72-f1164ec316a8' name:
"GET Token"
2025-06-30T09:37:40.1445712+05:30 [Information] Set Value to Step 'a6733c09-cc0d-4515-8b72-f1164ec316a8' description:
"Common - Step to get token"
2025-06-30T09:37:40.1446241+05:30 [Information] [ RUN      ] Test with include > GET Token
2025-06-30T09:37:40.1450532+05:30 [Information] Executing single instance of step 'GET Token'.
2025-06-30T09:37:40.1452002+05:30 [Information] Executing step 'GET Token' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "a6733c09-cc0d-4515-8b72-f1164ec316a8",
        "tables": {},
        "variables": {},
        "name": "GET Token",
        "description": "Common - Step to get token"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:37:40.1456563+05:30 [Information] Processing $var: path 'baseUrl'
2025-06-30T09:37:40.1457562+05:30 [Information] Checking if 'baseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:40.1458225+05:30 [Information] Resolving path 'baseUrl' using global variables.
2025-06-30T09:37:40.1458641+05:30 [Information] Resolved JPath '$var:baseUrl' to 'https://httpbin.org'
2025-06-30T09:37:40.1459072+05:30 [Information] Resolved template pattern '{{$var:baseUrl}}' to 'https://httpbin.org' in '{{$var:baseUrl}}/post'
2025-06-30T09:37:40.1459632+05:30 [Information] Resolved template '{{$var:baseUrl}}/post' to 'https://httpbin.org/post'
2025-06-30T09:37:40.1460789+05:30 [Information] Set Value to Step 'a6733c09-cc0d-4515-8b72-f1164ec316a8' input:
{
  "Method": "POST",
  "RequestUri": "https://httpbin.org/post",
  "Headers": {
    "Content-Type": "application/json"
  },
  "Content": {
    "key": "key_value"
  }
}
2025-06-30T09:37:40.9499320+05:30 [Information] Set Value to Step 'a6733c09-cc0d-4515-8b72-f1164ec316a8' output:
{
  "statusCode": 200,
  "reasonPhrase": "OK",
  "headers": [
    {
      "Key": "Date",
      "Value": [
        "Mon, 30 Jun 2025 04:07:46 GMT"
      ]
    },
    {
      "Key": "Connection",
      "Value": [
        "keep-alive"
      ]
    },
    {
      "Key": "Server",
      "Value": [
        "gunicorn/19.9.0"
      ]
    },
    {
      "Key": "Access-Control-Allow-Origin",
      "Value": [
        "*"
      ]
    },
    {
      "Key": "Access-Control-Allow-Credentials",
      "Value": [
        "true"
      ]
    }
  ],
  "content": {
    "args": {},
    "data": "{\r\n  \"key\": \"key_value\"\r\n}",
    "files": {},
    "form": {},
    "headers": {
      "Content-Length": "26",
      "Content-Type": "application/json; charset=utf-8",
      "Host": "httpbin.org",
      "X-Amzn-Trace-Id": "Root=1-68620d92-03bc31cd3bdba7ee589a468f"
    },
    "json": {
      "key": "key_value"
    },
    "origin": "**************",
    "url": "https://httpbin.org/post"
  }
}
2025-06-30T09:37:40.9502951+05:30 [Information] Resolving path 'output.content.json.key' using step input/output.
2025-06-30T09:37:40.9504409+05:30 [Information] Resolved JPath 'output.content.json.key' to 'key_value'
2025-06-30T09:37:40.9510121+05:30 [Information] Set Value to Step 'a6733c09-cc0d-4515-8b72-f1164ec316a8' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:07:40.145051Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:37:40.9512309+05:30 [Information] Run Asserter : {"Expression1":{"Value":"200","ErrorMesage":null},"Expression2":{"JPath":"output.statusCode","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:37:40.9513355+05:30 [Information] Resolving path 'output.statusCode' using step input/output.
2025-06-30T09:37:40.9514174+05:30 [Information] Resolved JPath 'output.statusCode' to '200'
2025-06-30T09:37:40.9515243+05:30 [Information] Set Value to Step 'a6733c09-cc0d-4515-8b72-f1164ec316a8' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 805,
  "startTime": "2025-06-30T04:07:40.145051Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:37:40.9516290+05:30 [Information] Step 'GET Token' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "a6733c09-cc0d-4515-8b72-f1164ec316a8",
        "tables": {},
        "variables": {},
        "name": "GET Token",
        "description": "Common - Step to get token",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "key": "key_value"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:07:46 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"key\": \"key_value\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "26",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620d92-03bc31cd3bdba7ee589a468f"
            },
            "json": {
              "key": "key_value"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 805,
          "startTime": "2025-06-30T04:07:40.145051Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "token": "key_value"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:37:40.9517132+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:37:40.9517793+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:37:40.9518462+05:30 [Information] No performance data found for step 'GET Token' (http)
2025-06-30T09:37:40.9519040+05:30 [Information] [ OK       ]
2025-06-30T09:37:40.9525478+05:30 [Information] Set Value to Step '3c079d80-9fe6-4e2e-bd32-27a7faa47094' name:
"Step to test POST request with token"
2025-06-30T09:37:40.9526931+05:30 [Information] Set Value to Step '3c079d80-9fe6-4e2e-bd32-27a7faa47094' description:
"POST request with data from included test"
2025-06-30T09:37:40.9527745+05:30 [Information] [ RUN      ] Test with include > Step to test POST request with token
2025-06-30T09:37:40.9531343+05:30 [Information] Executing single instance of step 'Step to test POST request with token'.
2025-06-30T09:37:40.9532934+05:30 [Information] Executing step 'Step to test POST request with token' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "a6733c09-cc0d-4515-8b72-f1164ec316a8",
        "tables": {},
        "variables": {},
        "name": "GET Token",
        "description": "Common - Step to get token",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "key": "key_value"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:07:46 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"key\": \"key_value\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "26",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620d92-03bc31cd3bdba7ee589a468f"
            },
            "json": {
              "key": "key_value"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 805,
          "startTime": "2025-06-30T04:07:40.145051Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      },
      {
        "id": "3c079d80-9fe6-4e2e-bd32-27a7faa47094",
        "tables": {},
        "variables": {},
        "name": "Step to test POST request with token",
        "description": "POST request with data from included test"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "token": "key_value"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:37:40.9533754+05:30 [Information] Processing $var: path 'baseUrl'
2025-06-30T09:37:40.9535112+05:30 [Information] Checking if 'baseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:40.9536858+05:30 [Information] Resolving path 'baseUrl' using global variables.
2025-06-30T09:37:40.9538531+05:30 [Information] Resolved JPath '$var:baseUrl' to 'https://httpbin.org'
2025-06-30T09:37:40.9540233+05:30 [Information] Resolved template pattern '{{$var:baseUrl}}' to 'https://httpbin.org' in '{{$var:baseUrl}}/post'
2025-06-30T09:37:40.9541491+05:30 [Information] Resolved template '{{$var:baseUrl}}/post' to 'https://httpbin.org/post'
2025-06-30T09:37:40.9542114+05:30 [Information] Processing $var: path 'token'
2025-06-30T09:37:40.9543292+05:30 [Information] Checking if 'token' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:40.9544818+05:30 [Information] Resolving path 'token' using global variables.
2025-06-30T09:37:40.9546332+05:30 [Information] Resolved JPath '$var:token' to 'key_value'
2025-06-30T09:37:40.9547343+05:30 [Information] Resolved template pattern '{{$var:token}}' to 'key_value' in 'Bearer {{$var:token}}'
2025-06-30T09:37:40.9547887+05:30 [Information] Resolved template 'Bearer {{$var:token}}' to 'Bearer key_value'
2025-06-30T09:37:40.9549150+05:30 [Information] Processing $var: path 'baseUrl'
2025-06-30T09:37:40.9549978+05:30 [Information] Checking if 'baseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:40.9550524+05:30 [Information] Resolving path 'baseUrl' using global variables.
2025-06-30T09:37:40.9550933+05:30 [Information] Resolved JPath '$var:baseUrl' to 'https://httpbin.org'
2025-06-30T09:37:40.9551303+05:30 [Information] Resolved template pattern '{{$var:baseUrl}}' to 'https://httpbin.org' in '{
  "keyFromVar": "{{$var:baseUrl}}",
  "keyFromJPath": "{{$var:token}}"
}'
2025-06-30T09:37:40.9551690+05:30 [Information] Processing $var: path 'token'
2025-06-30T09:37:40.9552051+05:30 [Information] Checking if 'token' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:40.9552414+05:30 [Information] Resolving path 'token' using global variables.
2025-06-30T09:37:40.9552772+05:30 [Information] Resolved JPath '$var:token' to 'key_value'
2025-06-30T09:37:40.9553127+05:30 [Information] Resolved template pattern '{{$var:token}}' to 'key_value' in '{
  "keyFromVar": "{{$var:baseUrl}}",
  "keyFromJPath": "{{$var:token}}"
}'
2025-06-30T09:37:40.9553492+05:30 [Information] Resolved template '{
  "keyFromVar": "{{$var:baseUrl}}",
  "keyFromJPath": "{{$var:token}}"
}' to '{
  "keyFromVar": "https://httpbin.org",
  "keyFromJPath": "key_value"
}'
2025-06-30T09:37:40.9554179+05:30 [Information] Set Value to Step '3c079d80-9fe6-4e2e-bd32-27a7faa47094' input:
{
  "Method": "POST",
  "RequestUri": "https://httpbin.org/post",
  "Headers": {
    "Content-Type": "application/json",
    "accept": "application/json",
    "Authorization": "Bearer key_value"
  },
  "Content": {
    "keyFromVar": "https://httpbin.org",
    "keyFromJPath": "key_value"
  }
}
2025-06-30T09:37:41.9144662+05:30 [Information] Set Value to Step '3c079d80-9fe6-4e2e-bd32-27a7faa47094' output:
{
  "statusCode": 200,
  "reasonPhrase": "OK",
  "headers": [
    {
      "Key": "Date",
      "Value": [
        "Mon, 30 Jun 2025 04:07:47 GMT"
      ]
    },
    {
      "Key": "Connection",
      "Value": [
        "keep-alive"
      ]
    },
    {
      "Key": "Server",
      "Value": [
        "gunicorn/19.9.0"
      ]
    },
    {
      "Key": "Access-Control-Allow-Origin",
      "Value": [
        "*"
      ]
    },
    {
      "Key": "Access-Control-Allow-Credentials",
      "Value": [
        "true"
      ]
    }
  ],
  "content": {
    "args": {},
    "data": "{\r\n  \"keyFromVar\": \"https://httpbin.org\",\r\n  \"keyFromJPath\": \"key_value\"\r\n}",
    "files": {},
    "form": {},
    "headers": {
      "Accept": "application/json",
      "Authorization": "Bearer key_value",
      "Content-Length": "75",
      "Content-Type": "application/json; charset=utf-8",
      "Host": "httpbin.org",
      "X-Amzn-Trace-Id": "Root=1-68620d93-425406000abb68fe5f23d672"
    },
    "json": {
      "keyFromJPath": "key_value",
      "keyFromVar": "https://httpbin.org"
    },
    "origin": "**************",
    "url": "https://httpbin.org/post"
  }
}
2025-06-30T09:37:41.9149668+05:30 [Information] Set Value to Step '3c079d80-9fe6-4e2e-bd32-27a7faa47094' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:07:40.9531316Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:37:41.9151968+05:30 [Information] Run Asserter : {"Expression1":{"Value":"200","ErrorMesage":null},"Expression2":{"JPath":"output.statusCode","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:37:41.9153291+05:30 [Information] Resolving path 'output.statusCode' using step input/output.
2025-06-30T09:37:41.9154504+05:30 [Information] Resolved JPath 'output.statusCode' to '200'
2025-06-30T09:37:41.9155850+05:30 [Information] Set Value to Step '3c079d80-9fe6-4e2e-bd32-27a7faa47094' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 960,
  "startTime": "2025-06-30T04:07:40.9531316Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:37:41.9157399+05:30 [Information] Step 'Step to test POST request with token' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "a6733c09-cc0d-4515-8b72-f1164ec316a8",
        "tables": {},
        "variables": {},
        "name": "GET Token",
        "description": "Common - Step to get token",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "key": "key_value"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:07:46 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"key\": \"key_value\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "26",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620d92-03bc31cd3bdba7ee589a468f"
            },
            "json": {
              "key": "key_value"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 805,
          "startTime": "2025-06-30T04:07:40.145051Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      },
      {
        "id": "3c079d80-9fe6-4e2e-bd32-27a7faa47094",
        "tables": {},
        "variables": {},
        "name": "Step to test POST request with token",
        "description": "POST request with data from included test",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json",
            "accept": "application/json",
            "Authorization": "Bearer key_value"
          },
          "Content": {
            "keyFromVar": "https://httpbin.org",
            "keyFromJPath": "key_value"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:07:47 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"keyFromVar\": \"https://httpbin.org\",\r\n  \"keyFromJPath\": \"key_value\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Accept": "application/json",
              "Authorization": "Bearer key_value",
              "Content-Length": "75",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620d93-425406000abb68fe5f23d672"
            },
            "json": {
              "keyFromJPath": "key_value",
              "keyFromVar": "https://httpbin.org"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 960,
          "startTime": "2025-06-30T04:07:40.9531316Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "token": "key_value"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:37:41.9159138+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:37:41.9160223+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:37:41.9161574+05:30 [Information] No performance data found for step 'Step to test POST request with token' (http)
2025-06-30T09:37:41.9162743+05:30 [Information] [ OK       ]
2025-06-30T09:37:41.9173046+05:30 [Information] [----------] 2 steps from Test with include (1772 ms total)
