﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{3A01BC59-6A19-3D6A-8774-B1EE198C8334}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.22621.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>bbclient</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\build\testlib\cocshmercur\mercuryswigcs\mercury\cocppbbclie\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">bbclient.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">bbclient</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\build\testlib\cocshmercur\mercuryswigcs\mercury\cocppbbclie\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">bbclient.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">bbclient</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.lib</TargetExt>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\kibisoft\workarea\git\interop\libs\comm\cpp\bbclient\..\..\..;D:\kibisoft\workarea\git\interop\libs\comm\cpp\bbclient\include;D:\kibisoft\workarea\git\interop\libs\comm\cpp\bbclient\..;D:\kibisoft\workarea\git\interop\libs\3rdparty\openssl-3.0.15\win\x64\release\include;D:\kibisoft\workarea\git\interop\libs\xplatform\include;D:\kibisoft\workarea\git\interop\libs\3rdparty\tiny-process-library\include;D:\kibisoft\workarea\git\interop\libs\comm\cpp\boarder\..;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4127;4458</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <UndefinePreprocessorDefinitions>GetCurrentTime</UndefinePreprocessorDefinitions>
      <UseFullPaths>false</UseFullPaths>
      <UseStandardPreprocessor>true</UseStandardPreprocessor>
      <WarningLevel>TurnOffAllWarnings</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;GIT_COMMIT_COUNT=3186;_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR;NOMINMAX;_CRT_SECURE_NO_WARNINGS;bongobongo;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;GIT_COMMIT_COUNT=3186;_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR;NOMINMAX;_CRT_SECURE_NO_WARNINGS;bongobongo;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\kibisoft\workarea\git\interop\libs\comm\cpp\bbclient\..\..\..;D:\kibisoft\workarea\git\interop\libs\comm\cpp\bbclient\include;D:\kibisoft\workarea\git\interop\libs\comm\cpp\bbclient\..;D:\kibisoft\workarea\git\interop\libs\3rdparty\openssl-3.0.15\win\x64\release\include;D:\kibisoft\workarea\git\interop\libs\xplatform\include;D:\kibisoft\workarea\git\interop\libs\3rdparty\tiny-process-library\include;D:\kibisoft\workarea\git\interop\libs\comm\cpp\boarder\..;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\kibisoft\workarea\git\interop\libs\comm\cpp\bbclient\..\..\..;D:\kibisoft\workarea\git\interop\libs\comm\cpp\bbclient\include;D:\kibisoft\workarea\git\interop\libs\comm\cpp\bbclient\..;D:\kibisoft\workarea\git\interop\libs\3rdparty\openssl-3.0.15\win\x64\release\include;D:\kibisoft\workarea\git\interop\libs\xplatform\include;D:\kibisoft\workarea\git\interop\libs\3rdparty\tiny-process-library\include;D:\kibisoft\workarea\git\interop\libs\comm\cpp\boarder\..;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\kibisoft\workarea\git\interop\libs\comm\cpp\bbclient\..\..\..;D:\kibisoft\workarea\git\interop\libs\comm\cpp\bbclient\include;D:\kibisoft\workarea\git\interop\libs\comm\cpp\bbclient\..;D:\kibisoft\workarea\git\interop\libs\3rdparty\openssl-3.0.15\win\x64\release\include;D:\kibisoft\workarea\git\interop\libs\xplatform\include;D:\kibisoft\workarea\git\interop\libs\3rdparty\tiny-process-library\include;D:\kibisoft\workarea\git\interop\libs\comm\cpp\boarder\..;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DisableSpecificWarnings>4127;4458</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <UndefinePreprocessorDefinitions>GetCurrentTime</UndefinePreprocessorDefinitions>
      <UseFullPaths>false</UseFullPaths>
      <UseStandardPreprocessor>true</UseStandardPreprocessor>
      <WarningLevel>TurnOffAllWarnings</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;GIT_COMMIT_COUNT=3186;_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR;NOMINMAX;_CRT_SECURE_NO_WARNINGS;bongobongo;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;GIT_COMMIT_COUNT=3186;_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR;NOMINMAX;_CRT_SECURE_NO_WARNINGS;bongobongo;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\kibisoft\workarea\git\interop\libs\comm\cpp\bbclient\..\..\..;D:\kibisoft\workarea\git\interop\libs\comm\cpp\bbclient\include;D:\kibisoft\workarea\git\interop\libs\comm\cpp\bbclient\..;D:\kibisoft\workarea\git\interop\libs\3rdparty\openssl-3.0.15\win\x64\release\include;D:\kibisoft\workarea\git\interop\libs\xplatform\include;D:\kibisoft\workarea\git\interop\libs\3rdparty\tiny-process-library\include;D:\kibisoft\workarea\git\interop\libs\comm\cpp\boarder\..;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\kibisoft\workarea\git\interop\libs\comm\cpp\bbclient\..\..\..;D:\kibisoft\workarea\git\interop\libs\comm\cpp\bbclient\include;D:\kibisoft\workarea\git\interop\libs\comm\cpp\bbclient\..;D:\kibisoft\workarea\git\interop\libs\3rdparty\openssl-3.0.15\win\x64\release\include;D:\kibisoft\workarea\git\interop\libs\xplatform\include;D:\kibisoft\workarea\git\interop\libs\3rdparty\tiny-process-library\include;D:\kibisoft\workarea\git\interop\libs\comm\cpp\boarder\..;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\kibisoft\workarea\git\interop\libs\comm\cpp\bbclient\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule D:/kibisoft/workarea/git/interop/libs/comm/cpp/bbclient/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/kibisoft/workarea/git/interop/libs/test/service/testlib/tests/unit -BD:/kibisoft/workarea/git/interop/libs/test/service/testlib/tests/unit/build --check-stamp-file D:/kibisoft/workarea/git/interop/libs/test/service/testlib/tests/unit/build/testlib/cocshmercur/mercuryswigcs/mercury/cocppbbclie/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\kibisoft\workarea\git\interop\cmake\Globals.cmake;D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\build\CMakeFiles\cmake.verify_globs;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\build\testlib\cocshmercur\mercuryswigcs\mercury\cocppbbclie\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule D:/kibisoft/workarea/git/interop/libs/comm/cpp/bbclient/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/kibisoft/workarea/git/interop/libs/test/service/testlib/tests/unit -BD:/kibisoft/workarea/git/interop/libs/test/service/testlib/tests/unit/build --check-stamp-file D:/kibisoft/workarea/git/interop/libs/test/service/testlib/tests/unit/build/testlib/cocshmercur/mercuryswigcs/mercury/cocppbbclie/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\kibisoft\workarea\git\interop\cmake\Globals.cmake;D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\build\CMakeFiles\cmake.verify_globs;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\build\testlib\cocshmercur\mercuryswigcs\mercury\cocppbbclie\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\kibisoft\workarea\git\interop\libs\comm\cpp\bbclient\include\bbclient.h" />
    <ClInclude Include="D:\kibisoft\workarea\git\interop\libs\comm\cpp\bbclient\include\bbdriver.h" />
    <ClInclude Include="D:\kibisoft\workarea\git\interop\libs\comm\cpp\bbclient\include\bbfollower.h" />
    <ClInclude Include="D:\kibisoft\workarea\git\interop\libs\comm\cpp\bbclient\include\bbtypes.h" />
    <ClCompile Include="D:\kibisoft\workarea\git\interop\libs\comm\cpp\bbclient\src\bbclient.cpp" />
    <ClInclude Include="D:\kibisoft\workarea\git\interop\libs\comm\cpp\bbclient\src\bbclientimpl.h" />
    <ClCompile Include="D:\kibisoft\workarea\git\interop\libs\comm\cpp\bbclient\src\bbclientimpl.cpp" />
    <ClInclude Include="D:\kibisoft\workarea\git\interop\libs\comm\cpp\bbclient\src\bbconnector.h" />
    <ClCompile Include="D:\kibisoft\workarea\git\interop\libs\comm\cpp\bbclient\src\bbconnector.cpp" />
    <ClCompile Include="D:\kibisoft\workarea\git\interop\libs\comm\cpp\bbclient\src\bbdriver.cpp" />
    <ClInclude Include="D:\kibisoft\workarea\git\interop\libs\comm\cpp\bbclient\src\bbdriverimpl.h" />
    <ClCompile Include="D:\kibisoft\workarea\git\interop\libs\comm\cpp\bbclient\src\bbdriverimpl.cpp" />
    <ClCompile Include="D:\kibisoft\workarea\git\interop\libs\comm\cpp\bbclient\src\bbfollower.cpp" />
    <ClInclude Include="D:\kibisoft\workarea\git\interop\libs\comm\cpp\bbclient\src\bbfollowerimpl.h" />
    <ClCompile Include="D:\kibisoft\workarea\git\interop\libs\comm\cpp\bbclient\src\bbfollowerimpl.cpp" />
    <ClInclude Include="D:\kibisoft\workarea\git\interop\libs\comm\cpp\bbclient\src\bblogger.h" />
    <ClCompile Include="D:\kibisoft\workarea\git\interop\libs\comm\cpp\bbclient\src\bblogger.cpp" />
    <ClCompile Include="D:\kibisoft\workarea\git\interop\libs\comm\cpp\bbclient\src\bbtypes.cpp" />
    <ClInclude Include="D:\kibisoft\workarea\git\interop\libs\comm\cpp\bbclient\src\dodo.h" />
    <ClCompile Include="D:\kibisoft\workarea\git\interop\libs\comm\cpp\bbclient\src\dodo.c" />
    <ClInclude Include="D:\kibisoft\workarea\git\interop\libs\comm\cpp\bbclient\src\profile.h" />
    <ClCompile Include="D:\kibisoft\workarea\git\interop\libs\comm\cpp\bbclient\src\bbsocketws.cpp" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\build\ZERO_CHECK.vcxproj">
      <Project>{61469607-E4E8-314E-BE85-14884F74CA7F}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\build\testlib\cocshmercur\mercuryswigcs\mercury\cocppbbclie\cocppboarde\boarder.vcxproj">
      <Project>{7F1CCF46-8232-3DF0-8DB8-1AA7D869E66E}</Project>
      <Name>boarder</Name>
    </ProjectReference>
    <ProjectReference Include="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\build\testlib\cocshmercur\mercuryswigcs\mercury\cocppbbclie\libsxplatfo\li3rdtinypr\tiny_proc_lib.vcxproj">
      <Project>{0C975849-2C64-3705-A7CB-5CD13CB273B2}</Project>
      <Name>tiny_proc_lib</Name>
    </ProjectReference>
    <ProjectReference Include="D:\kibisoft\workarea\git\interop\libs\test\service\testlib\tests\unit\build\testlib\cocshmercur\mercuryswigcs\mercury\cocppbbclie\libsxplatfo\xplatform.vcxproj">
      <Project>{109650CC-FCF9-30DD-9050-56C1B7224A03}</Project>
      <Name>xplatform</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>