/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

#pragma once

#include "RandomUtils.h"
#include "ProtoUtils.h"
#include "KeratometryDataMaker.h"
#include "PatientDataMaker.h"
#include "RefractionDataMaker.h"
#include "RefractiveTreatmentDataMaker.h"
#include "CataractTreatmentDataMaker.h"
#include "../generated/SurgeryReportDataMaker.pb.h"

namespace testdatamaker {

namespace type = alcon::interop::type;
namespace srdmp = surgeryreportdatamaker;
using ctu = alcon::interop::type::CoreTypesUtil;
using pddm = testdatamaker::PatientDataMaker;
using rdm = testdatamaker::RefractionDataMaker;
using rtdm = testdatamaker::RefractiveTreatmentDataMaker;
using ctdm = testdatamaker::CataractTreatmentDataMaker;

class SurgeryReportDataMaker {
public:
    using surgeryreportdatamaker_config_t = dmb::datamaker_config_t<type::SurgeryReport, srdmp::SurgeryReportParams>;
    using surgeryreportdatamaker_config_Builder = dmb::datamaker_config_Builder<type::SurgeryReport, srdmp::SurgeryReportParams>;

    static std::optional<type::SurgeryReport> make(const srdmp::SurgeryReportConfig& config);
    static std::optional<type::SurgeryReport> make(const surgeryreportdatamaker_config_t& config);
    static std::vector<type::SurgeryReport> extractSurgeryReportFromDb(const mpdb::InMultiProtobufDb& data);

    // Default values

    static srdmp::OtherTreatmentArgs defaultOtherTreatment;
    static srdmp::SurgeryReportParams defaultSurgeryReportParams;

private:

    static type::SurgeryReport makeRandom(const srdmp::SurgeryReportParams& params);
    static type::SurgeryReport makeBasedOn(const std::optional<type::SurgeryReport>& seed, const srdmp::SurgeryReportParams& params);
    static type::OtherTreatment makeOtherTreatment(const srdmp::OtherTreatmentArgs& args);
};

} // testdatamaker
