2025-06-30T09:17:07.2381458+05:30 [Information] [----------] 1 step from Startup
2025-06-30T09:17:07.2405975+05:30 [Information] Set Value to Step '2f169908-113b-4692-9265-37cfc7df8ad4' name:
"Initialize Database"
2025-06-30T09:17:07.2409483+05:30 [Information] Set Value to Step '2f169908-113b-4692-9265-37cfc7df8ad4' description:
"Setup initial database state"
2025-06-30T09:17:07.2412194+05:30 [Information] [ RUN      ] Startup > Initialize Database
2025-06-30T09:17:07.2428604+05:30 [Information] Executing single instance of step 'Initialize Database'.
2025-06-30T09:17:07.2441220+05:30 [Information] Executing step 'Initialize Database' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "2f169908-113b-4692-9265-37cfc7df8ad4",
        "tables": {},
        "variables": {},
        "name": "Initialize Database",
        "description": "Setup initial database state"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:17:07.2458232+05:30 [Information] Processing $var: path 'baseUrl'
2025-06-30T09:17:07.2472682+05:30 [Information] Checking if 'baseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:17:07.2488085+05:30 [Information] Resolving path 'baseUrl' using global variables.
2025-06-30T09:17:07.2490224+05:30 [Information] Resolved JPath '$var:baseUrl' to 'https://httpbin.org'
2025-06-30T09:17:07.2491267+05:30 [Information] Resolved template pattern '{{$var:baseUrl}}' to 'https://httpbin.org' in '{{$var:baseUrl}}/post'
2025-06-30T09:17:07.2493797+05:30 [Information] Resolved template '{{$var:baseUrl}}/post' to 'https://httpbin.org/post'
2025-06-30T09:17:07.2516134+05:30 [Information] Set Value to Step '2f169908-113b-4692-9265-37cfc7df8ad4' input:
{
  "Method": "POST",
  "RequestUri": "https://httpbin.org/post",
  "Headers": {
    "Content-Type": "application/json"
  },
  "Content": {
    "action": "initialize_db"
  }
}
2025-06-30T09:17:09.5574451+05:30 [Information] Set Value to Step '2f169908-113b-4692-9265-37cfc7df8ad4' output:
{
  "statusCode": 200,
  "reasonPhrase": "OK",
  "headers": [
    {
      "Key": "Date",
      "Value": [
        "Mon, 30 Jun 2025 03:47:14 GMT"
      ]
    },
    {
      "Key": "Connection",
      "Value": [
        "keep-alive"
      ]
    },
    {
      "Key": "Server",
      "Value": [
        "gunicorn/19.9.0"
      ]
    },
    {
      "Key": "Access-Control-Allow-Origin",
      "Value": [
        "*"
      ]
    },
    {
      "Key": "Access-Control-Allow-Credentials",
      "Value": [
        "true"
      ]
    }
  ],
  "content": {
    "args": {},
    "data": "{\r\n  \"action\": \"initialize_db\"\r\n}",
    "files": {},
    "form": {},
    "headers": {
      "Content-Length": "33",
      "Content-Type": "application/json; charset=utf-8",
      "Host": "httpbin.org",
      "X-Amzn-Trace-Id": "Root=1-686208c2-678e163d385abea067705606"
    },
    "json": {
      "action": "initialize_db"
    },
    "origin": "**************",
    "url": "https://httpbin.org/post"
  }
}
2025-06-30T09:17:09.5580058+05:30 [Information] Resolving path 'output.content.json.action' using step input/output.
2025-06-30T09:17:09.5582454+05:30 [Information] Resolved JPath 'output.content.json.action' to 'initialize_db'
2025-06-30T09:17:09.5592235+05:30 [Information] Set Value to Step '2f169908-113b-4692-9265-37cfc7df8ad4' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T03:47:07.2428288Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:17:09.5616509+05:30 [Information] Run Asserter : {"Expression1":{"JPath":"$curStep:output.content.json.action","ErrorMesage":null},"Expression2":{"Value":null,"ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:17:09.5620098+05:30 [Information] Resolving path 'output.content.json.action' using $curStep:
2025-06-30T09:17:09.5621507+05:30 [Information] Resolved JPath '$curStep:output.content.json.action' to 'initialize_db'
2025-06-30T09:17:09.5635342+05:30 [Information] Error in EqualExpression: The method or operation is not implemented.
2025-06-30T09:17:09.5644176+05:30 [Information] Set Value to Step '2f169908-113b-4692-9265-37cfc7df8ad4' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 2311,
  "startTime": "2025-06-30T03:47:07.2428288Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:17:09.5645903+05:30 [Information] Step 'Initialize Database' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "2f169908-113b-4692-9265-37cfc7df8ad4",
        "tables": {},
        "variables": {},
        "name": "Initialize Database",
        "description": "Setup initial database state",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "action": "initialize_db"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 03:47:14 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"action\": \"initialize_db\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "33",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-686208c2-678e163d385abea067705606"
            },
            "json": {
              "action": "initialize_db"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 2311,
          "startTime": "2025-06-30T03:47:07.2428288Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:17:09.5647488+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:17:09.5648359+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:17:09.5648934+05:30 [Information] No performance data found for step 'Initialize Database' (http)
2025-06-30T09:17:09.5651767+05:30 [Information] [ OK       ]
2025-06-30T09:17:09.5661863+05:30 [Information] [----------] 1 step from Startup (2326 ms total)
