2025-06-30T09:38:11.6799007+05:30 [Information] [----------] 1 step from Simple Get Request
2025-06-30T09:38:11.6809915+05:30 [Information] Set Value to Step '078583d9-2b2c-4610-a11b-2f9afeb7b1a7' name:
"Step get request"
2025-06-30T09:38:11.6812026+05:30 [Information] Set Value to Step '078583d9-2b2c-4610-a11b-2f9afeb7b1a7' description:
"Simple GET Request"
2025-06-30T09:38:11.6813572+05:30 [Information] [ RUN      ] Simple Get Request > Step get request
2025-06-30T09:38:11.6817359+05:30 [Information] Executing single instance of step 'Step get request'.
2025-06-30T09:38:11.6819353+05:30 [Information] Executing step 'Step get request' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "078583d9-2b2c-4610-a11b-2f9afeb7b1a7",
        "tables": {},
        "variables": {},
        "name": "Step get request",
        "description": "Simple GET Request"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "ProductID": 123
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ],
      "ProductTable": [
        {
          "ProductID": "123",
          "Name": "Laptop",
          "Price": "1500",
          "Specifications": {
            "Processor": "Intel i7",
            "RAM": "16GB",
            "Storage": "512GB SSD"
          }
        },
        {
          "ProductID": "102",
          "Name": "Smartphone",
          "Price": "800",
          "Specifications": {
            "Processor": "Snapdragon 888",
            "RAM": "8GB",
            "Storage": "128GB"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:38:11.6823586+05:30 [Information] Processing $var: path 'baseUrl'
2025-06-30T09:38:11.6825129+05:30 [Information] Checking if 'baseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:11.6827495+05:30 [Information] Resolving path 'baseUrl' using global variables.
2025-06-30T09:38:11.6828770+05:30 [Information] Resolved JPath '$var:baseUrl' to 'https://httpbin.org'
2025-06-30T09:38:11.6829565+05:30 [Information] Resolved template pattern '{{$var:baseUrl}}' to 'https://httpbin.org' in '{{$var:baseUrl}}/get'
2025-06-30T09:38:11.6831216+05:30 [Information] Resolved template '{{$var:baseUrl}}/get' to 'https://httpbin.org/get'
2025-06-30T09:38:11.6833312+05:30 [Information] Set Value to Step '078583d9-2b2c-4610-a11b-2f9afeb7b1a7' input:
{
  "Method": "GET",
  "RequestUri": "https://httpbin.org/get",
  "Headers": {
    "Content-Type": "application/json"
  }
}
2025-06-30T09:38:12.4185290+05:30 [Information] Set Value to Step '9dc39cc3-329b-4c6c-8c0f-805c24da5dec' output:
{
  "statusCode": 200,
  "reasonPhrase": "OK",
  "headers": [
    {
      "Key": "Date",
      "Value": [
        "Mon, 30 Jun 2025 04:08:17 GMT"
      ]
    },
    {
      "Key": "Connection",
      "Value": [
        "keep-alive"
      ]
    },
    {
      "Key": "Server",
      "Value": [
        "gunicorn/19.9.0"
      ]
    },
    {
      "Key": "Access-Control-Allow-Origin",
      "Value": [
        "*"
      ]
    },
    {
      "Key": "Access-Control-Allow-Credentials",
      "Value": [
        "true"
      ]
    }
  ],
  "content": {
    "args": {},
    "headers": {
      "Host": "httpbin.org",
      "X-Amzn-Trace-Id": "Root=1-68620db1-7ba9fbd00a8a50db210fa91e"
    },
    "origin": "**************",
    "url": "https://httpbin.org/get"
  }
}
2025-06-30T09:38:12.4199192+05:30 [Information] Run Asserter : {"Expression1":{"Value":"200","ErrorMesage":null},"Expression2":{"JPath":"$curStep:$.output.statusCode","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:38:12.4201090+05:30 [Information] Resolving path '$.output.statusCode' using $curStep:
2025-06-30T09:38:12.4202379+05:30 [Information] Resolved JPath '$curStep:$.output.statusCode' to '200'
2025-06-30T09:38:12.4204856+05:30 [Information] Run Asserter : {"Expression1":{"Value":"OK","ErrorMesage":null},"Expression2":{"JPath":"output.reasonPhrase","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:38:12.4206638+05:30 [Information] Resolving path 'output.reasonPhrase' using step input/output.
2025-06-30T09:38:12.4207576+05:30 [Information] Resolved JPath 'output.reasonPhrase' to 'OK'
2025-06-30T09:38:12.4210673+05:30 [Information] Set Value to Step '078583d9-2b2c-4610-a11b-2f9afeb7b1a7' output:
{
  "statusCode": 200,
  "reasonPhrase": "OK",
  "headers": [
    {
      "Key": "Date",
      "Value": [
        "Mon, 30 Jun 2025 04:08:17 GMT"
      ]
    },
    {
      "Key": "Connection",
      "Value": [
        "keep-alive"
      ]
    },
    {
      "Key": "Server",
      "Value": [
        "gunicorn/19.9.0"
      ]
    },
    {
      "Key": "Access-Control-Allow-Origin",
      "Value": [
        "*"
      ]
    },
    {
      "Key": "Access-Control-Allow-Credentials",
      "Value": [
        "true"
      ]
    }
  ],
  "content": {
    "args": {},
    "headers": {
      "Host": "httpbin.org",
      "X-Amzn-Trace-Id": "Root=1-68620db1-7ba9fbd00a8a50db210fa91e"
    },
    "origin": "**************",
    "url": "https://httpbin.org/get"
  }
}
2025-06-30T09:38:12.4212180+05:30 [Information] Set Value to Step '078583d9-2b2c-4610-a11b-2f9afeb7b1a7' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:08:11.6817311Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:38:12.4213083+05:30 [Information] Run Asserter : {"Expression1":{"Value":"{{$var:baseUrl}}/get","ErrorMesage":null},"Expression2":{"JPath":"$curStep:$.output.content.url","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:38:12.4214136+05:30 [Information] Processing $var: path 'baseUrl'
2025-06-30T09:38:12.4214966+05:30 [Information] Checking if 'baseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:12.4215646+05:30 [Information] Resolving path 'baseUrl' using global variables.
2025-06-30T09:38:12.4216172+05:30 [Information] Resolved JPath '$var:baseUrl' to 'https://httpbin.org'
2025-06-30T09:38:12.4216629+05:30 [Information] Resolved template pattern '{{$var:baseUrl}}' to 'https://httpbin.org' in '{{$var:baseUrl}}/get'
2025-06-30T09:38:12.4217142+05:30 [Information] Resolved template '{{$var:baseUrl}}/get' to 'https://httpbin.org/get'
2025-06-30T09:38:12.4218347+05:30 [Information] Resolving path '$.output.content.url' using $curStep:
2025-06-30T09:38:12.4218930+05:30 [Information] Resolved JPath '$curStep:$.output.content.url' to 'https://httpbin.org/get'
2025-06-30T09:38:12.4219658+05:30 [Information] Run Asserter : {"Expression1":{"Value":"200","ErrorMesage":null},"Expression2":{"JPath":"$curStep:$.output.statusCode","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:38:12.4220254+05:30 [Information] Resolving path '$.output.statusCode' using $curStep:
2025-06-30T09:38:12.4220856+05:30 [Information] Resolved JPath '$curStep:$.output.statusCode' to '200'
2025-06-30T09:38:12.4221534+05:30 [Information] Run Asserter : {"Expression1":{"Value":"OK","ErrorMesage":null},"Expression2":{"JPath":"output.reasonPhrase","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:38:12.4222448+05:30 [Information] Resolving path 'output.reasonPhrase' using step input/output.
2025-06-30T09:38:12.4223138+05:30 [Information] Resolved JPath 'output.reasonPhrase' to 'OK'
2025-06-30T09:38:12.4224013+05:30 [Information] Run Asserter : {"Expression1":{"JPath":"$table:ProductTable[0].ProductID","ErrorMesage":null},"Expression2":{"Value":"{{$var:ProductID}}","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:38:12.4235484+05:30 [Information] Resolving path 'ProductTable[0].ProductID' using global tables.
2025-06-30T09:38:12.4238561+05:30 [Information] Resolved JPath '$table:ProductTable[0].ProductID' to '123'
2025-06-30T09:38:12.4239958+05:30 [Information] Processing $var: path 'ProductID'
2025-06-30T09:38:12.4241052+05:30 [Information] Checking if 'ProductID' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:12.4241728+05:30 [Information] Resolving path 'ProductID' using global variables.
2025-06-30T09:38:12.4242465+05:30 [Information] Resolved JPath '$var:ProductID' to '123'
2025-06-30T09:38:12.4242927+05:30 [Information] Resolved template pattern '{{$var:ProductID}}' to '123' in '{{$var:ProductID}}'
2025-06-30T09:38:12.4243400+05:30 [Information] Resolved template '{{$var:ProductID}}' to '123'
2025-06-30T09:38:12.4244164+05:30 [Information] Run Asserter : {"Expression1":{"JPath":"$table:UserTable[0].Username","ErrorMesage":null},"Expression2":{"Value":"user1","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:38:12.4244820+05:30 [Information] Resolving path 'UserTable[0].Username' using global tables.
2025-06-30T09:38:12.4245395+05:30 [Information] Resolved JPath '$table:UserTable[0].Username' to 'user1'
2025-06-30T09:38:12.4249469+05:30 [Information] Set Value to Step '078583d9-2b2c-4610-a11b-2f9afeb7b1a7' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 741,
  "startTime": "2025-06-30T04:08:11.6817311Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:38:12.4251446+05:30 [Information] Step 'Step get request' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "078583d9-2b2c-4610-a11b-2f9afeb7b1a7",
        "tables": {},
        "variables": {},
        "name": "Step get request",
        "description": "Simple GET Request",
        "input": {
          "Method": "GET",
          "RequestUri": "https://httpbin.org/get",
          "Headers": {
            "Content-Type": "application/json"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:08:17 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "headers": {
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620db1-7ba9fbd00a8a50db210fa91e"
            },
            "origin": "**************",
            "url": "https://httpbin.org/get"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 741,
          "startTime": "2025-06-30T04:08:11.6817311Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "ProductID": 123
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ],
      "ProductTable": [
        {
          "ProductID": "123",
          "Name": "Laptop",
          "Price": "1500",
          "Specifications": {
            "Processor": "Intel i7",
            "RAM": "16GB",
            "Storage": "512GB SSD"
          }
        },
        {
          "ProductID": "102",
          "Name": "Smartphone",
          "Price": "800",
          "Specifications": {
            "Processor": "Snapdragon 888",
            "RAM": "8GB",
            "Storage": "128GB"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:38:12.4252825+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:38:12.4253927+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:38:12.4254570+05:30 [Information] No performance data found for step 'Step get request' (http)
2025-06-30T09:38:12.4255089+05:30 [Information] [ OK       ]
2025-06-30T09:38:12.4258247+05:30 [Information] [----------] 1 step from Simple Get Request (744 ms total)
