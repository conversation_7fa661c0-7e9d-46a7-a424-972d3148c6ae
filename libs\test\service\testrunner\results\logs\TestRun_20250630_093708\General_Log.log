2025-06-30T09:37:09.1037540+05:30 [Information] Global variables set to: baseUrl: https://httpbin.org, serverIp: 127.0.0.1, serverPort: 60606
2025-06-30T09:37:09.1183687+05:30 [Information] Loading tests from D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc with pattern *math-expressions-simple.yaml
2025-06-30T09:37:09.1221841+05:30 [Information] Loading test from D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc\math-expressions-simple.yaml
2025-06-30T09:37:09.1341273+05:30 [Information] Global variables set to: 
2025-06-30T09:37:09.1352095+05:30 [Information] [==========] Running 1 test from Yaml files
2025-06-30T09:37:09.1364777+05:30 [Information] [-------------------------------------    Startup     -------------------------------------]
2025-06-30T09:37:09.1601501+05:30 [Information] [----------] 1 step from Startup
2025-06-30T09:37:09.1625511+05:30 [Information] [ RUN      ] Startup > Initialize Database
2025-06-30T09:37:11.2912802+05:30 [Information] [ OK       ]
2025-06-30T09:37:11.2923895+05:30 [Information] [----------] 1 step from Startup (2131 ms total)
2025-06-30T09:37:11.2946626+05:30 [Information] Global variables set to: curDir: D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc, baseUrl: https://httpbin.org, serverIp: 127.0.0.1, serverPort: 60606, dbInitStatus: initialize_db
2025-06-30T09:37:11.2952184+05:30 [Information] [------------------------------------- Test Execution -------------------------------------]
2025-06-30T09:37:11.2965531+05:30 [Information] [----------] 6 steps from Simple Mathematical Expressions Demo - Passing Tests
2025-06-30T09:37:11.2984991+05:30 [Information] [ RUN      ] Simple Mathematical Expressions Demo - Passing Tests > Fast Baseline Operation
2025-06-30T09:37:11.3910561+05:30 [Information] [ OK       ]
2025-06-30T09:37:11.3915448+05:30 [Information] [ RUN      ] Simple Mathematical Expressions Demo - Passing Tests > Regression Threshold Test
2025-06-30T09:37:11.6276381+05:30 [Information] [ OK       ]
2025-06-30T09:37:11.6280871+05:30 [Information] [ RUN      ] Simple Mathematical Expressions Demo - Passing Tests > Complex Expression Test
2025-06-30T09:37:11.8119662+05:30 [Information] [ OK       ]
2025-06-30T09:37:11.8122903+05:30 [Information] [ RUN      ] Simple Mathematical Expressions Demo - Passing Tests > Operator Precedence Test
2025-06-30T09:37:11.9079201+05:30 [Information] [ OK       ]
2025-06-30T09:37:11.9086076+05:30 [Information] [ RUN      ] Simple Mathematical Expressions Demo - Passing Tests > Performance Budget Test
2025-06-30T09:37:12.1420866+05:30 [Information] [ OK       ]
2025-06-30T09:37:12.1424320+05:30 [Information] [ RUN      ] Simple Mathematical Expressions Demo - Passing Tests > Error Message Demo
2025-06-30T09:37:12.2299339+05:30 [Information] [ OK       ]
2025-06-30T09:37:12.2301927+05:30 [Information] [----------] 6 steps from Simple Mathematical Expressions Demo - Passing Tests (933 ms total)
2025-06-30T09:37:12.2307544+05:30 [Information] [-------------------------------------    Teardown    -------------------------------------]
2025-06-30T09:37:12.2318137+05:30 [Information] [----------] 1 step from Teardown
2025-06-30T09:37:12.2323210+05:30 [Information] [ RUN      ] Teardown > Clean up Database
