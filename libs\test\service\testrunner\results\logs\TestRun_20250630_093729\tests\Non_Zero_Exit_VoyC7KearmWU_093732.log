2025-06-30T09:37:32.7454636+05:30 [Information] [----------] 1 step from Non-Zero Exit Code Test
2025-06-30T09:37:32.7460305+05:30 [Information] Set Value to Step '48f94aa2-2fbc-4740-94a0-d4dc43d9a0b8' name:
"Step exit failure"
2025-06-30T09:37:32.7461372+05:30 [Information] Set Value to Step '48f94aa2-2fbc-4740-94a0-d4dc43d9a0b8' description:
"Command exits with code 1"
2025-06-30T09:37:32.7461984+05:30 [Information] [ RUN      ] Non-Zero Exit Code Test > Step exit failure
2025-06-30T09:37:32.7464206+05:30 [Information] Executing single instance of step 'Step exit failure'.
2025-06-30T09:37:32.7465435+05:30 [Information] Executing step 'Step exit failure' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "48f94aa2-2fbc-4740-94a0-d4dc43d9a0b8",
        "tables": {},
        "variables": {},
        "name": "Step exit failure",
        "description": "Command exits with code 1"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:37:32.7468317+05:30 [Information] Set Value to Step '48f94aa2-2fbc-4740-94a0-d4dc43d9a0b8' input:
{
  "command": "cmd.exe",
  "arguments": [
    "/c",
    "exit 1"
  ],
  "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
  "printOutput": false
}
2025-06-30T09:37:32.7738737+05:30 [Information] Set Value to Step '48f94aa2-2fbc-4740-94a0-d4dc43d9a0b8' output:
{
  "stdout": "",
  "stderr": "",
  "exitCode": 1
}
2025-06-30T09:37:32.7740795+05:30 [Information] Set Value to Step '48f94aa2-2fbc-4740-94a0-d4dc43d9a0b8' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:07:32.7464187Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "cmd"
}
2025-06-30T09:37:32.7741894+05:30 [Information] Run Asserter : {"Expression1":{"Value":"1","ErrorMesage":null},"Expression2":{"JPath":"$curStep:$.output.exitCode","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:37:32.7743069+05:30 [Information] Resolving path '$.output.exitCode' using $curStep:
2025-06-30T09:37:32.7744220+05:30 [Information] Resolved JPath '$curStep:$.output.exitCode' to '1'
2025-06-30T09:37:32.7745668+05:30 [Information] Set Value to Step '48f94aa2-2fbc-4740-94a0-d4dc43d9a0b8' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 27,
  "startTime": "2025-06-30T04:07:32.7464187Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "cmd"
}
2025-06-30T09:37:32.7746598+05:30 [Information] Step 'Step exit failure' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "48f94aa2-2fbc-4740-94a0-d4dc43d9a0b8",
        "tables": {},
        "variables": {},
        "name": "Step exit failure",
        "description": "Command exits with code 1",
        "input": {
          "command": "cmd.exe",
          "arguments": [
            "/c",
            "exit 1"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "",
          "stderr": "",
          "exitCode": 1
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 27,
          "startTime": "2025-06-30T04:07:32.7464187Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:37:32.7747361+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:37:32.7747951+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:37:32.7748511+05:30 [Information] No performance data found for step 'Step exit failure' (cmd)
2025-06-30T09:37:32.7748979+05:30 [Information] [ OK       ]
2025-06-30T09:37:32.7751446+05:30 [Information] [----------] 1 step from Non-Zero Exit Code Test (29 ms total)
