2025-06-30T09:23:33.8206663+05:30 [Information] Loading tests from D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc\examples with pattern *performance-testing-examples.yaml
2025-06-30T09:23:33.8336429+05:30 [Information] Loading test from D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc\examples\performance-testing-examples.yaml
2025-06-30T09:23:33.9169008+05:30 [Information] Global variables set to: 
2025-06-30T09:23:33.9179769+05:30 [Information] [==========] Running 1 test from Yaml files
2025-06-30T09:23:33.9196495+05:30 [Information] [------------------------------------- Test Execution -------------------------------------]
2025-06-30T09:23:33.9350609+05:30 [Error] Failed to initialize test Performance Testing Examples: Table must have a 'Name' and 'Data'.
2025-06-30T09:23:33.9356098+05:30 [Error] Failed to run the test: Table must have a 'Name' and 'Data'.
