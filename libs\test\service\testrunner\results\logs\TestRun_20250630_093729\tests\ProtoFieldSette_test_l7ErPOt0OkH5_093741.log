2025-06-30T09:37:41.9194173+05:30 [Information] [----------] 5 steps from ProtoFieldSetters test
2025-06-30T09:37:41.9214827+05:30 [Information] Set Value to Step 'f2cda7de-b484-4334-a009-d087273002ad' name:
"Step 1: Connect to the MBusClient with admin credentials"
2025-06-30T09:37:41.9217501+05:30 [Information] Set Value to Step 'f2cda7de-b484-4334-a009-d087273002ad' description:
"Connect to the MBusClient with admin credentials"
2025-06-30T09:37:41.9218032+05:30 [Information] [ RUN      ] ProtoFieldSetters test > Step 1: Connect to the MBusClient with admin credentials
2025-06-30T09:37:41.9220478+05:30 [Information] Executing single instance of step 'Step 1: Connect to the MBusClient with admin credentials'.
2025-06-30T09:37:41.9222660+05:30 [Information] Executing step 'Step 1: Connect to the MBusClient with admin credentials' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "f2cda7de-b484-4334-a009-d087273002ad",
        "tables": {},
        "variables": {},
        "name": "Step 1: Connect to the MBusClient with admin credentials",
        "description": "Connect to the MBusClient with admin credentials"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "userId": "surgeon2",
      "userIdentifier": {
        "value": "surgeon2",
        "assigner": 6
      }
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:37:41.9225207+05:30 [Information] Processing $var: path 'serverIp'
2025-06-30T09:37:41.9226216+05:30 [Information] Checking if 'serverIp' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:41.9226775+05:30 [Information] Resolving path 'serverIp' using global variables.
2025-06-30T09:37:41.9227200+05:30 [Information] Resolved JPath '$var:serverIp' to '127.0.0.1'
2025-06-30T09:37:41.9227550+05:30 [Information] Resolved template pattern '{{$var:serverIp}}' to '127.0.0.1' in '{{$var:serverIp}}'
2025-06-30T09:37:41.9229466+05:30 [Information] Resolved template '{{$var:serverIp}}' to '127.0.0.1'
2025-06-30T09:37:41.9230378+05:30 [Information] Processing $var: path 'serverPort'
2025-06-30T09:37:41.9230919+05:30 [Information] Checking if 'serverPort' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:41.9231386+05:30 [Information] Resolving path 'serverPort' using global variables.
2025-06-30T09:37:41.9231850+05:30 [Information] Resolved JPath '$var:serverPort' to '60606'
2025-06-30T09:37:41.9232213+05:30 [Information] Resolved template pattern '{{$var:serverPort}}' to '60606' in '{{$var:serverPort}}'
2025-06-30T09:37:41.9232624+05:30 [Information] Resolved template '{{$var:serverPort}}' to '60606'
2025-06-30T09:37:41.9235010+05:30 [Information] Set Value to Step 'f2cda7de-b484-4334-a009-d087273002ad' input:
{
  "ServerIp": "127.0.0.1",
  "ServerPort": 60606,
  "Username": "admin",
  "Password": "admin"
}
2025-06-30T09:37:43.9495242+05:30 [Information] Failed to execute step 'Step 1: Connect to the MBusClient with admin credentials': Failed to execute step : Cannot connect to 127.0.0.1:60606.
2025-06-30T09:37:43.9497303+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:37:43.9498112+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:37:43.9498761+05:30 [Information] No performance data found for step 'Step 1: Connect to the MBusClient with admin credentials' (mbus)
2025-06-30T09:37:43.9502236+05:30 [Error] Failed to execute step : Cannot connect to 127.0.0.1:60606.
2025-06-30T09:37:43.9504140+05:30 [Error] [ FAILED   ]
2025-06-30T09:37:43.9507270+05:30 [Information] [----------] 5 steps from ProtoFieldSetters test (2029 ms total)
