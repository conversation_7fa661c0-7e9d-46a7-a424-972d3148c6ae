# Performance Testing Guide

TestRunner is an excellent tool for performance testing that helps you validate response times, test system behavior under load, and ensure your applications meet performance requirements. This guide shows you how to use TestRunner effectively for different types of performance testing.

> **Quick Start**: See [performance-testing-examples.yaml](../testrunner/etc/examples/performance-testing-examples.yaml) for ready-to-run examples that demonstrate all the concepts in this guide.

## Why Use TestRunner for Performance Testing?

TestRunner offers unique advantages for performance testing:

- **Integrated Testing**: Combine functional and performance validation in one test
- **Real-time Metrics**: Access performance data during test execution for dynamic validation
- **Flexible Thresholds**: Set performance limits and validate against them
- **Controlled Timing**: Use DelayMs to simulate realistic user behavior and system conditions
- **Retry Logic**: Test warm-up scenarios, stability, and recovery patterns
- **Multiple Protocols**: Test HTTP APIs, RPC services, and system operations
- **Rich Reporting**: Generate detailed performance reports with visualizations

## Types of Performance Testing with TestRunner

### 1. **Response Time Testing**
Validate that your APIs and services respond within acceptable time limits.

### 2. **Load Testing**
Test how your system performs under expected user loads.

### 3. **Stress Testing**
Find your system's breaking point by gradually increasing load.

### 4. **Baseline Testing**
Establish performance benchmarks for future comparison.

### 5. **Regression Testing**
Ensure performance doesn't degrade with new releases.

### 6. **Warm-up Testing**
Test how long it takes for systems to reach optimal performance.

### 7. **Stability Testing**
Validate consistent performance over time.

### 8. **Recovery Testing**
Test how quickly systems recover after load spikes.

## Getting Started: Your First Performance Test

Let's start with a simple example that validates API response time:

```yaml
Name: "My First Performance Test"
Description: "Validate API response time"

Steps:
  - Name: "API Response Time Test"
    Input:
      Method: "GET"
      RequestUri: "https://httpbin.org/json"
    Asserters:
      # Functional validation
      - AssertEq:
          ConstExpr: 200
          JPathExpr: "$curStep:$.output.statusCode"

      # Performance validation - must complete within 2 seconds
      - AssertLt:
          ConstExpr: 2000
          JPathExpr: "$curStep:performance.executionTimeMs"
          ErrorMessage: "API response too slow: {{$curStep:performance.executionTimeMs}}ms"
```

**Key Points:**
- `$curStep:performance.executionTimeMs` gives you the total execution time
- Set realistic thresholds based on your requirements
- Include meaningful error messages for debugging

## Performance Data Available in TestRunner

TestRunner automatically captures performance metrics for every test step:

### Core Performance Metrics
```yaml
$curStep:performance.executionTimeMs    # Total step execution time
$curStep:performance.assertionTimeMs    # Time spent on assertions
$curStep:performance.stepType           # Step type (http, rpc, cmd)
$curStep:performance.startTime          # Step start timestamp
$curStep:performance.endTime            # Step end timestamp
```

### Using Performance Data
```yaml
# Set performance thresholds
- AssertLt:
    ConstExpr: 1000  # Must complete within 1 second
    JPathExpr: "$curStep:performance.executionTimeMs"

# Store baseline for comparison
Output:
  Store:
    baselineTime: "performance.executionTimeMs"

# Compare against baseline
- AssertLt:
    ConstExpr: "{{$var:baselineTime * 1.5}}"  # No more than 150% of baseline
    JPathExpr: "$curStep:performance.executionTimeMs"
```

## Common Performance Testing Scenarios

### 1. Response Time Validation

**Goal**: Ensure your APIs respond within acceptable time limits.

**When to use**: For SLA validation, user experience testing, and basic performance monitoring.

```yaml
- Name: "API Response Time Test"
  Input:
    Method: "GET"
    RequestUri: "https://api.example.com/users"
  Asserters:
    - AssertEq:
        ConstExpr: 200
        JPathExpr: "$curStep:$.output.statusCode"

    # Good user experience: under 1 second
    - AssertLt:
        ConstExpr: 1000
        JPathExpr: "$curStep:performance.executionTimeMs"
        ErrorMessage: "API response too slow for good UX: {{$curStep:performance.executionTimeMs}}ms"

    # SLA requirement: under 3 seconds
    - AssertLt:
        ConstExpr: 3000
        JPathExpr: "$curStep:performance.executionTimeMs"
        ErrorMessage: "API response exceeded SLA: {{$curStep:performance.executionTimeMs}}ms"
```

### 2. Baseline and Regression Testing

**Goal**: Establish performance baselines and detect regressions.

**When to use**: In CI/CD pipelines to catch performance regressions before deployment.

```yaml
Variables:
  MaxRegressionPercent: 150  # Allow 50% performance degradation

Steps:
  # Establish baseline
  - Name: "Performance Baseline"
    Input:
      Method: "GET"
      RequestUri: "https://api.example.com/baseline"
    Output:
      Store:
        baselineTime: "performance.executionTimeMs"
    Asserters:
      - AssertLt:
          ConstExpr: 2000  # Baseline should be reasonable
          JPathExpr: "$curStep:performance.executionTimeMs"

  # Test for regression
  - Name: "Regression Check"
    Input:
      Method: "GET"
      RequestUri: "https://api.example.com/new-feature"
    Asserters:
      # Should not be more than 50% slower than baseline
      - AssertLt:
          ConstExpr: "{{$var:baselineTime * $var:MaxRegressionPercent / 100}}"
          JPathExpr: "$curStep:performance.executionTimeMs"
          ErrorMessage: "Performance regression detected: {{$curStep:performance.executionTimeMs}}ms vs baseline {{$var:baselineTime}}ms"
```

### 3. Load Testing Simulation

**Goal**: Test how your system performs under expected user loads.

**When to use**: Before major releases, capacity planning, and performance validation.

#### Basic Load Testing with Inline Tables

```yaml
- Name: "Basic Load Test"
  RepeatFor:
    Table: "LoadTestRequests"
  Input:
    Method: "{{$row:Method}}"
    RequestUri: "{{$row:Url}}"
    Headers:
      User-Agent: "LoadTest-{{$row:UserId}}"
  Execution:
    DelayMs: 250  # 250ms between requests = 4 requests/second per user
  Asserters:
    # Each request should succeed
    - AssertEq:
        ConstExpr: 200
        JPathExpr: "$curStep:$.output.statusCode"
        ErrorMessage: "Request {{$row:RequestId}} failed during load test"

    # Performance should remain acceptable under load
    - AssertLt:
        ConstExpr: 5000  # More lenient threshold under load
        JPathExpr: "$curStep:performance.executionTimeMs"
        ErrorMessage: "Request {{$row:RequestId}} too slow under load: {{$curStep:performance.executionTimeMs}}ms"

Tables:
  - Name: "LoadTestRequests"
    Data:
      - RequestId: "REQ001"
        Method: "GET"
        Url: "https://api.example.com/users"
        UserId: "user1"
      - RequestId: "REQ002"
        Method: "GET"
        Url: "https://api.example.com/products"
        UserId: "user2"
      - RequestId: "REQ003"
        Method: "POST"
        Url: "https://api.example.com/orders"
        UserId: "user3"
```

#### Advanced Load Testing with External Tables

For larger scale load testing, use external files to manage test data:

**Create load test data file: `load-test-data.csv`**
```csv
RequestId,Method,Endpoint,UserId,ExpectedStatus,MaxResponseTime
REQ001,GET,/api/users,user1,200,1000
REQ002,GET,/api/products,user2,200,1500
REQ003,POST,/api/orders,user3,201,2000
REQ004,GET,/api/analytics,user4,200,3000
REQ005,PUT,/api/users/profile,user5,200,1200
REQ006,DELETE,/api/cache,user6,204,500
REQ007,GET,/api/reports,user7,200,5000
REQ008,POST,/api/notifications,user8,201,800
```

**Load testing with external data:**
```yaml
Variables:
  BaseUrl: "https://api.example.com"
  LoadTestUsers: 50  # Simulate 50 concurrent users

Tables:
  - Name: "LoadTestData"
    Data: "file://./data/load-test-data.csv"

Steps:
  - Name: "High-Volume Load Test"
    RepeatFor:
      Table: "LoadTestData"
      Variables:
        endpoint: "{{$var:BaseUrl}}{{$row:Endpoint}}"
        maxTime: "{{$row:MaxResponseTime}}"
    Input:
      Method: "{{$row:Method}}"
      RequestUri: "{{$var:endpoint}}"
      Headers:
        User-Agent: "LoadTest-{{$row:UserId}}"
        X-Test-Request-Id: "{{$row:RequestId}}"
    Execution:
      DelayMs: 100  # 100ms between requests for high throughput
    Asserters:
      # Functional validation
      - AssertEq:
          ConstExpr: "{{$row:ExpectedStatus}}"
          JPathExpr: "$curStep:$.output.statusCode"
          ErrorMessage: "Request {{$row:RequestId}} returned {{$curStep:$.output.statusCode}} instead of {{$row:ExpectedStatus}}"

      # Performance validation with per-request thresholds
      - AssertLt:
          ConstExpr: "{{$var:maxTime}}"
          JPathExpr: "$curStep:performance.executionTimeMs"
          ErrorMessage: "Request {{$row:RequestId}} too slow: {{$curStep:performance.executionTimeMs}}ms > {{$var:maxTime}}ms"

    # Store results back to table for analysis
    Output:
      Store:
        Row:
          ActualResponseTime: "performance.executionTimeMs"
          ActualStatus: "$.output.statusCode"
          TestTimestamp: "performance.startTime"
```

#### Benefits of External Table Load Testing

✅ **Scalability**: Easily test with hundreds or thousands of requests
✅ **Data Management**: Separate test data from test logic
✅ **Realistic Scenarios**: Use production-like data patterns
✅ **Parameterization**: Different thresholds per request type
✅ **Result Storage**: Capture actual performance data for analysis
✅ **Maintainability**: Update test data without changing test code

#### Creating Comprehensive Load Test Scenarios

**Example: E-commerce Load Testing**

Create realistic load testing scenarios using external tables to simulate different user behaviors:

**File: `user-scenarios.json`**
```json
[
  {
    "ScenarioId": "BROWSE_001",
    "UserType": "Browser",
    "Action": "Browse Products",
    "Method": "GET",
    "Endpoint": "/api/products",
    "DelayMs": 2000,
    "MaxResponseTime": 1000,
    "Weight": 40
  },
  {
    "ScenarioId": "SEARCH_001",
    "UserType": "Searcher",
    "Action": "Search Products",
    "Method": "GET",
    "Endpoint": "/api/search?q=laptop",
    "DelayMs": 3000,
    "MaxResponseTime": 1500,
    "Weight": 30
  },
  {
    "ScenarioId": "PURCHASE_001",
    "UserType": "Buyer",
    "Action": "Create Order",
    "Method": "POST",
    "Endpoint": "/api/orders",
    "DelayMs": 5000,
    "MaxResponseTime": 3000,
    "Weight": 20
  },
  {
    "ScenarioId": "ADMIN_001",
    "UserType": "Admin",
    "Action": "View Analytics",
    "Method": "GET",
    "Endpoint": "/api/analytics/dashboard",
    "DelayMs": 10000,
    "MaxResponseTime": 5000,
    "Weight": 10
  }
]
```

**Load test using realistic scenarios:**
```yaml
Name: "E-commerce Load Testing"
Description: "Comprehensive load testing with realistic user scenarios"

Variables:
  BaseUrl: "https://api.ecommerce.com"
  TotalUsers: 100
  TestDuration: "5 minutes"

Tables:
  - Name: "UserScenarios"
    Data: "file://./data/user-scenarios.json"

Steps:
  - Name: "Realistic User Load Simulation"
    RepeatFor:
      Table: "UserScenarios"
      Variables:
        fullUrl: "{{$var:BaseUrl}}{{$row:Endpoint}}"
        userDelay: "{{$row:DelayMs}}"
        maxTime: "{{$row:MaxResponseTime}}"
        scenario: "{{$row:ScenarioId}}"
    Input:
      Method: "{{$row:Method}}"
      RequestUri: "{{$var:fullUrl}}"
      Headers:
        User-Agent: "LoadTest-{{$row:UserType}}"
        X-Scenario-Id: "{{$var:scenario}}"
        X-User-Weight: "{{$row:Weight}}"
    Execution:
      DelayMs: "{{$var:userDelay}}"  # Realistic user think time
    Asserters:
      # Functional validation
      - AssertEq:
          ConstExpr: 200
          JPathExpr: "$curStep:$.output.statusCode"
          ErrorMessage: "Scenario {{$var:scenario}} failed: {{$curStep:$.output.statusCode}}"

      # Performance validation per scenario type
      - AssertLt:
          ConstExpr: "{{$var:maxTime}}"
          JPathExpr: "$curStep:performance.executionTimeMs"
          ErrorMessage: "{{$row:Action}} too slow: {{$curStep:performance.executionTimeMs}}ms > {{$var:maxTime}}ms"

      # Validate response content exists
      - AssertNotNull:
          JPathExpr: "$curStep:$.output.content"
          ErrorMessage: "No response content for scenario {{$var:scenario}}"

    # Store performance results for analysis
    Output:
      Store:
        Row:
          ActualResponseTime: "performance.executionTimeMs"
          AssertionTime: "performance.assertionTimeMs"
          TestResult: "PASSED"
          Timestamp: "performance.startTime"
```

**Benefits of this approach:**
- **Realistic Load Patterns**: Different user types with appropriate delays
- **Weighted Scenarios**: More browsers than buyers (realistic distribution)
- **Scenario-Specific Thresholds**: Different performance expectations per action
- **Data Collection**: Store results back to table for post-test analysis
- **Maintainable**: Easy to add new scenarios or adjust weights

For more details on table loading and advanced scenarios, see [Tables Documentation](tables.md).

### 4. Performance Comparison Testing

**Goal**: Compare performance between different endpoints, configurations, or versions.

**When to use**: A/B testing, optimization validation, and architectural decisions.

```yaml
- Name: "Lightweight Endpoint Baseline"
  Input:
    Method: "GET"
    RequestUri: "https://api.example.com/health"  # Simple endpoint
  Execution:
    DelayMs: 100  # Allow system to stabilize
  Output:
    Store:
      lightweightTime: "performance.executionTimeMs"
  Asserters:
    - AssertLt:
        ConstExpr: 500  # Should be very fast
        JPathExpr: "$curStep:performance.executionTimeMs"

- Name: "Complex Endpoint Comparison"
  Input:
    Method: "GET"
    RequestUri: "https://api.example.com/analytics/report"  # Complex endpoint
  Execution:
    DelayMs: 100  # Consistent timing between tests
  Asserters:
    # Should be slower than lightweight endpoint (expected)
    - AssertGt:
        ConstExpr: "{{$var:lightweightTime}}"
        JPathExpr: "$curStep:performance.executionTimeMs"
        ErrorMessage: "Complex endpoint unexpectedly faster than lightweight endpoint"

    # But not excessively slower (performance boundary)
    - AssertLt:
        ConstExpr: "{{$var:lightweightTime * 10}}"  # Max 10x slower
        JPathExpr: "$curStep:performance.executionTimeMs"
        ErrorMessage: "Complex endpoint too slow: {{$curStep:performance.executionTimeMs}}ms vs {{$var:lightweightTime}}ms baseline"

    # Absolute maximum for complex operations
    - AssertLt:
        ConstExpr: 30000  # 30 seconds absolute max
        JPathExpr: "$curStep:performance.executionTimeMs"
        ErrorMessage: "Complex endpoint exceeded absolute maximum: {{$curStep:performance.executionTimeMs}}ms"
```

### 5. Warm-up and Stability Testing

**Goal**: Test how systems reach optimal performance and maintain stability.

**When to use**: Testing JIT compilation, cache warming, connection pooling, and system stability.

```yaml
- Name: "System Warm-up Test"
  Input:
    Method: "GET"
    RequestUri: "https://api.example.com/data"
  Execution:
    DelayMs: 2000      # Wait for system startup
    MaxRetries: 8
    RetryDelayMs: 1000  # Wait between warm-up attempts
    RetryUntil:
      # Keep trying until performance is acceptable
      - ExpectLt:
          ConstExpr: 1000  # Target: under 1 second after warm-up
          JPathExpr: "$curStep:performance.executionTimeMs"
      - ExpectEq:
          ConstExpr: 200
          JPathExpr: "$curStep:$.output.statusCode"
  Asserters:
    - AssertLt:
        ConstExpr: 1000
        JPathExpr: "$curStep:performance.executionTimeMs"
        ErrorMessage: "System failed to warm up properly: {{$curStep:performance.executionTimeMs}}ms"

- Name: "Performance Stability Test"
  Input:
    Method: "GET"
    RequestUri: "https://api.example.com/data"
  Execution:
    DelayMs: 500       # Allow system to stabilize
    MaxRetries: 5
    RetryDelayMs: 200
    RetryUntil:
      # Ensure consistent performance
      - ExpectLt:
          ConstExpr: 1200  # Slightly more lenient for stability
          JPathExpr: "$curStep:performance.executionTimeMs"
      - ExpectEq:
          ConstExpr: 200
          JPathExpr: "$curStep:$.output.statusCode"
  Asserters:
    - AssertLt:
        ConstExpr: 1200
        JPathExpr: "$curStep:performance.executionTimeMs"
        ErrorMessage: "Performance not stable: {{$curStep:performance.executionTimeMs}}ms"
```

## Advanced Performance Testing Techniques

### 1. Using DelayMs for Realistic Testing

**DelayMs** allows you to control timing in your tests, making them more realistic:

```yaml
# Simulate realistic user behavior
- Name: "User Workflow Simulation"
  Input:
    Method: "GET"
    RequestUri: "https://api.example.com/login"
  Execution:
    DelayMs: 1000  # User thinks for 1 second before login
  Asserters:
    - AssertLt:
        ConstExpr: 2000
        JPathExpr: "$curStep:performance.executionTimeMs"

- Name: "Browse Products"
  Input:
    Method: "GET"
    RequestUri: "https://api.example.com/products"
  Execution:
    DelayMs: 3000  # User browses for 3 seconds
  Asserters:
    - AssertLt:
        ConstExpr: 1500
        JPathExpr: "$curStep:performance.executionTimeMs"

# Rate limiting compliance
- Name: "API Rate Limit Compliance"
  Input:
    Method: "GET"
    RequestUri: "https://api.example.com/data"
  Execution:
    DelayMs: 2000  # Respect 30 requests/minute rate limit
  Asserters:
    - AssertLt:
        ConstExpr: 1000  # Should be fast when respecting limits
        JPathExpr: "$curStep:performance.executionTimeMs"
```

### 2. Recovery and Resilience Testing

**Goal**: Test how quickly systems recover after load spikes or failures.

```yaml
- Name: "Post-Load Recovery Test"
  Input:
    Method: "GET"
    RequestUri: "https://api.example.com/health"
  Execution:
    DelayMs: 5000      # Wait 5 seconds after simulated load
    MaxRetries: 10
    RetryDelayMs: 2000  # Check every 2 seconds
    RetryUntil:
      # Keep trying until performance recovers
      - ExpectLt:
          ConstExpr: 1500  # Should recover to acceptable performance
          JPathExpr: "$curStep:performance.executionTimeMs"
      - ExpectEq:
          ConstExpr: 200
          JPathExpr: "$curStep:$.output.statusCode"
  Asserters:
    - AssertLt:
        ConstExpr: 1500
        JPathExpr: "$curStep:performance.executionTimeMs"
        ErrorMessage: "System failed to recover: {{$curStep:performance.executionTimeMs}}ms"
```

### 3. Infrastructure and System Testing

**Goal**: Test performance of system operations, database connections, and infrastructure components.

```yaml
# Database connection simulation
- Name: "Database Connection Performance"
  Type: "cmd"
  Input:
    Command: "timeout"
    Arguments: ["/t", "2", "/nobreak"]  # Simulate 2-second DB connection
  Execution:
    DelayMs: 500  # Application startup delay
  Asserters:
    - AssertEq:
        ConstExpr: 0
        JPathExpr: "$curStep:$.output.exitCode"
        ErrorMessage: "Database connection simulation failed"

# Network service availability
- Name: "Network Service Performance"
  Type: "cmd"
  Input:
    Command: "ping"
    Arguments: ["8.8.8.8", "-n", "1"]  # Test external connectivity
  Execution:
    DelayMs: 1000  # Network initialization delay
  Asserters:
    - AssertEq:
        ConstExpr: 0
        JPathExpr: "$curStep:$.output.exitCode"
        ErrorMessage: "Network service unavailable"
```

## Performance Testing Best Practices

### 1. Set Realistic Thresholds

**Base thresholds on real requirements:**
```yaml
Variables:
  # User experience thresholds
  ExcellentResponseTime: 500   # Under 500ms = excellent
  GoodResponseTime: 1000       # Under 1s = good
  AcceptableResponseTime: 3000 # Under 3s = acceptable

  # SLA thresholds
  SLAResponseTime: 5000        # 5s SLA requirement

Steps:
  - Name: "User Experience Validation"
    Input:
      Method: "GET"
      RequestUri: "https://api.example.com/user-data"
    Asserters:
      # Aim for excellent, accept good
      - AssertLt:
          ConstExpr: "{{$var:GoodResponseTime}}"
          JPathExpr: "$curStep:performance.executionTimeMs"
          ErrorMessage: "Response time {{$curStep:performance.executionTimeMs}}ms not good enough for users"

      # Must meet SLA
      - AssertLt:
          ConstExpr: "{{$var:SLAResponseTime}}"
          JPathExpr: "$curStep:performance.executionTimeMs"
          ErrorMessage: "SLA violation: {{$curStep:performance.executionTimeMs}}ms > {{$var:SLAResponseTime}}ms"
```

### 2. Include Meaningful Error Messages

**Always include context in error messages:**
```yaml
Asserters:
  - AssertLt:
      ConstExpr: 2000
      JPathExpr: "$curStep:performance.executionTimeMs"
      ErrorMessage: "API response too slow: {{$curStep:performance.executionTimeMs}}ms (threshold: 2000ms) for endpoint {{$curStep:input.requestUri}}"
```

### 3. Use DelayMs for Realistic Scenarios

**Simulate real user behavior:**
```yaml
# Realistic user workflow
- Name: "User Login"
  Input:
    Method: "POST"
    RequestUri: "https://api.example.com/login"
  Execution:
    DelayMs: 0  # Immediate login attempt

- Name: "Browse Dashboard"
  Input:
    Method: "GET"
    RequestUri: "https://api.example.com/dashboard"
  Execution:
    DelayMs: 2000  # User takes 2 seconds to navigate

- Name: "Search Products"
  Input:
    Method: "GET"
    RequestUri: "https://api.example.com/search?q=laptop"
  Execution:
    DelayMs: 5000  # User thinks and types search query
```

### 4. Monitor Test Infrastructure Performance

**Don't let test overhead skew results:**
```yaml
Asserters:
  # Validate assertion processing is fast
  - AssertLt:
      ConstExpr: 100  # Assertions should be under 100ms
      JPathExpr: "$curStep:performance.assertionTimeMs"
      ErrorMessage: "Test assertion overhead too high: {{$curStep:performance.assertionTimeMs}}ms"
```

### 5. Plan Your Performance Testing Strategy

**Start simple, then expand:**

1. **Basic Response Time Testing**: Start with simple threshold validation
2. **Baseline Establishment**: Create performance benchmarks
3. **Load Testing**: Add controlled load simulation
4. **Advanced Scenarios**: Include warm-up, stability, and recovery testing

**Example progression:**
```yaml
# Week 1: Basic response time validation
- Name: "Basic API Performance"
  Input:
    Method: "GET"
    RequestUri: "https://api.example.com/health"
  Asserters:
    - AssertLt:
        ConstExpr: 1000
        JPathExpr: "$curStep:performance.executionTimeMs"

# Week 2: Add baseline comparison
- Name: "Performance Regression Check"
  # ... baseline comparison logic

# Week 3: Add load testing
- Name: "Load Test Simulation"
  RepeatFor:
    Table: "LoadTestData"
  # ... load testing logic

# Week 4: Add advanced scenarios
- Name: "Warm-up and Recovery Testing"
  Execution:
    MaxRetries: 10
    RetryUntil:
      # ... advanced retry logic
```

## Running Performance Tests

### Command Line Usage

```bash
# Run performance tests
test_runner.exe -t ./performance-tests -p "*performance*.yaml"

# Run with trace output for debugging
test_runner.exe -t ./performance-tests -p "*performance*.yaml" --trace

# Generate performance report
test_runner.exe -t ./performance-tests -p "*performance*.yaml" -r -f performance
```

### Generate Performance Reports

TestRunner includes a powerful performance reporting feature that generates interactive HTML reports with detailed timing analysis and visualizations, specifically designed for performance testing scenarios.

#### Performance HTML Reports

```bash
# Generate interactive performance HTML report
test_runner.exe -t ./performance-tests -r -f performance

# Generate performance report in custom directory
test_runner.exe -t ./performance-tests -r -f performance -o ./performance-results

# Generate performance report with trace output for debugging
test_runner.exe -t ./performance-tests -r -f performance --trace
```

#### What's Included in Performance Reports

The performance HTML report provides comprehensive analysis through multiple interactive sections:

**📊 Test Suite Overview**
- Total tests, passed/failed tests, total steps
- Total execution time and average test time
- Performance-specific statistics
- RPC statistics (when RPC steps are present):
  - Total RPC steps and timeout counts
  - Average RPC time and slowest RPC identification
  - Service and method-level performance metrics

**📈 Interactive Performance Analysis Tabs**

1. **Test Analysis Tab**:
   - **Test Execution Timeline**: Interactive bar chart showing execution time for each test
   - **Step Duration Analysis**: Bar chart showing execution time for each step with color coding (green for passed, red for failed)
   - **Test Comparison**: Dual-axis chart showing execution time vs step count

2. **RPC Analysis Tab** (when RPC steps are present):
   - **RPC Method Performance Summary**: Aggregated average performance by RPC method
   - **Slowest Individual RPC Calls**: Top 5 slowest individual RPC requests
   - **RPC Method Performance Details Table**: Comprehensive statistics including:
     - Call count, average/min/max times, total time
     - Average network time, timeout counts, retry statistics
     - Color-coded performance indicators (warning/danger for slow methods)

**📋 Detailed Test Views**
- Step-by-step timing breakdowns
- Execution time and assertion time for each step
- Failure messages with context
- RPC-specific details (service name, method name, timing)
- Performance metadata (start time, end time, step type)

#### Enhanced RPC Performance Analysis

**🚀 New Feature: Aggregated RPC Method Analysis**

The improved performance reports now provide much clearer analysis for RPC performance:

**Before**: Individual RPC calls listed separately, making it hard to see patterns
```
ValidateTokenReq: 2ms
ValidateTokenReq: 36ms
ValidateTokenReq: 15ms
GetUserReq: 1ms
GetUserReq: 23ms
...
```

**After**: Aggregated analysis by RPC method with comprehensive statistics
```
RPC Method Performance Details Table:
┌─────────────────────────────────────┬───────┬──────────┬─────────┬─────────┬────────────┐
│ RPC Method                          │ Calls │ Avg (ms) │ Min (ms)│ Max (ms)│ Total (ms) │
├─────────────────────────────────────┼───────┼──────────┼─────────┼─────────┼────────────┤
│ ChangeUserPasswordReq               │   6   │  178.17  │    8    │   314   │    1069    │
│ CreateUserReq                       │   5   │   91.2   │    4    │   160   │    456     │
│ ValidateTokenReq                    │  12   │   3.92   │    2    │    22   │     47     │
└─────────────────────────────────────┴───────┴──────────┴─────────┴─────────┴────────────┘
```

**Key Benefits:**
- **Clear Performance Patterns**: See which RPC methods are consistently slow
- **Statistical Analysis**: Average, min, max, and total times for each method
- **Call Frequency**: Understand which methods are called most often
- **Network Analysis**: Separate network time from processing time
- **Visual Indicators**: Color-coded warnings for slow methods (>100ms = warning, >200ms = danger)
- **Timeout Tracking**: Identify methods with timeout issues

#### Sample Performance Report Features

Based on the reports generated in `etc/results/`, the performance reports include:

- **Bootstrap-styled responsive design** for viewing on any device
- **Chart.js visualizations** for interactive performance analysis
- **Color-coded performance indicators** (green for good, yellow for warning, red for danger)
- **Aggregated RPC analysis** with statistical breakdowns
- **Drill-down capabilities** from overview to detailed step analysis
- **Export-friendly format** for sharing with teams
- **Real-time performance data** captured during test execution

#### Other Report Formats

```bash
# JSON report for CI/CD integration and automated analysis
test_runner.exe -t ./performance-tests -r -f json -o ./results

# JUnit XML for Jenkins/Azure DevOps integration
test_runner.exe -t ./performance-tests -r -f xml -o ./ci-results

# YAML report for human-readable output
test_runner.exe -t ./performance-tests -r -f yaml -o ./results
```

#### Performance Report Analysis Tips

**🔍 Identifying Performance Issues:**
- Look for red bars in step duration charts (failed steps)
- Check test execution timeline for outliers
- Review RPC analysis for timeout patterns
- Compare execution times across similar test runs

**📊 Using Reports for Optimization:**
- Identify slowest steps for optimization focus
- Track performance trends over time
- Validate performance improvements after changes
- Share visual reports with stakeholders

#### Analyzing Performance Reports

**📈 Reading Performance Charts:**

1. **Test Execution Timeline**:
   - Green bars = tests that passed within expected time
   - Red bars = tests that failed or exceeded thresholds
   - Height = execution time (taller = slower)
   - Use to identify which tests are consistently slow

2. **Step Duration Analysis**:
   - Shows individual step performance within tests
   - Color coding helps identify problematic steps
   - Useful for pinpointing optimization opportunities

3. **Test Comparison Chart**:
   - Dual-axis showing time vs step count
   - Helps identify if slowness is due to complexity (more steps) or inefficiency
   - Good for comparing different test approaches

4. **RPC Method Performance Summary** (New!):
   - Shows average performance by RPC method across all calls
   - Blue bars = average request time, Green bars = average network time
   - Ordered by slowest average time first
   - Helps identify which RPC methods need optimization

5. **RPC Method Performance Details Table** (New!):
   - Comprehensive statistics for each RPC method
   - Color-coded rows: Yellow = >100ms average, Red = >200ms average
   - Call count shows usage frequency
   - Min/Max times show performance variability
   - Timeout and retry counts indicate reliability issues

**🔍 Performance Report Best Practices:**

```bash
# Generate baseline report before changes
test_runner.exe -t ./tests -r -f performance -o ./baseline

# Generate comparison report after changes
test_runner.exe -t ./tests -r -f performance -o ./after-changes

# Compare reports to validate improvements
```

**📊 Using Improved RPC Analysis for Optimization:**

**Example: Analyzing IAM Service Performance**

From the sample report, we can see clear optimization opportunities:

```
RPC Method Performance Analysis:
┌─────────────────────────────────────┬───────┬──────────┬─────────┬─────────┐
│ RPC Method                          │ Calls │ Avg (ms) │ Min (ms)│ Max (ms)│
├─────────────────────────────────────┼───────┼──────────┼─────────┼─────────┤
│ ChangeUserPasswordReq               │   6   │  178.17  │    8    │   314   │ ⚠️ SLOW
│ CreateUserReq                       │   5   │   91.2   │    4    │   160   │ ⚠️ SLOW
│ DeleteUserReq                       │   2   │   20.0   │   17    │    23   │ ✅ GOOD
│ ValidateTokenReq                    │  12   │   3.92   │    2    │    22   │ ✅ GOOD
└─────────────────────────────────────┴───────┴──────────┴─────────┴─────────┘
```

**Optimization Insights:**
1. **ChangeUserPasswordReq** (178ms avg) - High variability (8ms-314ms) suggests optimization needed
2. **CreateUserReq** (91ms avg) - Consistently slow, may need database optimization
3. **ValidateTokenReq** (3.92ms avg) - Very fast, well-optimized
4. **High call frequency** - ValidateTokenReq called 12 times, ensure it stays fast

**📊 Using Reports for Continuous Improvement:**

1. **Establish Baselines**: Run performance tests regularly to establish normal performance ranges
2. **Track Trends**: Compare reports over time to identify performance regressions
3. **Identify Patterns**: Look for consistent slow RPC methods across multiple test runs
4. **Prioritize Optimization**: Focus on slow methods with high call counts first
5. **Monitor Variability**: High min/max differences indicate inconsistent performance
6. **Share Results**: Use visual reports to communicate performance status to stakeholders
7. **Set Alerts**: Use JSON reports in CI/CD to automatically fail builds on performance regressions

**Example CI/CD Integration with Performance Reports:**
```yaml
# GitHub Actions example
- name: Run Performance Tests
  run: test_runner.exe -t ./tests/performance -r -f json -o ./results

- name: Analyze Performance Results
  run: |
    # Parse JSON report and check thresholds
    python scripts/check-performance.py ./results/report.json

- name: Upload Performance Report
  uses: actions/upload-artifact@v3
  with:
    name: performance-report
    path: ./results/
```

For detailed information about all output options, see [Output Settings Documentation](output-settings.md).

### CI/CD Integration

Integrate performance testing into your pipeline:

```yaml
# Example CI step
- name: Performance Tests
  run: |
    ./test_runner -t ./tests/performance -r -f json -o ./results
    # Parse results and fail if thresholds exceeded
```

## Troubleshooting Common Issues

### Performance Data Issues

**Zero execution times**: Very fast operations may show 0ms
```yaml
# Use >= 0 instead of > 0
- AssertGte:
    ConstExpr: 0
    JPathExpr: "$curStep:performance.executionTimeMs"
```

**Inconsistent timing**: Network tests have variable timing
```yaml
# Use retry logic for stability
Execution:
  MaxRetries: 5
  RetryDelayMs: 1000
  RetryUntil:
    - ExpectLt:
        ConstExpr: 2000  # More lenient threshold
        JPathExpr: "$curStep:performance.executionTimeMs"
```

### Debugging Performance Tests

```yaml
# Add debug assertions to see actual values
- Name: "Debug Performance"
  Input:
    Method: "GET"
    RequestUri: "https://api.example.com/test"
  Asserters:
    # This will show the actual timing in test output
    - AssertGte:
        ConstExpr: 0
        JPathExpr: "$curStep:performance.executionTimeMs"
        ErrorMessage: "Execution time: {{$curStep:performance.executionTimeMs}}ms"
```

## Next Steps

### 1. Start with the Examples
Run the [performance-testing-examples.yaml](../testrunner/etc/examples/performance-testing-examples.yaml) file to see all concepts in action:

```bash
cd service/testrunner
test_runner.exe -t "etc/examples" -p "*performance-testing-examples.yaml" --trace
```

### 2. Create Your First Performance Test
Start with a simple response time test for your API:

```yaml
Name: "My API Performance Test"
Steps:
  - Name: "API Response Time"
    Input:
      Method: "GET"
      RequestUri: "https://your-api.com/endpoint"
    Asserters:
      - AssertEq:
          ConstExpr: 200
          JPathExpr: "$curStep:$.output.statusCode"
      - AssertLt:
          ConstExpr: 2000  # 2 second threshold
          JPathExpr: "$curStep:performance.executionTimeMs"
          ErrorMessage: "API too slow: {{$curStep:performance.executionTimeMs}}ms"
```

### 3. Expand Your Testing
Gradually add more sophisticated scenarios:
- Baseline and regression testing
- Load testing with DelayMs
- Warm-up and stability testing
- Recovery and resilience testing

### 4. Integrate with CI/CD
Add performance tests to your deployment pipeline to catch regressions early.

## Summary

TestRunner provides comprehensive performance testing capabilities that help you build robust, scalable applications:

### 🚀 **Core Performance Testing Features**
✅ **Response Time Validation** - Ensure APIs meet performance requirements
✅ **Regression Detection** - Catch performance issues before deployment
✅ **Load Testing** - Simulate realistic user scenarios with external data
✅ **System Behavior Testing** - Validate warm-up, stability, and recovery
✅ **Advanced Timing Control** - Use DelayMs for realistic test pacing
✅ **Rich Performance Reports** - Interactive HTML reports with detailed analysis

### 📊 **Advanced Capabilities**
✅ **External Table Loading** - Scale load testing with CSV/JSON/YAML data files
✅ **Scenario-Based Testing** - Realistic user behavior simulation
✅ **Performance Baselines** - Establish and track performance benchmarks
✅ **CI/CD Integration** - Automate performance validation in deployment pipelines
✅ **Visual Analytics** - Interactive charts and performance trend analysis
✅ **RPC Performance Testing** - Specialized metrics for RPC services

### 🎯 **Getting Started Path**

1. **Start Simple**: Begin with basic response time validation
2. **Add Baselines**: Establish performance benchmarks for comparison
3. **Scale Up**: Use external tables for comprehensive load testing
4. **Analyze Results**: Leverage interactive performance reports
5. **Automate**: Integrate with CI/CD for continuous performance validation

### 📚 **Resources**

- **Examples**: [performance-testing-examples.yaml](../testrunner/etc/examples/performance-testing-examples.yaml) - Comprehensive examples of all concepts
- **Table Loading**: [Tables Documentation](tables.md) - Advanced data loading for load testing
- **Output Options**: [Output Settings Documentation](output-settings.md) - Report generation and formats
- **Sample Reports**: Check `etc/results/` for example performance reports

TestRunner transforms performance testing from a complex, specialized task into an integrated part of your development workflow. Start with simple scenarios and gradually build sophisticated performance testing suites that give you confidence in your application's performance.

**Happy performance testing!** 🚀
