2025-06-30T09:00:25.9088453+05:30 [Information] Loading tests from D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc\examples with pattern *performance-testing-examples.yaml
2025-06-30T09:00:25.9223342+05:30 [Information] Loading test from D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc\examples\performance-testing-examples.yaml
2025-06-30T09:00:26.0116368+05:30 [Information] Global variables set to: 
2025-06-30T09:00:26.0127456+05:30 [Information] [==========] Running 1 test from Yaml files
2025-06-30T09:00:26.0144549+05:30 [Information] [------------------------------------- Test Execution -------------------------------------]
2025-06-30T09:00:26.0348288+05:30 [Information] [----------] 17 steps from Performance Testing Examples
2025-06-30T09:00:26.0375818+05:30 [Information] [ RUN      ] Performance Testing Examples > HTTP Response Time Test
2025-06-30T09:00:27.3233154+05:30 [Information] [ OK       ]
2025-06-30T09:00:27.3241391+05:30 [Information] [ RUN      ] Performance Testing Examples > Performance Baseline Establishment
2025-06-30T09:00:28.2569330+05:30 [Information] [ OK       ]
2025-06-30T09:00:28.2578904+05:30 [Information] [ RUN      ] Performance Testing Examples > Performance Regression Check
2025-06-30T09:00:29.0742096+05:30 [Information] [ OK       ]
2025-06-30T09:00:29.0746685+05:30 [Information] [ RUN      ] Performance Testing Examples > Load Testing with Controlled Request Pacing
2025-06-30T09:00:30.8903141+05:30 [Information] [ OK       ]
2025-06-30T09:00:30.8907619+05:30 [Information] [ RUN      ] Performance Testing Examples > Fast Endpoint Baseline
2025-06-30T09:00:31.9675687+05:30 [Information] [ OK       ]
2025-06-30T09:00:31.9691887+05:30 [Information] [ RUN      ] Performance Testing Examples > Complex Endpoint Comparison
2025-06-30T09:00:33.8621863+05:30 [Information] [ OK       ]
2025-06-30T09:00:33.8630149+05:30 [Information] [ RUN      ] Performance Testing Examples > Service Warm-up Performance Test
2025-06-30T09:00:34.6089169+05:30 [Information] [ OK       ]
2025-06-30T09:00:34.6094126+05:30 [Information] [ RUN      ] Performance Testing Examples > Performance Stability Test
2025-06-30T09:00:35.8780093+05:30 [Information] [ OK       ]
2025-06-30T09:00:35.8787979+05:30 [Information] [ RUN      ] Performance Testing Examples > Performance Recovery Test
2025-06-30T09:00:39.6661598+05:30 [Information] [ OK       ]
2025-06-30T09:00:39.6669277+05:30 [Information] [ RUN      ] Performance Testing Examples > Database Connection Simulation Performance Test
2025-06-30T09:00:42.1930574+05:30 [Information] [ OK       ]
2025-06-30T09:00:42.1936761+05:30 [Information] [ RUN      ] Performance Testing Examples > System Information Performance Test
2025-06-30T09:00:45.7386812+05:30 [Information] [ OK       ]
2025-06-30T09:00:45.7390473+05:30 [Information] [ RUN      ] Performance Testing Examples > Directory Listing Performance Test
2025-06-30T09:00:45.7618106+05:30 [Information] [ OK       ]
2025-06-30T09:00:45.7622774+05:30 [Information] [ RUN      ] Performance Testing Examples > Network Service Availability Performance Test
2025-06-30T09:00:47.3503298+05:30 [Information] [ OK       ]
2025-06-30T09:00:47.3509185+05:30 [Information] [ RUN      ] Performance Testing Examples > API Rate Limiting Compliance Test
2025-06-30T09:00:50.4263758+05:30 [Information] [ OK       ]
2025-06-30T09:00:50.4271252+05:30 [Information] [ RUN      ] Performance Testing Examples > Performance Baseline - Fast Operation
2025-06-30T09:00:50.4470197+05:30 [Information] [ OK       ]
2025-06-30T09:00:50.4474244+05:30 [Information] [ RUN      ] Performance Testing Examples > Performance Comparison - Slower Operation
2025-06-30T09:00:51.1432312+05:30 [Information] [ OK       ]
2025-06-30T09:00:51.1446035+05:30 [Information] [ RUN      ] Performance Testing Examples > Process Enumeration Performance Test
2025-06-30T09:00:51.4645082+05:30 [Information] [ OK       ]
2025-06-30T09:00:51.4654699+05:30 [Information] [----------] 17 steps from Performance Testing Examples (25429 ms total)
2025-06-30T09:00:51.4671019+05:30 [Information] [-------------------------------------  Test Results  -------------------------------------]
2025-06-30T09:00:51.4676227+05:30 [Information] [==========] 1 test from Yaml files ran. (25455 ms total)
2025-06-30T09:00:51.4688582+05:30 [Information] [  PASSED  ] 1 test.
