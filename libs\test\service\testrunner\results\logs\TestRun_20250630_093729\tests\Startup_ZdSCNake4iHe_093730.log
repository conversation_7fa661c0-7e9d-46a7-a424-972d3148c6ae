2025-06-30T09:37:30.6098062+05:30 [Information] [----------] 1 step from Startup
2025-06-30T09:37:30.6126398+05:30 [Information] Set Value to Step 'c7b5ab5e-e56d-4b02-ba96-a032e22e17a1' name:
"Initialize Database"
2025-06-30T09:37:30.6129454+05:30 [Information] Set Value to Step 'c7b5ab5e-e56d-4b02-ba96-a032e22e17a1' description:
"Setup initial database state"
2025-06-30T09:37:30.6132002+05:30 [Information] [ RUN      ] Startup > Initialize Database
2025-06-30T09:37:30.6144395+05:30 [Information] Executing single instance of step 'Initialize Database'.
2025-06-30T09:37:30.6156610+05:30 [Information] Executing step 'Initialize Database' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "c7b5ab5e-e56d-4b02-ba96-a032e22e17a1",
        "tables": {},
        "variables": {},
        "name": "Initialize Database",
        "description": "Setup initial database state"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:37:30.6174544+05:30 [Information] Processing $var: path 'baseUrl'
2025-06-30T09:37:30.6190193+05:30 [Information] Checking if 'baseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:30.6206103+05:30 [Information] Resolving path 'baseUrl' using global variables.
2025-06-30T09:37:30.6208380+05:30 [Information] Resolved JPath '$var:baseUrl' to 'https://httpbin.org'
2025-06-30T09:37:30.6209600+05:30 [Information] Resolved template pattern '{{$var:baseUrl}}' to 'https://httpbin.org' in '{{$var:baseUrl}}/post'
2025-06-30T09:37:30.6211929+05:30 [Information] Resolved template '{{$var:baseUrl}}/post' to 'https://httpbin.org/post'
2025-06-30T09:37:30.6227162+05:30 [Information] Set Value to Step 'c7b5ab5e-e56d-4b02-ba96-a032e22e17a1' input:
{
  "Method": "POST",
  "RequestUri": "https://httpbin.org/post",
  "Headers": {
    "Content-Type": "application/json"
  },
  "Content": {
    "action": "initialize_db"
  }
}
2025-06-30T09:37:31.6912425+05:30 [Information] Set Value to Step 'c7b5ab5e-e56d-4b02-ba96-a032e22e17a1' output:
{
  "statusCode": 200,
  "reasonPhrase": "OK",
  "headers": [
    {
      "Key": "Date",
      "Value": [
        "Mon, 30 Jun 2025 04:07:37 GMT"
      ]
    },
    {
      "Key": "Connection",
      "Value": [
        "keep-alive"
      ]
    },
    {
      "Key": "Server",
      "Value": [
        "gunicorn/19.9.0"
      ]
    },
    {
      "Key": "Access-Control-Allow-Origin",
      "Value": [
        "*"
      ]
    },
    {
      "Key": "Access-Control-Allow-Credentials",
      "Value": [
        "true"
      ]
    }
  ],
  "content": {
    "args": {},
    "data": "{\r\n  \"action\": \"initialize_db\"\r\n}",
    "files": {},
    "form": {},
    "headers": {
      "Content-Length": "33",
      "Content-Type": "application/json; charset=utf-8",
      "Host": "httpbin.org",
      "X-Amzn-Trace-Id": "Root=1-68620d89-7cad020465f7462346eef4dd"
    },
    "json": {
      "action": "initialize_db"
    },
    "origin": "**************",
    "url": "https://httpbin.org/post"
  }
}
2025-06-30T09:37:31.6917726+05:30 [Information] Resolving path 'output.content.json.action' using step input/output.
2025-06-30T09:37:31.6919403+05:30 [Information] Resolved JPath 'output.content.json.action' to 'initialize_db'
2025-06-30T09:37:31.6930596+05:30 [Information] Set Value to Step 'c7b5ab5e-e56d-4b02-ba96-a032e22e17a1' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:07:30.6144099Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:37:31.6958332+05:30 [Information] Run Asserter : {"Expression1":{"JPath":"$curStep:output.content.json.action","ErrorMesage":null},"Expression2":{"Value":null,"ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:37:31.6961868+05:30 [Information] Resolving path 'output.content.json.action' using $curStep:
2025-06-30T09:37:31.6963260+05:30 [Information] Resolved JPath '$curStep:output.content.json.action' to 'initialize_db'
2025-06-30T09:37:31.6976023+05:30 [Information] Error in EqualExpression: The method or operation is not implemented.
2025-06-30T09:37:31.6984963+05:30 [Information] Set Value to Step 'c7b5ab5e-e56d-4b02-ba96-a032e22e17a1' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 1074,
  "startTime": "2025-06-30T04:07:30.6144099Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:37:31.6987004+05:30 [Information] Step 'Initialize Database' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "c7b5ab5e-e56d-4b02-ba96-a032e22e17a1",
        "tables": {},
        "variables": {},
        "name": "Initialize Database",
        "description": "Setup initial database state",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "action": "initialize_db"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:07:37 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"action\": \"initialize_db\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "33",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620d89-7cad020465f7462346eef4dd"
            },
            "json": {
              "action": "initialize_db"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 1074,
          "startTime": "2025-06-30T04:07:30.6144099Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:37:31.6988619+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:37:31.6989554+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:37:31.6990282+05:30 [Information] No performance data found for step 'Initialize Database' (http)
2025-06-30T09:37:31.6993366+05:30 [Information] [ OK       ]
2025-06-30T09:37:31.7004228+05:30 [Information] [----------] 1 step from Startup (1089 ms total)
