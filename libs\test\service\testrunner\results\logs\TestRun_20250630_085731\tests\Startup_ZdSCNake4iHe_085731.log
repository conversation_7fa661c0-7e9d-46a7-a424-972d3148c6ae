2025-06-30T08:57:31.8852981+05:30 [Information] [----------] 1 step from Startup
2025-06-30T08:57:31.8875498+05:30 [Information] Set Value to Step '61847d33-f6d9-4f3e-af25-8a048d938043' name:
"Initialize Database"
2025-06-30T08:57:31.8878375+05:30 [Information] Set Value to Step '61847d33-f6d9-4f3e-af25-8a048d938043' description:
"Setup initial database state"
2025-06-30T08:57:31.8881037+05:30 [Information] [ RUN      ] Startup > Initialize Database
2025-06-30T08:57:31.8893871+05:30 [Information] Executing single instance of step 'Initialize Database'.
2025-06-30T08:57:31.8906970+05:30 [Information] Executing step 'Initialize Database' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "61847d33-f6d9-4f3e-af25-8a048d938043",
        "tables": {},
        "variables": {},
        "name": "Initialize Database",
        "description": "Setup initial database state"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T08:57:31.8927659+05:30 [Information] Processing $var: path 'baseUrl'
2025-06-30T08:57:31.8942903+05:30 [Information] Checking if 'baseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T08:57:31.8960825+05:30 [Information] Resolving path 'baseUrl' using global variables.
2025-06-30T08:57:31.8963153+05:30 [Information] Resolved JPath '$var:baseUrl' to 'https://httpbin.org'
2025-06-30T08:57:31.8964224+05:30 [Information] Resolved template pattern '{{$var:baseUrl}}' to 'https://httpbin.org' in '{{$var:baseUrl}}/post'
2025-06-30T08:57:31.8966396+05:30 [Information] Resolved template '{{$var:baseUrl}}/post' to 'https://httpbin.org/post'
2025-06-30T08:57:31.8989041+05:30 [Information] Set Value to Step '61847d33-f6d9-4f3e-af25-8a048d938043' input:
{
  "Method": "POST",
  "RequestUri": "https://httpbin.org/post",
  "Headers": {
    "Content-Type": "application/json"
  },
  "Content": {
    "action": "initialize_db"
  }
}
2025-06-30T08:57:33.0295342+05:30 [Information] Set Value to Step '61847d33-f6d9-4f3e-af25-8a048d938043' output:
{
  "statusCode": 200,
  "reasonPhrase": "OK",
  "headers": [
    {
      "Key": "Date",
      "Value": [
        "Mon, 30 Jun 2025 03:27:38 GMT"
      ]
    },
    {
      "Key": "Connection",
      "Value": [
        "keep-alive"
      ]
    },
    {
      "Key": "Server",
      "Value": [
        "gunicorn/19.9.0"
      ]
    },
    {
      "Key": "Access-Control-Allow-Origin",
      "Value": [
        "*"
      ]
    },
    {
      "Key": "Access-Control-Allow-Credentials",
      "Value": [
        "true"
      ]
    }
  ],
  "content": {
    "args": {},
    "data": "{\r\n  \"action\": \"initialize_db\"\r\n}",
    "files": {},
    "form": {},
    "headers": {
      "Content-Length": "33",
      "Content-Type": "application/json; charset=utf-8",
      "Host": "httpbin.org",
      "X-Amzn-Trace-Id": "Root=1-6862042a-0f1a24c4181d68d8235c91ca"
    },
    "json": {
      "action": "initialize_db"
    },
    "origin": "**************",
    "url": "https://httpbin.org/post"
  }
}
2025-06-30T08:57:33.0300765+05:30 [Information] Resolving path 'output.content.json.action' using step input/output.
2025-06-30T08:57:33.0302916+05:30 [Information] Resolved JPath 'output.content.json.action' to 'initialize_db'
2025-06-30T08:57:33.0313056+05:30 [Information] Set Value to Step '61847d33-f6d9-4f3e-af25-8a048d938043' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T03:27:31.8893563Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T08:57:33.0339399+05:30 [Information] Run Asserter : {"Expression1":{"JPath":"$curStep:output.content.json.action","ErrorMesage":null},"Expression2":{"Value":null,"ErrorMesage":null},"ErrorMesage":null}
2025-06-30T08:57:33.0343833+05:30 [Information] Resolving path 'output.content.json.action' using $curStep:
2025-06-30T08:57:33.0345156+05:30 [Information] Resolved JPath '$curStep:output.content.json.action' to 'initialize_db'
2025-06-30T08:57:33.0360201+05:30 [Information] Error in EqualExpression: The method or operation is not implemented.
2025-06-30T08:57:33.0370432+05:30 [Information] Set Value to Step '61847d33-f6d9-4f3e-af25-8a048d938043' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 1137,
  "startTime": "2025-06-30T03:27:31.8893563Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T08:57:33.0372352+05:30 [Information] Step 'Initialize Database' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "61847d33-f6d9-4f3e-af25-8a048d938043",
        "tables": {},
        "variables": {},
        "name": "Initialize Database",
        "description": "Setup initial database state",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "action": "initialize_db"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 03:27:38 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"action\": \"initialize_db\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "33",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-6862042a-0f1a24c4181d68d8235c91ca"
            },
            "json": {
              "action": "initialize_db"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 1137,
          "startTime": "2025-06-30T03:27:31.8893563Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T08:57:33.0374084+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T08:57:33.0375132+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T08:57:33.0375775+05:30 [Information] No performance data found for step 'Initialize Database' (http)
2025-06-30T08:57:33.0378826+05:30 [Information] [ OK       ]
2025-06-30T08:57:33.0388131+05:30 [Information] [----------] 1 step from Startup (1152 ms total)
