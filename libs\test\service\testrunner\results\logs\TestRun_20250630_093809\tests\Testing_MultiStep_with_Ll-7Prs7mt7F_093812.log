2025-06-30T09:38:12.7591120+05:30 [Information] [----------] 2 steps from Testing MultiStep with JOSNPath
2025-06-30T09:38:12.7597660+05:30 [Information] Set Value to Step '098f6f3c-740f-460f-adcf-1c14c0f87d18' name:
"GET Token"
2025-06-30T09:38:12.7598909+05:30 [Information] Set Value to Step '098f6f3c-740f-460f-adcf-1c14c0f87d18' description:
"Step to get token"
2025-06-30T09:38:12.7599524+05:30 [Information] [ RUN      ] Testing MultiStep with JOSNPath > GET Token
2025-06-30T09:38:12.7602188+05:30 [Information] Executing single instance of step 'GET Token'.
2025-06-30T09:38:12.7603522+05:30 [Information] Executing step 'GET Token' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "098f6f3c-740f-460f-adcf-1c14c0f87d18",
        "tables": {},
        "variables": {},
        "name": "GET Token",
        "description": "Step to get token"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:38:12.7605545+05:30 [Information] Processing $var: path 'baseUrl'
2025-06-30T09:38:12.7606638+05:30 [Information] Checking if 'baseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:12.7607304+05:30 [Information] Resolving path 'baseUrl' using global variables.
2025-06-30T09:38:12.7607841+05:30 [Information] Resolved JPath '$var:baseUrl' to 'https://httpbin.org'
2025-06-30T09:38:12.7608320+05:30 [Information] Resolved template pattern '{{$var:baseUrl}}' to 'https://httpbin.org' in '{{$var:baseUrl}}/post'
2025-06-30T09:38:12.7608841+05:30 [Information] Resolved template '{{$var:baseUrl}}/post' to 'https://httpbin.org/post'
2025-06-30T09:38:12.7610296+05:30 [Information] Set Value to Step '098f6f3c-740f-460f-adcf-1c14c0f87d18' input:
{
  "Method": "POST",
  "RequestUri": "https://httpbin.org/post",
  "Headers": {
    "Content-Type": "application/json"
  },
  "Content": {
    "key": "key_value"
  }
}
2025-06-30T09:38:13.5633124+05:30 [Information] Set Value to Step '098f6f3c-740f-460f-adcf-1c14c0f87d18' output:
{
  "statusCode": 200,
  "reasonPhrase": "OK",
  "headers": [
    {
      "Key": "Date",
      "Value": [
        "Mon, 30 Jun 2025 04:08:19 GMT"
      ]
    },
    {
      "Key": "Connection",
      "Value": [
        "keep-alive"
      ]
    },
    {
      "Key": "Server",
      "Value": [
        "gunicorn/19.9.0"
      ]
    },
    {
      "Key": "Access-Control-Allow-Origin",
      "Value": [
        "*"
      ]
    },
    {
      "Key": "Access-Control-Allow-Credentials",
      "Value": [
        "true"
      ]
    }
  ],
  "content": {
    "args": {},
    "data": "{\r\n  \"key\": \"key_value\"\r\n}",
    "files": {},
    "form": {},
    "headers": {
      "Content-Length": "26",
      "Content-Type": "application/json; charset=utf-8",
      "Host": "httpbin.org",
      "X-Amzn-Trace-Id": "Root=1-68620db2-1bf7ad36131e822351cbc7df"
    },
    "json": {
      "key": "key_value"
    },
    "origin": "**************",
    "url": "https://httpbin.org/post"
  }
}
2025-06-30T09:38:13.5641564+05:30 [Information] Resolving path 'output.content.json.key' using step input/output.
2025-06-30T09:38:13.5646956+05:30 [Information] Resolved JPath 'output.content.json.key' to 'key_value'
2025-06-30T09:38:13.5655165+05:30 [Information] Set Value to Step '098f6f3c-740f-460f-adcf-1c14c0f87d18' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:08:12.7602164Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:38:13.5660189+05:30 [Information] Run Asserter : {"Expression1":{"Value":"200","ErrorMesage":null},"Expression2":{"JPath":"$curStep:$.output.statusCode","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:38:13.5663119+05:30 [Information] Resolving path '$.output.statusCode' using $curStep:
2025-06-30T09:38:13.5666024+05:30 [Information] Resolved JPath '$curStep:$.output.statusCode' to '200'
2025-06-30T09:38:13.5669391+05:30 [Information] Set Value to Step '098f6f3c-740f-460f-adcf-1c14c0f87d18' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 805,
  "startTime": "2025-06-30T04:08:12.7602164Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:38:13.5676223+05:30 [Information] Step 'GET Token' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "098f6f3c-740f-460f-adcf-1c14c0f87d18",
        "tables": {},
        "variables": {},
        "name": "GET Token",
        "description": "Step to get token",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "key": "key_value"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:08:19 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"key\": \"key_value\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "26",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620db2-1bf7ad36131e822351cbc7df"
            },
            "json": {
              "key": "key_value"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 805,
          "startTime": "2025-06-30T04:08:12.7602164Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "token": "key_value"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:38:13.5676859+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:38:13.5677481+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:38:13.5677849+05:30 [Information] No performance data found for step 'GET Token' (http)
2025-06-30T09:38:13.5678197+05:30 [Information] [ OK       ]
2025-06-30T09:38:13.5684884+05:30 [Information] Set Value to Step '1001821d-225a-41ff-a3c6-04d906ffb2e9' name:
"Step to test POST request with token"
2025-06-30T09:38:13.5686105+05:30 [Information] Set Value to Step '1001821d-225a-41ff-a3c6-04d906ffb2e9' description:
"POST request with variable in body"
2025-06-30T09:38:13.5686660+05:30 [Information] [ RUN      ] Testing MultiStep with JOSNPath > Step to test POST request with token
2025-06-30T09:38:13.5689437+05:30 [Information] Executing single instance of step 'Step to test POST request with token'.
2025-06-30T09:38:13.5690897+05:30 [Information] Executing step 'Step to test POST request with token' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "098f6f3c-740f-460f-adcf-1c14c0f87d18",
        "tables": {},
        "variables": {},
        "name": "GET Token",
        "description": "Step to get token",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "key": "key_value"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:08:19 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"key\": \"key_value\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "26",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620db2-1bf7ad36131e822351cbc7df"
            },
            "json": {
              "key": "key_value"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 805,
          "startTime": "2025-06-30T04:08:12.7602164Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      },
      {
        "id": "1001821d-225a-41ff-a3c6-04d906ffb2e9",
        "tables": {},
        "variables": {},
        "name": "Step to test POST request with token",
        "description": "POST request with variable in body"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "token": "key_value"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:38:13.5692056+05:30 [Information] Processing $var: path 'baseUrl'
2025-06-30T09:38:13.5692751+05:30 [Information] Checking if 'baseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:13.5693451+05:30 [Information] Resolving path 'baseUrl' using global variables.
2025-06-30T09:38:13.5693837+05:30 [Information] Resolved JPath '$var:baseUrl' to 'https://httpbin.org'
2025-06-30T09:38:13.5694222+05:30 [Information] Resolved template pattern '{{$var:baseUrl}}' to 'https://httpbin.org' in '{{$var:baseUrl}}/post'
2025-06-30T09:38:13.5694738+05:30 [Information] Resolved template '{{$var:baseUrl}}/post' to 'https://httpbin.org/post'
2025-06-30T09:38:13.5695343+05:30 [Information] Processing $var: path 'token'
2025-06-30T09:38:13.5695786+05:30 [Information] Checking if 'token' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:13.5696193+05:30 [Information] Resolving path 'token' using global variables.
2025-06-30T09:38:13.5696603+05:30 [Information] Resolved JPath '$var:token' to 'key_value'
2025-06-30T09:38:13.5696977+05:30 [Information] Resolved template pattern '{{$var:token}}' to 'key_value' in 'Bearer {{$var:token}}'
2025-06-30T09:38:13.5697372+05:30 [Information] Resolved template 'Bearer {{$var:token}}' to 'Bearer key_value'
2025-06-30T09:38:13.5697958+05:30 [Information] Processing $var: path 'baseUrl'
2025-06-30T09:38:13.5698426+05:30 [Information] Checking if 'baseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:13.5698823+05:30 [Information] Resolving path 'baseUrl' using global variables.
2025-06-30T09:38:13.5699216+05:30 [Information] Resolved JPath '$var:baseUrl' to 'https://httpbin.org'
2025-06-30T09:38:13.5699577+05:30 [Information] Resolved template pattern '{{$var:baseUrl}}' to 'https://httpbin.org' in '{
  "keyFromVar": "{{$var:baseUrl}}",
  "keyFromJPath": "{{$var:token}}"
}'
2025-06-30T09:38:13.5699979+05:30 [Information] Processing $var: path 'token'
2025-06-30T09:38:13.5700379+05:30 [Information] Checking if 'token' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:13.5700764+05:30 [Information] Resolving path 'token' using global variables.
2025-06-30T09:38:13.5701150+05:30 [Information] Resolved JPath '$var:token' to 'key_value'
2025-06-30T09:38:13.5701513+05:30 [Information] Resolved template pattern '{{$var:token}}' to 'key_value' in '{
  "keyFromVar": "{{$var:baseUrl}}",
  "keyFromJPath": "{{$var:token}}"
}'
2025-06-30T09:38:13.5701900+05:30 [Information] Resolved template '{
  "keyFromVar": "{{$var:baseUrl}}",
  "keyFromJPath": "{{$var:token}}"
}' to '{
  "keyFromVar": "https://httpbin.org",
  "keyFromJPath": "key_value"
}'
2025-06-30T09:38:13.5702746+05:30 [Information] Set Value to Step '1001821d-225a-41ff-a3c6-04d906ffb2e9' input:
{
  "Method": "POST",
  "RequestUri": "https://httpbin.org/post",
  "Headers": {
    "Content-Type": "application/json",
    "Authorization": "Bearer key_value"
  },
  "Content": {
    "keyFromVar": "https://httpbin.org",
    "keyFromJPath": "key_value"
  }
}
2025-06-30T09:38:14.3161800+05:30 [Information] Set Value to Step '1001821d-225a-41ff-a3c6-04d906ffb2e9' output:
{
  "statusCode": 200,
  "reasonPhrase": "OK",
  "headers": [
    {
      "Key": "Date",
      "Value": [
        "Mon, 30 Jun 2025 04:08:19 GMT"
      ]
    },
    {
      "Key": "Connection",
      "Value": [
        "keep-alive"
      ]
    },
    {
      "Key": "Server",
      "Value": [
        "gunicorn/19.9.0"
      ]
    },
    {
      "Key": "Access-Control-Allow-Origin",
      "Value": [
        "*"
      ]
    },
    {
      "Key": "Access-Control-Allow-Credentials",
      "Value": [
        "true"
      ]
    }
  ],
  "content": {
    "args": {},
    "data": "{\r\n  \"keyFromVar\": \"https://httpbin.org\",\r\n  \"keyFromJPath\": \"key_value\"\r\n}",
    "files": {},
    "form": {},
    "headers": {
      "Authorization": "Bearer key_value",
      "Content-Length": "75",
      "Content-Type": "application/json; charset=utf-8",
      "Host": "httpbin.org",
      "X-Amzn-Trace-Id": "Root=1-68620db3-19c313f32cc319805634e990"
    },
    "json": {
      "keyFromJPath": "key_value",
      "keyFromVar": "https://httpbin.org"
    },
    "origin": "**************",
    "url": "https://httpbin.org/post"
  }
}
2025-06-30T09:38:14.3165547+05:30 [Information] Set Value to Step '1001821d-225a-41ff-a3c6-04d906ffb2e9' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:08:13.5689416Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:38:14.3167355+05:30 [Information] Run Asserter : {"Expression1":{"Value":"200","ErrorMesage":null},"Expression2":{"JPath":"output.statusCode","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:38:14.3168472+05:30 [Information] Resolving path 'output.statusCode' using step input/output.
2025-06-30T09:38:14.3171059+05:30 [Information] Resolved JPath 'output.statusCode' to '200'
2025-06-30T09:38:14.3173521+05:30 [Information] Set Value to Step '1001821d-225a-41ff-a3c6-04d906ffb2e9' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 746,
  "startTime": "2025-06-30T04:08:13.5689416Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:38:14.3176586+05:30 [Information] Step 'Step to test POST request with token' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "098f6f3c-740f-460f-adcf-1c14c0f87d18",
        "tables": {},
        "variables": {},
        "name": "GET Token",
        "description": "Step to get token",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "key": "key_value"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:08:19 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"key\": \"key_value\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "26",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620db2-1bf7ad36131e822351cbc7df"
            },
            "json": {
              "key": "key_value"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 805,
          "startTime": "2025-06-30T04:08:12.7602164Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      },
      {
        "id": "1001821d-225a-41ff-a3c6-04d906ffb2e9",
        "tables": {},
        "variables": {},
        "name": "Step to test POST request with token",
        "description": "POST request with variable in body",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json",
            "Authorization": "Bearer key_value"
          },
          "Content": {
            "keyFromVar": "https://httpbin.org",
            "keyFromJPath": "key_value"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:08:19 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"keyFromVar\": \"https://httpbin.org\",\r\n  \"keyFromJPath\": \"key_value\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Authorization": "Bearer key_value",
              "Content-Length": "75",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620db3-19c313f32cc319805634e990"
            },
            "json": {
              "keyFromJPath": "key_value",
              "keyFromVar": "https://httpbin.org"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 746,
          "startTime": "2025-06-30T04:08:13.5689416Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "token": "key_value"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:38:14.3178622+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:38:14.3179929+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:38:14.3180789+05:30 [Information] No performance data found for step 'Step to test POST request with token' (http)
2025-06-30T09:38:14.3181592+05:30 [Information] [ OK       ]
2025-06-30T09:38:14.3189216+05:30 [Information] [----------] 2 steps from Testing MultiStep with JOSNPath (1559 ms total)
