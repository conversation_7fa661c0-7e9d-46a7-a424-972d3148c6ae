2025-06-30T09:38:20.9801939+05:30 [Information] [----------] 2 steps from Test with include
2025-06-30T09:38:20.9809604+05:30 [Information] Set Value to Step 'b672cc8c-09b3-4fd7-8aef-254d68fe8002' name:
"GET Token"
2025-06-30T09:38:20.9811149+05:30 [Information] Set Value to Step 'b672cc8c-09b3-4fd7-8aef-254d68fe8002' description:
"Common - Step to get token"
2025-06-30T09:38:20.9811742+05:30 [Information] [ RUN      ] Test with include > GET Token
2025-06-30T09:38:20.9814827+05:30 [Information] Executing single instance of step 'GET Token'.
2025-06-30T09:38:20.9816072+05:30 [Information] Executing step 'GET Token' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "b672cc8c-09b3-4fd7-8aef-254d68fe8002",
        "tables": {},
        "variables": {},
        "name": "GET Token",
        "description": "Common - Step to get token"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:38:20.9818467+05:30 [Information] Processing $var: path 'baseUrl'
2025-06-30T09:38:20.9819177+05:30 [Information] Checking if 'baseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:20.9819684+05:30 [Information] Resolving path 'baseUrl' using global variables.
2025-06-30T09:38:20.9820101+05:30 [Information] Resolved JPath '$var:baseUrl' to 'https://httpbin.org'
2025-06-30T09:38:20.9820519+05:30 [Information] Resolved template pattern '{{$var:baseUrl}}' to 'https://httpbin.org' in '{{$var:baseUrl}}/post'
2025-06-30T09:38:20.9820920+05:30 [Information] Resolved template '{{$var:baseUrl}}/post' to 'https://httpbin.org/post'
2025-06-30T09:38:20.9821853+05:30 [Information] Set Value to Step 'b672cc8c-09b3-4fd7-8aef-254d68fe8002' input:
{
  "Method": "POST",
  "RequestUri": "https://httpbin.org/post",
  "Headers": {
    "Content-Type": "application/json"
  },
  "Content": {
    "key": "key_value"
  }
}
2025-06-30T09:38:21.7554695+05:30 [Information] Set Value to Step 'b672cc8c-09b3-4fd7-8aef-254d68fe8002' output:
{
  "statusCode": 200,
  "reasonPhrase": "OK",
  "headers": [
    {
      "Key": "Date",
      "Value": [
        "Mon, 30 Jun 2025 04:08:27 GMT"
      ]
    },
    {
      "Key": "Connection",
      "Value": [
        "keep-alive"
      ]
    },
    {
      "Key": "Server",
      "Value": [
        "gunicorn/19.9.0"
      ]
    },
    {
      "Key": "Access-Control-Allow-Origin",
      "Value": [
        "*"
      ]
    },
    {
      "Key": "Access-Control-Allow-Credentials",
      "Value": [
        "true"
      ]
    }
  ],
  "content": {
    "args": {},
    "data": "{\r\n  \"key\": \"key_value\"\r\n}",
    "files": {},
    "form": {},
    "headers": {
      "Content-Length": "26",
      "Content-Type": "application/json; charset=utf-8",
      "Host": "httpbin.org",
      "X-Amzn-Trace-Id": "Root=1-68620dbb-2600e80e7cd728d064f9301a"
    },
    "json": {
      "key": "key_value"
    },
    "origin": "**************",
    "url": "https://httpbin.org/post"
  }
}
2025-06-30T09:38:21.7564416+05:30 [Information] Resolving path 'output.content.json.key' using step input/output.
2025-06-30T09:38:21.7572100+05:30 [Information] Resolved JPath 'output.content.json.key' to 'key_value'
2025-06-30T09:38:21.7577349+05:30 [Information] Set Value to Step 'b672cc8c-09b3-4fd7-8aef-254d68fe8002' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:08:20.9814804Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:38:21.7582456+05:30 [Information] Run Asserter : {"Expression1":{"Value":"200","ErrorMesage":null},"Expression2":{"JPath":"output.statusCode","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:38:21.7585167+05:30 [Information] Resolving path 'output.statusCode' using step input/output.
2025-06-30T09:38:21.7587526+05:30 [Information] Resolved JPath 'output.statusCode' to '200'
2025-06-30T09:38:21.7590736+05:30 [Information] Set Value to Step 'b672cc8c-09b3-4fd7-8aef-254d68fe8002' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 776,
  "startTime": "2025-06-30T04:08:20.9814804Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:38:21.7593686+05:30 [Information] Step 'GET Token' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "b672cc8c-09b3-4fd7-8aef-254d68fe8002",
        "tables": {},
        "variables": {},
        "name": "GET Token",
        "description": "Common - Step to get token",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "key": "key_value"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:08:27 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"key\": \"key_value\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "26",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620dbb-2600e80e7cd728d064f9301a"
            },
            "json": {
              "key": "key_value"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 776,
          "startTime": "2025-06-30T04:08:20.9814804Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "token": "key_value"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:38:21.7596256+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:38:21.7598478+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:38:21.7600271+05:30 [Information] No performance data found for step 'GET Token' (http)
2025-06-30T09:38:21.7601763+05:30 [Information] [ OK       ]
2025-06-30T09:38:21.7614700+05:30 [Information] Set Value to Step '0ee87a54-44e5-4f50-a81c-42ac0c63a495' name:
"Step to test POST request with token"
2025-06-30T09:38:21.7618513+05:30 [Information] Set Value to Step '0ee87a54-44e5-4f50-a81c-42ac0c63a495' description:
"POST request with data from included test"
2025-06-30T09:38:21.7623555+05:30 [Information] [ RUN      ] Test with include > Step to test POST request with token
2025-06-30T09:38:21.7644515+05:30 [Information] Executing single instance of step 'Step to test POST request with token'.
2025-06-30T09:38:21.7647273+05:30 [Information] Executing step 'Step to test POST request with token' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "b672cc8c-09b3-4fd7-8aef-254d68fe8002",
        "tables": {},
        "variables": {},
        "name": "GET Token",
        "description": "Common - Step to get token",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "key": "key_value"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:08:27 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"key\": \"key_value\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "26",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620dbb-2600e80e7cd728d064f9301a"
            },
            "json": {
              "key": "key_value"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 776,
          "startTime": "2025-06-30T04:08:20.9814804Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      },
      {
        "id": "0ee87a54-44e5-4f50-a81c-42ac0c63a495",
        "tables": {},
        "variables": {},
        "name": "Step to test POST request with token",
        "description": "POST request with data from included test"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "token": "key_value"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:38:21.7648874+05:30 [Information] Processing $var: path 'baseUrl'
2025-06-30T09:38:21.7649839+05:30 [Information] Checking if 'baseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:21.7650722+05:30 [Information] Resolving path 'baseUrl' using global variables.
2025-06-30T09:38:21.7651567+05:30 [Information] Resolved JPath '$var:baseUrl' to 'https://httpbin.org'
2025-06-30T09:38:21.7652289+05:30 [Information] Resolved template pattern '{{$var:baseUrl}}' to 'https://httpbin.org' in '{{$var:baseUrl}}/post'
2025-06-30T09:38:21.7653025+05:30 [Information] Resolved template '{{$var:baseUrl}}/post' to 'https://httpbin.org/post'
2025-06-30T09:38:21.7653934+05:30 [Information] Processing $var: path 'token'
2025-06-30T09:38:21.7654638+05:30 [Information] Checking if 'token' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:21.7655343+05:30 [Information] Resolving path 'token' using global variables.
2025-06-30T09:38:21.7656066+05:30 [Information] Resolved JPath '$var:token' to 'key_value'
2025-06-30T09:38:21.7656684+05:30 [Information] Resolved template pattern '{{$var:token}}' to 'key_value' in 'Bearer {{$var:token}}'
2025-06-30T09:38:21.7657297+05:30 [Information] Resolved template 'Bearer {{$var:token}}' to 'Bearer key_value'
2025-06-30T09:38:21.7658093+05:30 [Information] Processing $var: path 'baseUrl'
2025-06-30T09:38:21.7658751+05:30 [Information] Checking if 'baseUrl' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:21.7659371+05:30 [Information] Resolving path 'baseUrl' using global variables.
2025-06-30T09:38:21.7659985+05:30 [Information] Resolved JPath '$var:baseUrl' to 'https://httpbin.org'
2025-06-30T09:38:21.7660651+05:30 [Information] Resolved template pattern '{{$var:baseUrl}}' to 'https://httpbin.org' in '{
  "keyFromVar": "{{$var:baseUrl}}",
  "keyFromJPath": "{{$var:token}}"
}'
2025-06-30T09:38:21.7661344+05:30 [Information] Processing $var: path 'token'
2025-06-30T09:38:21.7662132+05:30 [Information] Checking if 'token' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:38:21.7662799+05:30 [Information] Resolving path 'token' using global variables.
2025-06-30T09:38:21.7663420+05:30 [Information] Resolved JPath '$var:token' to 'key_value'
2025-06-30T09:38:21.7663934+05:30 [Information] Resolved template pattern '{{$var:token}}' to 'key_value' in '{
  "keyFromVar": "{{$var:baseUrl}}",
  "keyFromJPath": "{{$var:token}}"
}'
2025-06-30T09:38:21.7664460+05:30 [Information] Resolved template '{
  "keyFromVar": "{{$var:baseUrl}}",
  "keyFromJPath": "{{$var:token}}"
}' to '{
  "keyFromVar": "https://httpbin.org",
  "keyFromJPath": "key_value"
}'
2025-06-30T09:38:21.7665403+05:30 [Information] Set Value to Step '0ee87a54-44e5-4f50-a81c-42ac0c63a495' input:
{
  "Method": "POST",
  "RequestUri": "https://httpbin.org/post",
  "Headers": {
    "Content-Type": "application/json",
    "accept": "application/json",
    "Authorization": "Bearer key_value"
  },
  "Content": {
    "keyFromVar": "https://httpbin.org",
    "keyFromJPath": "key_value"
  }
}
2025-06-30T09:38:22.5362537+05:30 [Information] Set Value to Step '0ee87a54-44e5-4f50-a81c-42ac0c63a495' output:
{
  "statusCode": 200,
  "reasonPhrase": "OK",
  "headers": [
    {
      "Key": "Date",
      "Value": [
        "Mon, 30 Jun 2025 04:08:27 GMT"
      ]
    },
    {
      "Key": "Connection",
      "Value": [
        "keep-alive"
      ]
    },
    {
      "Key": "Server",
      "Value": [
        "gunicorn/19.9.0"
      ]
    },
    {
      "Key": "Access-Control-Allow-Origin",
      "Value": [
        "*"
      ]
    },
    {
      "Key": "Access-Control-Allow-Credentials",
      "Value": [
        "true"
      ]
    }
  ],
  "content": {
    "args": {},
    "data": "{\r\n  \"keyFromVar\": \"https://httpbin.org\",\r\n  \"keyFromJPath\": \"key_value\"\r\n}",
    "files": {},
    "form": {},
    "headers": {
      "Accept": "application/json",
      "Authorization": "Bearer key_value",
      "Content-Length": "75",
      "Content-Type": "application/json; charset=utf-8",
      "Host": "httpbin.org",
      "X-Amzn-Trace-Id": "Root=1-68620dbb-7d17be692451578f1c16bad2"
    },
    "json": {
      "keyFromJPath": "key_value",
      "keyFromVar": "https://httpbin.org"
    },
    "origin": "**************",
    "url": "https://httpbin.org/post"
  }
}
2025-06-30T09:38:22.5375536+05:30 [Information] Set Value to Step '0ee87a54-44e5-4f50-a81c-42ac0c63a495' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:08:21.7644467Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:38:22.5383149+05:30 [Information] Run Asserter : {"Expression1":{"Value":"200","ErrorMesage":null},"Expression2":{"JPath":"output.statusCode","ErrorMesage":null},"ErrorMesage":null}
2025-06-30T09:38:22.5388285+05:30 [Information] Resolving path 'output.statusCode' using step input/output.
2025-06-30T09:38:22.5392234+05:30 [Information] Resolved JPath 'output.statusCode' to '200'
2025-06-30T09:38:22.5397201+05:30 [Information] Set Value to Step '0ee87a54-44e5-4f50-a81c-42ac0c63a495' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 773,
  "startTime": "2025-06-30T04:08:21.7644467Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "http"
}
2025-06-30T09:38:22.5401832+05:30 [Information] Step 'Step to test POST request with token' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "b672cc8c-09b3-4fd7-8aef-254d68fe8002",
        "tables": {},
        "variables": {},
        "name": "GET Token",
        "description": "Common - Step to get token",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json"
          },
          "Content": {
            "key": "key_value"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:08:27 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"key\": \"key_value\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Content-Length": "26",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620dbb-2600e80e7cd728d064f9301a"
            },
            "json": {
              "key": "key_value"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 776,
          "startTime": "2025-06-30T04:08:20.9814804Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      },
      {
        "id": "0ee87a54-44e5-4f50-a81c-42ac0c63a495",
        "tables": {},
        "variables": {},
        "name": "Step to test POST request with token",
        "description": "POST request with data from included test",
        "input": {
          "Method": "POST",
          "RequestUri": "https://httpbin.org/post",
          "Headers": {
            "Content-Type": "application/json",
            "accept": "application/json",
            "Authorization": "Bearer key_value"
          },
          "Content": {
            "keyFromVar": "https://httpbin.org",
            "keyFromJPath": "key_value"
          }
        },
        "output": {
          "statusCode": 200,
          "reasonPhrase": "OK",
          "headers": [
            {
              "Key": "Date",
              "Value": [
                "Mon, 30 Jun 2025 04:08:27 GMT"
              ]
            },
            {
              "Key": "Connection",
              "Value": [
                "keep-alive"
              ]
            },
            {
              "Key": "Server",
              "Value": [
                "gunicorn/19.9.0"
              ]
            },
            {
              "Key": "Access-Control-Allow-Origin",
              "Value": [
                "*"
              ]
            },
            {
              "Key": "Access-Control-Allow-Credentials",
              "Value": [
                "true"
              ]
            }
          ],
          "content": {
            "args": {},
            "data": "{\r\n  \"keyFromVar\": \"https://httpbin.org\",\r\n  \"keyFromJPath\": \"key_value\"\r\n}",
            "files": {},
            "form": {},
            "headers": {
              "Accept": "application/json",
              "Authorization": "Bearer key_value",
              "Content-Length": "75",
              "Content-Type": "application/json; charset=utf-8",
              "Host": "httpbin.org",
              "X-Amzn-Trace-Id": "Root=1-68620dbb-7d17be692451578f1c16bad2"
            },
            "json": {
              "keyFromJPath": "key_value",
              "keyFromVar": "https://httpbin.org"
            },
            "origin": "**************",
            "url": "https://httpbin.org/post"
          }
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 773,
          "startTime": "2025-06-30T04:08:21.7644467Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "http"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "token": "key_value"
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:38:22.5405494+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:38:22.5409956+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:38:22.5410806+05:30 [Information] No performance data found for step 'Step to test POST request with token' (http)
2025-06-30T09:38:22.5411517+05:30 [Information] [ OK       ]
2025-06-30T09:38:22.5416196+05:30 [Information] [----------] 2 steps from Test with include (1560 ms total)
