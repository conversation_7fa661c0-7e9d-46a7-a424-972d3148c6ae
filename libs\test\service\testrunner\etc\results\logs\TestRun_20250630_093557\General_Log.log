2025-06-30T09:35:58.0104945+05:30 [Information] Loading tests from D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc\examples with pattern *performance-testing-examples.yaml
2025-06-30T09:35:58.0236960+05:30 [Information] Loading test from D:\kibisoft\workarea\git\interop\libs\test\service\testrunner\etc\examples\performance-testing-examples.yaml
2025-06-30T09:35:58.1078955+05:30 [Information] Global variables set to: 
2025-06-30T09:35:58.1089609+05:30 [Information] [==========] Running 1 test from Yaml files
2025-06-30T09:35:58.1102501+05:30 [Information] [------------------------------------- Test Execution -------------------------------------]
2025-06-30T09:35:58.1300418+05:30 [Information] [----------] 19 steps from Performance Testing Examples
2025-06-30T09:35:58.1332162+05:30 [Information] [ RUN      ] Performance Testing Examples > HTTP Response Time Test
2025-06-30T09:36:00.0077069+05:30 [Information] [ OK       ]
2025-06-30T09:36:00.0084291+05:30 [Information] [ RUN      ] Performance Testing Examples > Performance Baseline Establishment
2025-06-30T09:36:01.1376712+05:30 [Information] [ OK       ]
2025-06-30T09:36:01.1381782+05:30 [Information] [ RUN      ] Performance Testing Examples > Performance Regression Check
2025-06-30T09:36:01.9649078+05:30 [Information] [ OK       ]
2025-06-30T09:36:01.9656150+05:30 [Information] [ RUN      ] Performance Testing Examples > Load Testing with Controlled Request Pacing
2025-06-30T09:36:03.0520561+05:30 [Information] [ OK       ]
2025-06-30T09:36:03.0524465+05:30 [Information] [ RUN      ] Performance Testing Examples > Fast Endpoint Baseline
2025-06-30T09:36:04.4217018+05:30 [Information] [ OK       ]
2025-06-30T09:36:04.4223118+05:30 [Information] [ RUN      ] Performance Testing Examples > Complex Endpoint Comparison
2025-06-30T09:36:05.6184092+05:30 [Information] [ OK       ]
2025-06-30T09:36:05.6212227+05:30 [Information] [ RUN      ] Performance Testing Examples > Service Warm-up Performance Test
2025-06-30T09:36:06.3999764+05:30 [Information] [ OK       ]
2025-06-30T09:36:06.4010111+05:30 [Information] [ RUN      ] Performance Testing Examples > Performance Stability Test
2025-06-30T09:36:07.7145655+05:30 [Information] [ OK       ]
2025-06-30T09:36:07.7156210+05:30 [Information] [ RUN      ] Performance Testing Examples > Performance Recovery Test
2025-06-30T09:36:12.0045028+05:30 [Information] [ OK       ]
2025-06-30T09:36:12.0053259+05:30 [Information] [ RUN      ] Performance Testing Examples > Database Connection Simulation Performance Test
2025-06-30T09:36:14.1964352+05:30 [Information] [ OK       ]
2025-06-30T09:36:14.1967777+05:30 [Information] [ RUN      ] Performance Testing Examples > System Information Performance Test
2025-06-30T09:36:18.4635856+05:30 [Information] [ OK       ]
2025-06-30T09:36:18.4639691+05:30 [Information] [ RUN      ] Performance Testing Examples > Directory Listing Performance Test
2025-06-30T09:36:18.4877622+05:30 [Information] [ OK       ]
2025-06-30T09:36:18.4882149+05:30 [Information] [ RUN      ] Performance Testing Examples > Network Service Availability Performance Test
2025-06-30T09:36:20.0734050+05:30 [Information] [ OK       ]
2025-06-30T09:36:20.0739984+05:30 [Information] [ RUN      ] Performance Testing Examples > API Rate Limiting Compliance Test
2025-06-30T09:36:22.8686092+05:30 [Information] [ OK       ]
2025-06-30T09:36:22.8693147+05:30 [Information] [ RUN      ] Performance Testing Examples > Performance Baseline - Fast Operation
2025-06-30T09:36:22.8903294+05:30 [Information] [ OK       ]
2025-06-30T09:36:22.8908213+05:30 [Information] [ RUN      ] Performance Testing Examples > Performance Comparison - Slower Operation
2025-06-30T09:36:23.1316585+05:30 [Information] [ OK       ]
2025-06-30T09:36:23.1323630+05:30 [Information] [ RUN      ] Performance Testing Examples > Process Enumeration Performance Test
2025-06-30T09:36:23.4427771+05:30 [Information] [ OK       ]
2025-06-30T09:36:23.4456238+05:30 [Information] [ RUN      ] Performance Testing Examples > Mathematical Expressions Demo
