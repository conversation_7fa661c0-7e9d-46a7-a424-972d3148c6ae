2025-06-30T09:37:11.2963458+05:30 [Information] [----------] 6 steps from Simple Mathematical Expressions Demo - Passing Tests
2025-06-30T09:37:11.2980009+05:30 [Information] Set Value to Step 'f09c2ca4-f087-4d4d-8de0-c26dc6ee0c93' name:
"Fast Baseline Operation"
2025-06-30T09:37:11.2982134+05:30 [Information] [ RUN      ] Simple Mathematical Expressions Demo - Passing Tests > Fast Baseline Operation
2025-06-30T09:37:11.2989095+05:30 [Information] Executing single instance of step 'Fast Baseline Operation'.
2025-06-30T09:37:11.2990498+05:30 [Information] Executing step 'Fast Baseline Operation' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "f09c2ca4-f087-4d4d-8de0-c26dc6ee0c93",
        "tables": {},
        "variables": {},
        "name": "Fast Baseline Operation"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaselineThreshold": 100,
      "MaxRegressionPercent": 1000,
      "BufferTime": 500,
      "FastThreshold": 10000,
      "SlowThreshold": 20000,
      "MathBaseline": 1
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:37:11.3001027+05:30 [Information] Set Value to Step 'f09c2ca4-f087-4d4d-8de0-c26dc6ee0c93' input:
{
  "command": "echo",
  "arguments": [
    "Establishing baseline performance"
  ],
  "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
  "printOutput": false
}
2025-06-30T09:37:11.3002528+05:30 [Information] Delaying step execution for 50ms
2025-06-30T09:37:11.3866485+05:30 [Information] Set Value to Step 'f09c2ca4-f087-4d4d-8de0-c26dc6ee0c93' output:
{
  "stdout": "Establishing baseline performance",
  "stderr": "",
  "exitCode": 0
}
2025-06-30T09:37:11.3868828+05:30 [Information] Resolving path 'performance.executionTimeMs' using global context.
2025-06-30T09:37:11.3869910+05:30 [Information] Resolved JPath 'performance.executionTimeMs' to ''
2025-06-30T09:37:11.3870896+05:30 [Information] Set Value to Step 'f09c2ca4-f087-4d4d-8de0-c26dc6ee0c93' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:07:11.298906Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "cmd"
}
2025-06-30T09:37:11.3878026+05:30 [Information] Run Asserter : {"Expression1":{"Value":"0","ErrorMesage":"Echo command failed"},"Expression2":{"JPath":"$curStep:$.output.exitCode","ErrorMesage":"Echo command failed"},"ErrorMesage":"Echo command failed"}
2025-06-30T09:37:11.3879481+05:30 [Information] Resolving path '$.output.exitCode' using $curStep:
2025-06-30T09:37:11.3880836+05:30 [Information] Resolved JPath '$curStep:$.output.exitCode' to '0'
2025-06-30T09:37:11.3890790+05:30 [Information] Run Asserter : {"_lessExpression":{"Expression1":{"Value":"0","ErrorMesage":"Performance data not available"},"Expression2":{"JPath":"$curStep:performance.executionTimeMs","ErrorMesage":"Performance data not available"},"ErrorMesage":null},"Expression1":{"Value":"0","ErrorMesage":"Performance data not available"},"Expression2":{"JPath":"$curStep:performance.executionTimeMs","ErrorMesage":"Performance data not available"},"ErrorMesage":"Performance data not available"}
2025-06-30T09:37:11.3892065+05:30 [Information] Resolving path 'performance.executionTimeMs' using $curStep:
2025-06-30T09:37:11.3893125+05:30 [Information] Resolved JPath '$curStep:performance.executionTimeMs' to '0'
2025-06-30T09:37:11.3896588+05:30 [Information] Run Asserter : {"_lessExpression":{"Expression1":{"Value":"0","ErrorMesage":"Performance timing should be non-negative"},"Expression2":{"JPath":"$curStep:performance.executionTimeMs","ErrorMesage":"Performance timing should be non-negative"},"ErrorMesage":null},"Expression1":{"Value":"0","ErrorMesage":"Performance timing should be non-negative"},"Expression2":{"JPath":"$curStep:performance.executionTimeMs","ErrorMesage":"Performance timing should be non-negative"},"ErrorMesage":"Performance timing should be non-negative"}
2025-06-30T09:37:11.3897752+05:30 [Information] Resolving path 'performance.executionTimeMs' using $curStep:
2025-06-30T09:37:11.3898538+05:30 [Information] Resolved JPath '$curStep:performance.executionTimeMs' to '0'
2025-06-30T09:37:11.3899466+05:30 [Information] Run Asserter : {"Expression1":{"JPath":"$curStep:performance.executionTimeMs","ErrorMesage":"Baseline too slow: {{$curStep:performance.executionTimeMs}}ms > {{$var:FastThreshold}}ms (calculated threshold)"},"Expression2":{"Value":"{{$var:FastThreshold}}","ErrorMesage":"Baseline too slow: {{$curStep:performance.executionTimeMs}}ms > {{$var:FastThreshold}}ms (calculated threshold)"},"ErrorMesage":"Baseline too slow: {{$curStep:performance.executionTimeMs}}ms > {{$var:FastThreshold}}ms (calculated threshold)"}
2025-06-30T09:37:11.3900131+05:30 [Information] Resolving path 'performance.executionTimeMs' using $curStep:
2025-06-30T09:37:11.3900700+05:30 [Information] Resolved JPath '$curStep:performance.executionTimeMs' to '0'
2025-06-30T09:37:11.3901361+05:30 [Information] Processing $var: path 'FastThreshold'
2025-06-30T09:37:11.3901950+05:30 [Information] Checking if 'FastThreshold' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:11.3902471+05:30 [Information] Resolving path 'FastThreshold' using global variables.
2025-06-30T09:37:11.3902986+05:30 [Information] Resolved JPath '$var:FastThreshold' to '10000'
2025-06-30T09:37:11.3903433+05:30 [Information] Resolved template pattern '{{$var:FastThreshold}}' to '10000' in '{{$var:FastThreshold}}'
2025-06-30T09:37:11.3904995+05:30 [Information] Resolved template '{{$var:FastThreshold}}' to '10000'
2025-06-30T09:37:11.3906285+05:30 [Information] Set Value to Step 'f09c2ca4-f087-4d4d-8de0-c26dc6ee0c93' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 90,
  "startTime": "2025-06-30T04:07:11.298906Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "cmd"
}
2025-06-30T09:37:11.3907389+05:30 [Information] Step 'Fast Baseline Operation' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "f09c2ca4-f087-4d4d-8de0-c26dc6ee0c93",
        "tables": {},
        "variables": {},
        "name": "Fast Baseline Operation",
        "input": {
          "command": "echo",
          "arguments": [
            "Establishing baseline performance"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Establishing baseline performance",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 90,
          "startTime": "2025-06-30T04:07:11.298906Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaselineThreshold": 100,
      "MaxRegressionPercent": 1000,
      "BufferTime": 500,
      "FastThreshold": 10000,
      "SlowThreshold": 20000,
      "MathBaseline": 1
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:37:11.3908228+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:37:11.3908840+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:37:11.3909328+05:30 [Information] No performance data found for step 'Fast Baseline Operation' (cmd)
2025-06-30T09:37:11.3909952+05:30 [Information] [ OK       ]
2025-06-30T09:37:11.3912944+05:30 [Information] Set Value to Step 'ad7331ee-d24e-45ad-b5a9-5cfffa1529e0' name:
"Regression Threshold Test"
2025-06-30T09:37:11.3913825+05:30 [Information] [ RUN      ] Simple Mathematical Expressions Demo - Passing Tests > Regression Threshold Test
2025-06-30T09:37:11.3917720+05:30 [Information] Executing single instance of step 'Regression Threshold Test'.
2025-06-30T09:37:11.3919179+05:30 [Information] Executing step 'Regression Threshold Test' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "f09c2ca4-f087-4d4d-8de0-c26dc6ee0c93",
        "tables": {},
        "variables": {},
        "name": "Fast Baseline Operation",
        "input": {
          "command": "echo",
          "arguments": [
            "Establishing baseline performance"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Establishing baseline performance",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 90,
          "startTime": "2025-06-30T04:07:11.298906Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "ad7331ee-d24e-45ad-b5a9-5cfffa1529e0",
        "tables": {},
        "variables": {},
        "name": "Regression Threshold Test"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaselineThreshold": 100,
      "MaxRegressionPercent": 1000,
      "BufferTime": 500,
      "FastThreshold": 10000,
      "SlowThreshold": 20000,
      "MathBaseline": 1
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:37:11.3920527+05:30 [Information] Set Value to Step 'ad7331ee-d24e-45ad-b5a9-5cfffa1529e0' input:
{
  "command": "echo",
  "arguments": [
    "Testing mathematical expression: MathBaseline * MaxRegressionPercent / 100"
  ],
  "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
  "printOutput": false
}
2025-06-30T09:37:11.3921333+05:30 [Information] Delaying step execution for 200ms
2025-06-30T09:37:11.6202775+05:30 [Information] Set Value to Step 'ad7331ee-d24e-45ad-b5a9-5cfffa1529e0' output:
{
  "stdout": "Testing mathematical expression: MathBaseline * MaxRegressionPercent / 100",
  "stderr": "",
  "exitCode": 0
}
2025-06-30T09:37:11.6205073+05:30 [Information] Set Value to Step 'ad7331ee-d24e-45ad-b5a9-5cfffa1529e0' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:07:11.3917689Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "cmd"
}
2025-06-30T09:37:11.6206440+05:30 [Information] Run Asserter : {"Expression1":{"Value":"0","ErrorMesage":"Echo command failed"},"Expression2":{"JPath":"$curStep:$.output.exitCode","ErrorMesage":"Echo command failed"},"ErrorMesage":"Echo command failed"}
2025-06-30T09:37:11.6207401+05:30 [Information] Resolving path '$.output.exitCode' using $curStep:
2025-06-30T09:37:11.6208226+05:30 [Information] Resolved JPath '$curStep:$.output.exitCode' to '0'
2025-06-30T09:37:11.6209096+05:30 [Information] Run Asserter : {"Expression1":{"JPath":"$curStep:performance.executionTimeMs","ErrorMesage":"Performance regression detected: {{$curStep:performance.executionTimeMs}}ms vs allowed {{$var:MathBaseline * $var:MaxRegressionPercent / 100}}ms ({{$var:MathBaseline}}ms * {{$var:MaxRegressionPercent}}% / 100)"},"Expression2":{"Value":"{{$var:MathBaseline * $var:MaxRegressionPercent / 100}}","ErrorMesage":"Performance regression detected: {{$curStep:performance.executionTimeMs}}ms vs allowed {{$var:MathBaseline * $var:MaxRegressionPercent / 100}}ms ({{$var:MathBaseline}}ms * {{$var:MaxRegressionPercent}}% / 100)"},"ErrorMesage":"Performance regression detected: {{$curStep:performance.executionTimeMs}}ms vs allowed {{$var:MathBaseline * $var:MaxRegressionPercent / 100}}ms ({{$var:MathBaseline}}ms * {{$var:MaxRegressionPercent}}% / 100)"}
2025-06-30T09:37:11.6209722+05:30 [Information] Resolving path 'performance.executionTimeMs' using $curStep:
2025-06-30T09:37:11.6210703+05:30 [Information] Resolved JPath '$curStep:performance.executionTimeMs' to '0'
2025-06-30T09:37:11.6211569+05:30 [Information] Processing $var: path 'MathBaseline * $var:MaxRegressionPercent / 100'
2025-06-30T09:37:11.6212370+05:30 [Information] Checking if 'MathBaseline * $var:MaxRegressionPercent / 100' is mathematical expression: hasArithmetic=True, hasArray=False, hasProperty=False, result=True
2025-06-30T09:37:11.6212887+05:30 [Information] Detected mathematical expression in $var: 'MathBaseline * $var:MaxRegressionPercent / 100'
2025-06-30T09:37:11.6215762+05:30 [Information] Evaluating mathematical expression: 'MathBaseline * $var:MaxRegressionPercent / 100'
2025-06-30T09:37:11.6218351+05:30 [Information] Resolving variables in expression: 'MathBaseline * $var:MaxRegressionPercent / 100'
2025-06-30T09:37:11.6222199+05:30 [Information] Resolved variable 'MaxRegressionPercent' from global variables: 1000
2025-06-30T09:37:11.6225384+05:30 [Information] Resolved variable 'MathBaseline' from global variables: 1
2025-06-30T09:37:11.6226570+05:30 [Information] Expression after variable resolution: '1 * 1000 / 100'
2025-06-30T09:37:11.6227238+05:30 [Information] Expression after variable resolution: '1 * 1000 / 100'
2025-06-30T09:37:11.6260434+05:30 [Information] Mathematical expression 'MathBaseline * $var:MaxRegressionPercent / 100' evaluated to: 10
2025-06-30T09:37:11.6262494+05:30 [Information] Mathematical expression result: '10'
2025-06-30T09:37:11.6263757+05:30 [Information] Resolved JPath '$var:MathBaseline * $var:MaxRegressionPercent / 100' to '10'
2025-06-30T09:37:11.6264740+05:30 [Information] Resolved template pattern '{{$var:MathBaseline * $var:MaxRegressionPercent / 100}}' to '10' in '{{$var:MathBaseline * $var:MaxRegressionPercent / 100}}'
2025-06-30T09:37:11.6267766+05:30 [Information] Resolved template '{{$var:MathBaseline * $var:MaxRegressionPercent / 100}}' to '10'
2025-06-30T09:37:11.6269666+05:30 [Information] Set Value to Step 'ad7331ee-d24e-45ad-b5a9-5cfffa1529e0' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 234,
  "startTime": "2025-06-30T04:07:11.3917689Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "cmd"
}
2025-06-30T09:37:11.6271665+05:30 [Information] Step 'Regression Threshold Test' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "f09c2ca4-f087-4d4d-8de0-c26dc6ee0c93",
        "tables": {},
        "variables": {},
        "name": "Fast Baseline Operation",
        "input": {
          "command": "echo",
          "arguments": [
            "Establishing baseline performance"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Establishing baseline performance",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 90,
          "startTime": "2025-06-30T04:07:11.298906Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "ad7331ee-d24e-45ad-b5a9-5cfffa1529e0",
        "tables": {},
        "variables": {},
        "name": "Regression Threshold Test",
        "input": {
          "command": "echo",
          "arguments": [
            "Testing mathematical expression: MathBaseline * MaxRegressionPercent / 100"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Testing mathematical expression: MathBaseline * MaxRegressionPercent / 100",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 234,
          "startTime": "2025-06-30T04:07:11.3917689Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaselineThreshold": 100,
      "MaxRegressionPercent": 1000,
      "BufferTime": 500,
      "FastThreshold": 10000,
      "SlowThreshold": 20000,
      "MathBaseline": 1
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:37:11.6272468+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:37:11.6273203+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:37:11.6273946+05:30 [Information] No performance data found for step 'Regression Threshold Test' (cmd)
2025-06-30T09:37:11.6275367+05:30 [Information] [ OK       ]
2025-06-30T09:37:11.6278978+05:30 [Information] Set Value to Step '54584d1d-0595-4471-961c-26601edd84c2' name:
"Complex Expression Test"
2025-06-30T09:37:11.6279908+05:30 [Information] [ RUN      ] Simple Mathematical Expressions Demo - Passing Tests > Complex Expression Test
2025-06-30T09:37:11.6282907+05:30 [Information] Executing single instance of step 'Complex Expression Test'.
2025-06-30T09:37:11.6285751+05:30 [Information] Executing step 'Complex Expression Test' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "f09c2ca4-f087-4d4d-8de0-c26dc6ee0c93",
        "tables": {},
        "variables": {},
        "name": "Fast Baseline Operation",
        "input": {
          "command": "echo",
          "arguments": [
            "Establishing baseline performance"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Establishing baseline performance",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 90,
          "startTime": "2025-06-30T04:07:11.298906Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "ad7331ee-d24e-45ad-b5a9-5cfffa1529e0",
        "tables": {},
        "variables": {},
        "name": "Regression Threshold Test",
        "input": {
          "command": "echo",
          "arguments": [
            "Testing mathematical expression: MathBaseline * MaxRegressionPercent / 100"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Testing mathematical expression: MathBaseline * MaxRegressionPercent / 100",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 234,
          "startTime": "2025-06-30T04:07:11.3917689Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "54584d1d-0595-4471-961c-26601edd84c2",
        "tables": {},
        "variables": {},
        "name": "Complex Expression Test"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaselineThreshold": 100,
      "MaxRegressionPercent": 1000,
      "BufferTime": 500,
      "FastThreshold": 10000,
      "SlowThreshold": 20000,
      "MathBaseline": 1
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:37:11.6287602+05:30 [Information] Set Value to Step '54584d1d-0595-4471-961c-26601edd84c2' input:
{
  "command": "echo",
  "arguments": [
    "Testing complex expression: MathBaseline * MaxRegressionPercent / 100 + BufferTime"
  ],
  "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
  "printOutput": false
}
2025-06-30T09:37:11.6288700+05:30 [Information] Delaying step execution for 150ms
2025-06-30T09:37:11.8099076+05:30 [Information] Set Value to Step '54584d1d-0595-4471-961c-26601edd84c2' output:
{
  "stdout": "Testing complex expression: MathBaseline * MaxRegressionPercent / 100 + BufferTime",
  "stderr": "",
  "exitCode": 0
}
2025-06-30T09:37:11.8100652+05:30 [Information] Set Value to Step '54584d1d-0595-4471-961c-26601edd84c2' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:07:11.6282873Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "cmd"
}
2025-06-30T09:37:11.8101858+05:30 [Information] Run Asserter : {"Expression1":{"Value":"0","ErrorMesage":"Echo command failed"},"Expression2":{"JPath":"$curStep:$.output.exitCode","ErrorMesage":"Echo command failed"},"ErrorMesage":"Echo command failed"}
2025-06-30T09:37:11.8102844+05:30 [Information] Resolving path '$.output.exitCode' using $curStep:
2025-06-30T09:37:11.8103895+05:30 [Information] Resolved JPath '$curStep:$.output.exitCode' to '0'
2025-06-30T09:37:11.8104739+05:30 [Information] Run Asserter : {"Expression1":{"JPath":"$curStep:performance.executionTimeMs","ErrorMesage":"Complex threshold exceeded: {{$curStep:performance.executionTimeMs}}ms vs {{$var:MathBaseline * $var:MaxRegressionPercent / 100 + $var:BufferTime}}ms ({{$var:MathBaseline}} * {{$var:MaxRegressionPercent}}% / 100 + {{$var:BufferTime}})"},"Expression2":{"Value":"{{$var:MathBaseline * $var:MaxRegressionPercent / 100 + $var:BufferTime}}","ErrorMesage":"Complex threshold exceeded: {{$curStep:performance.executionTimeMs}}ms vs {{$var:MathBaseline * $var:MaxRegressionPercent / 100 + $var:BufferTime}}ms ({{$var:MathBaseline}} * {{$var:MaxRegressionPercent}}% / 100 + {{$var:BufferTime}})"},"ErrorMesage":"Complex threshold exceeded: {{$curStep:performance.executionTimeMs}}ms vs {{$var:MathBaseline * $var:MaxRegressionPercent / 100 + $var:BufferTime}}ms ({{$var:MathBaseline}} * {{$var:MaxRegressionPercent}}% / 100 + {{$var:BufferTime}})"}
2025-06-30T09:37:11.8105326+05:30 [Information] Resolving path 'performance.executionTimeMs' using $curStep:
2025-06-30T09:37:11.8106060+05:30 [Information] Resolved JPath '$curStep:performance.executionTimeMs' to '0'
2025-06-30T09:37:11.8106655+05:30 [Information] Processing $var: path 'MathBaseline * $var:MaxRegressionPercent / 100 + $var:BufferTime'
2025-06-30T09:37:11.8107228+05:30 [Information] Checking if 'MathBaseline * $var:MaxRegressionPercent / 100 + $var:BufferTime' is mathematical expression: hasArithmetic=True, hasArray=False, hasProperty=False, result=True
2025-06-30T09:37:11.8107647+05:30 [Information] Detected mathematical expression in $var: 'MathBaseline * $var:MaxRegressionPercent / 100 + $var:BufferTime'
2025-06-30T09:37:11.8108042+05:30 [Information] Evaluating mathematical expression: 'MathBaseline * $var:MaxRegressionPercent / 100 + $var:BufferTime'
2025-06-30T09:37:11.8108429+05:30 [Information] Resolving variables in expression: 'MathBaseline * $var:MaxRegressionPercent / 100 + $var:BufferTime'
2025-06-30T09:37:11.8108980+05:30 [Information] Resolved variable 'MaxRegressionPercent' from global variables: 1000
2025-06-30T09:37:11.8109553+05:30 [Information] Resolved variable 'BufferTime' from global variables: 500
2025-06-30T09:37:11.8110153+05:30 [Information] Resolved variable 'MathBaseline' from global variables: 1
2025-06-30T09:37:11.8111091+05:30 [Information] Expression after variable resolution: '1 * 1000 / 100 + 500'
2025-06-30T09:37:11.8111801+05:30 [Information] Expression after variable resolution: '1 * 1000 / 100 + 500'
2025-06-30T09:37:11.8112568+05:30 [Information] Mathematical expression 'MathBaseline * $var:MaxRegressionPercent / 100 + $var:BufferTime' evaluated to: 510
2025-06-30T09:37:11.8113060+05:30 [Information] Mathematical expression result: '510'
2025-06-30T09:37:11.8113684+05:30 [Information] Resolved JPath '$var:MathBaseline * $var:MaxRegressionPercent / 100 + $var:BufferTime' to '510'
2025-06-30T09:37:11.8114166+05:30 [Information] Resolved template pattern '{{$var:MathBaseline * $var:MaxRegressionPercent / 100 + $var:BufferTime}}' to '510' in '{{$var:MathBaseline * $var:MaxRegressionPercent / 100 + $var:BufferTime}}'
2025-06-30T09:37:11.8114603+05:30 [Information] Resolved template '{{$var:MathBaseline * $var:MaxRegressionPercent / 100 + $var:BufferTime}}' to '510'
2025-06-30T09:37:11.8115408+05:30 [Information] Set Value to Step '54584d1d-0595-4471-961c-26601edd84c2' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 182,
  "startTime": "2025-06-30T04:07:11.6282873Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "cmd"
}
2025-06-30T09:37:11.8116601+05:30 [Information] Step 'Complex Expression Test' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "f09c2ca4-f087-4d4d-8de0-c26dc6ee0c93",
        "tables": {},
        "variables": {},
        "name": "Fast Baseline Operation",
        "input": {
          "command": "echo",
          "arguments": [
            "Establishing baseline performance"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Establishing baseline performance",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 90,
          "startTime": "2025-06-30T04:07:11.298906Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "ad7331ee-d24e-45ad-b5a9-5cfffa1529e0",
        "tables": {},
        "variables": {},
        "name": "Regression Threshold Test",
        "input": {
          "command": "echo",
          "arguments": [
            "Testing mathematical expression: MathBaseline * MaxRegressionPercent / 100"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Testing mathematical expression: MathBaseline * MaxRegressionPercent / 100",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 234,
          "startTime": "2025-06-30T04:07:11.3917689Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "54584d1d-0595-4471-961c-26601edd84c2",
        "tables": {},
        "variables": {},
        "name": "Complex Expression Test",
        "input": {
          "command": "echo",
          "arguments": [
            "Testing complex expression: MathBaseline * MaxRegressionPercent / 100 + BufferTime"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Testing complex expression: MathBaseline * MaxRegressionPercent / 100 + BufferTime",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 182,
          "startTime": "2025-06-30T04:07:11.6282873Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaselineThreshold": 100,
      "MaxRegressionPercent": 1000,
      "BufferTime": 500,
      "FastThreshold": 10000,
      "SlowThreshold": 20000,
      "MathBaseline": 1
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:37:11.8117372+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:37:11.8117886+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:37:11.8118728+05:30 [Information] No performance data found for step 'Complex Expression Test' (cmd)
2025-06-30T09:37:11.8119174+05:30 [Information] [ OK       ]
2025-06-30T09:37:11.8121784+05:30 [Information] Set Value to Step 'fdbfef01-e64d-4307-97f6-9a8a38b16b28' name:
"Operator Precedence Test"
2025-06-30T09:37:11.8122437+05:30 [Information] [ RUN      ] Simple Mathematical Expressions Demo - Passing Tests > Operator Precedence Test
2025-06-30T09:37:11.8124721+05:30 [Information] Executing single instance of step 'Operator Precedence Test'.
2025-06-30T09:37:11.8126628+05:30 [Information] Executing step 'Operator Precedence Test' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "f09c2ca4-f087-4d4d-8de0-c26dc6ee0c93",
        "tables": {},
        "variables": {},
        "name": "Fast Baseline Operation",
        "input": {
          "command": "echo",
          "arguments": [
            "Establishing baseline performance"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Establishing baseline performance",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 90,
          "startTime": "2025-06-30T04:07:11.298906Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "ad7331ee-d24e-45ad-b5a9-5cfffa1529e0",
        "tables": {},
        "variables": {},
        "name": "Regression Threshold Test",
        "input": {
          "command": "echo",
          "arguments": [
            "Testing mathematical expression: MathBaseline * MaxRegressionPercent / 100"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Testing mathematical expression: MathBaseline * MaxRegressionPercent / 100",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 234,
          "startTime": "2025-06-30T04:07:11.3917689Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "54584d1d-0595-4471-961c-26601edd84c2",
        "tables": {},
        "variables": {},
        "name": "Complex Expression Test",
        "input": {
          "command": "echo",
          "arguments": [
            "Testing complex expression: MathBaseline * MaxRegressionPercent / 100 + BufferTime"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Testing complex expression: MathBaseline * MaxRegressionPercent / 100 + BufferTime",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 182,
          "startTime": "2025-06-30T04:07:11.6282873Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "fdbfef01-e64d-4307-97f6-9a8a38b16b28",
        "tables": {},
        "variables": {},
        "name": "Operator Precedence Test"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaselineThreshold": 100,
      "MaxRegressionPercent": 1000,
      "BufferTime": 500,
      "FastThreshold": 10000,
      "SlowThreshold": 20000,
      "MathBaseline": 1
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:37:11.8128233+05:30 [Information] Set Value to Step 'fdbfef01-e64d-4307-97f6-9a8a38b16b28' input:
{
  "command": "echo",
  "arguments": [
    "Testing operator precedence"
  ],
  "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
  "printOutput": false
}
2025-06-30T09:37:11.8128969+05:30 [Information] Delaying step execution for 75ms
2025-06-30T09:37:11.9045390+05:30 [Information] Set Value to Step 'fdbfef01-e64d-4307-97f6-9a8a38b16b28' output:
{
  "stdout": "Testing operator precedence",
  "stderr": "",
  "exitCode": 0
}
2025-06-30T09:37:11.9049686+05:30 [Information] Set Value to Step 'fdbfef01-e64d-4307-97f6-9a8a38b16b28' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:07:11.8124695Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "cmd"
}
2025-06-30T09:37:11.9051500+05:30 [Information] Run Asserter : {"Expression1":{"Value":"0","ErrorMesage":"Echo command failed"},"Expression2":{"JPath":"$curStep:$.output.exitCode","ErrorMesage":"Echo command failed"},"ErrorMesage":"Echo command failed"}
2025-06-30T09:37:11.9052414+05:30 [Information] Resolving path '$.output.exitCode' using $curStep:
2025-06-30T09:37:11.9053132+05:30 [Information] Resolved JPath '$curStep:$.output.exitCode' to '0'
2025-06-30T09:37:11.9053975+05:30 [Information] Run Asserter : {"Expression1":{"JPath":"$curStep:performance.executionTimeMs","ErrorMesage":"Parentheses test failed: {{$curStep:performance.executionTimeMs}}ms vs {{$var:(MathBaseline + $var:BufferTime) * 2}}ms = ({{$var:MathBaseline}} + {{$var:BufferTime}}) * 2"},"Expression2":{"Value":"{{$var:(MathBaseline + $var:BufferTime) * 2}}","ErrorMesage":"Parentheses test failed: {{$curStep:performance.executionTimeMs}}ms vs {{$var:(MathBaseline + $var:BufferTime) * 2}}ms = ({{$var:MathBaseline}} + {{$var:BufferTime}}) * 2"},"ErrorMesage":"Parentheses test failed: {{$curStep:performance.executionTimeMs}}ms vs {{$var:(MathBaseline + $var:BufferTime) * 2}}ms = ({{$var:MathBaseline}} + {{$var:BufferTime}}) * 2"}
2025-06-30T09:37:11.9054786+05:30 [Information] Resolving path 'performance.executionTimeMs' using $curStep:
2025-06-30T09:37:11.9055378+05:30 [Information] Resolved JPath '$curStep:performance.executionTimeMs' to '0'
2025-06-30T09:37:11.9056140+05:30 [Information] Processing $var: path '(MathBaseline + $var:BufferTime) * 2'
2025-06-30T09:37:11.9056664+05:30 [Information] Checking if '(MathBaseline + $var:BufferTime) * 2' is mathematical expression: hasArithmetic=True, hasArray=False, hasProperty=False, result=True
2025-06-30T09:37:11.9057051+05:30 [Information] Detected mathematical expression in $var: '(MathBaseline + $var:BufferTime) * 2'
2025-06-30T09:37:11.9057446+05:30 [Information] Evaluating mathematical expression: '(MathBaseline + $var:BufferTime) * 2'
2025-06-30T09:37:11.9058119+05:30 [Information] Resolving variables in expression: '(MathBaseline + $var:BufferTime) * 2'
2025-06-30T09:37:11.9059324+05:30 [Information] Resolved variable 'BufferTime' from global variables: 500
2025-06-30T09:37:11.9060194+05:30 [Information] Resolved variable 'MathBaseline' from global variables: 1
2025-06-30T09:37:11.9060716+05:30 [Information] Expression after variable resolution: '(1 + 500) * 2'
2025-06-30T09:37:11.9061167+05:30 [Information] Expression after variable resolution: '(1 + 500) * 2'
2025-06-30T09:37:11.9061719+05:30 [Information] Mathematical expression '(MathBaseline + $var:BufferTime) * 2' evaluated to: 1002
2025-06-30T09:37:11.9062135+05:30 [Information] Mathematical expression result: '1002'
2025-06-30T09:37:11.9062682+05:30 [Information] Resolved JPath '$var:(MathBaseline + $var:BufferTime) * 2' to '1002'
2025-06-30T09:37:11.9063291+05:30 [Information] Resolved template pattern '{{$var:(MathBaseline + $var:BufferTime) * 2}}' to '1002' in '{{$var:(MathBaseline + $var:BufferTime) * 2}}'
2025-06-30T09:37:11.9064009+05:30 [Information] Resolved template '{{$var:(MathBaseline + $var:BufferTime) * 2}}' to '1002'
2025-06-30T09:37:11.9065240+05:30 [Information] Run Asserter : {"Expression1":{"JPath":"$curStep:performance.executionTimeMs","ErrorMesage":"Precedence test failed: {{$curStep:performance.executionTimeMs}}ms vs {{$var:MathBaseline + $var:BufferTime * 2}}ms = {{$var:MathBaseline}} + {{$var:BufferTime}} * 2"},"Expression2":{"Value":"{{$var:MathBaseline + $var:BufferTime * 2}}","ErrorMesage":"Precedence test failed: {{$curStep:performance.executionTimeMs}}ms vs {{$var:MathBaseline + $var:BufferTime * 2}}ms = {{$var:MathBaseline}} + {{$var:BufferTime}} * 2"},"ErrorMesage":"Precedence test failed: {{$curStep:performance.executionTimeMs}}ms vs {{$var:MathBaseline + $var:BufferTime * 2}}ms = {{$var:MathBaseline}} + {{$var:BufferTime}} * 2"}
2025-06-30T09:37:11.9065840+05:30 [Information] Resolving path 'performance.executionTimeMs' using $curStep:
2025-06-30T09:37:11.9066438+05:30 [Information] Resolved JPath '$curStep:performance.executionTimeMs' to '0'
2025-06-30T09:37:11.9066973+05:30 [Information] Processing $var: path 'MathBaseline + $var:BufferTime * 2'
2025-06-30T09:37:11.9067447+05:30 [Information] Checking if 'MathBaseline + $var:BufferTime * 2' is mathematical expression: hasArithmetic=True, hasArray=False, hasProperty=False, result=True
2025-06-30T09:37:11.9067836+05:30 [Information] Detected mathematical expression in $var: 'MathBaseline + $var:BufferTime * 2'
2025-06-30T09:37:11.9068228+05:30 [Information] Evaluating mathematical expression: 'MathBaseline + $var:BufferTime * 2'
2025-06-30T09:37:11.9068658+05:30 [Information] Resolving variables in expression: 'MathBaseline + $var:BufferTime * 2'
2025-06-30T09:37:11.9069383+05:30 [Information] Resolved variable 'BufferTime' from global variables: 500
2025-06-30T09:37:11.9070197+05:30 [Information] Resolved variable 'MathBaseline' from global variables: 1
2025-06-30T09:37:11.9071026+05:30 [Information] Expression after variable resolution: '1 + 500 * 2'
2025-06-30T09:37:11.9071562+05:30 [Information] Expression after variable resolution: '1 + 500 * 2'
2025-06-30T09:37:11.9072151+05:30 [Information] Mathematical expression 'MathBaseline + $var:BufferTime * 2' evaluated to: 1001
2025-06-30T09:37:11.9072614+05:30 [Information] Mathematical expression result: '1001'
2025-06-30T09:37:11.9073114+05:30 [Information] Resolved JPath '$var:MathBaseline + $var:BufferTime * 2' to '1001'
2025-06-30T09:37:11.9073571+05:30 [Information] Resolved template pattern '{{$var:MathBaseline + $var:BufferTime * 2}}' to '1001' in '{{$var:MathBaseline + $var:BufferTime * 2}}'
2025-06-30T09:37:11.9074111+05:30 [Information] Resolved template '{{$var:MathBaseline + $var:BufferTime * 2}}' to '1001'
2025-06-30T09:37:11.9074959+05:30 [Information] Set Value to Step 'fdbfef01-e64d-4307-97f6-9a8a38b16b28' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 94,
  "startTime": "2025-06-30T04:07:11.8124695Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "cmd"
}
2025-06-30T09:37:11.9076646+05:30 [Information] Step 'Operator Precedence Test' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "f09c2ca4-f087-4d4d-8de0-c26dc6ee0c93",
        "tables": {},
        "variables": {},
        "name": "Fast Baseline Operation",
        "input": {
          "command": "echo",
          "arguments": [
            "Establishing baseline performance"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Establishing baseline performance",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 90,
          "startTime": "2025-06-30T04:07:11.298906Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "ad7331ee-d24e-45ad-b5a9-5cfffa1529e0",
        "tables": {},
        "variables": {},
        "name": "Regression Threshold Test",
        "input": {
          "command": "echo",
          "arguments": [
            "Testing mathematical expression: MathBaseline * MaxRegressionPercent / 100"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Testing mathematical expression: MathBaseline * MaxRegressionPercent / 100",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 234,
          "startTime": "2025-06-30T04:07:11.3917689Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "54584d1d-0595-4471-961c-26601edd84c2",
        "tables": {},
        "variables": {},
        "name": "Complex Expression Test",
        "input": {
          "command": "echo",
          "arguments": [
            "Testing complex expression: MathBaseline * MaxRegressionPercent / 100 + BufferTime"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Testing complex expression: MathBaseline * MaxRegressionPercent / 100 + BufferTime",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 182,
          "startTime": "2025-06-30T04:07:11.6282873Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "fdbfef01-e64d-4307-97f6-9a8a38b16b28",
        "tables": {},
        "variables": {},
        "name": "Operator Precedence Test",
        "input": {
          "command": "echo",
          "arguments": [
            "Testing operator precedence"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Testing operator precedence",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 94,
          "startTime": "2025-06-30T04:07:11.8124695Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaselineThreshold": 100,
      "MaxRegressionPercent": 1000,
      "BufferTime": 500,
      "FastThreshold": 10000,
      "SlowThreshold": 20000,
      "MathBaseline": 1
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:37:11.9077399+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:37:11.9077917+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:37:11.9078414+05:30 [Information] No performance data found for step 'Operator Precedence Test' (cmd)
2025-06-30T09:37:11.9078820+05:30 [Information] [ OK       ]
2025-06-30T09:37:11.9084426+05:30 [Information] Set Value to Step '2bdb8cc8-a706-417a-8a53-4c51c257843c' name:
"Performance Budget Test"
2025-06-30T09:37:11.9085478+05:30 [Information] [ RUN      ] Simple Mathematical Expressions Demo - Passing Tests > Performance Budget Test
2025-06-30T09:37:11.9088050+05:30 [Information] Executing single instance of step 'Performance Budget Test'.
2025-06-30T09:37:11.9091886+05:30 [Information] Executing step 'Performance Budget Test' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "f09c2ca4-f087-4d4d-8de0-c26dc6ee0c93",
        "tables": {},
        "variables": {},
        "name": "Fast Baseline Operation",
        "input": {
          "command": "echo",
          "arguments": [
            "Establishing baseline performance"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Establishing baseline performance",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 90,
          "startTime": "2025-06-30T04:07:11.298906Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "ad7331ee-d24e-45ad-b5a9-5cfffa1529e0",
        "tables": {},
        "variables": {},
        "name": "Regression Threshold Test",
        "input": {
          "command": "echo",
          "arguments": [
            "Testing mathematical expression: MathBaseline * MaxRegressionPercent / 100"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Testing mathematical expression: MathBaseline * MaxRegressionPercent / 100",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 234,
          "startTime": "2025-06-30T04:07:11.3917689Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "54584d1d-0595-4471-961c-26601edd84c2",
        "tables": {},
        "variables": {},
        "name": "Complex Expression Test",
        "input": {
          "command": "echo",
          "arguments": [
            "Testing complex expression: MathBaseline * MaxRegressionPercent / 100 + BufferTime"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Testing complex expression: MathBaseline * MaxRegressionPercent / 100 + BufferTime",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 182,
          "startTime": "2025-06-30T04:07:11.6282873Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "fdbfef01-e64d-4307-97f6-9a8a38b16b28",
        "tables": {},
        "variables": {},
        "name": "Operator Precedence Test",
        "input": {
          "command": "echo",
          "arguments": [
            "Testing operator precedence"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Testing operator precedence",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 94,
          "startTime": "2025-06-30T04:07:11.8124695Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "2bdb8cc8-a706-417a-8a53-4c51c257843c",
        "tables": {},
        "variables": {
          "CriticalPath": 5000,
          "OptimizationFactor": 0.8
        },
        "name": "Performance Budget Test"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaselineThreshold": 100,
      "MaxRegressionPercent": 1000,
      "BufferTime": 500,
      "FastThreshold": 10000,
      "SlowThreshold": 20000,
      "MathBaseline": 1
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:37:11.9093879+05:30 [Information] Set Value to Step '2bdb8cc8-a706-417a-8a53-4c51c257843c' input:
{
  "command": "echo",
  "arguments": [
    "Testing performance budget: CriticalPath * OptimizationFactor"
  ],
  "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
  "printOutput": false
}
2025-06-30T09:37:11.9094810+05:30 [Information] Delaying step execution for 200ms
2025-06-30T09:37:12.1388628+05:30 [Information] Set Value to Step '2bdb8cc8-a706-417a-8a53-4c51c257843c' output:
{
  "stdout": "Testing performance budget: CriticalPath * OptimizationFactor",
  "stderr": "",
  "exitCode": 0
}
2025-06-30T09:37:12.1390263+05:30 [Information] Set Value to Step '2bdb8cc8-a706-417a-8a53-4c51c257843c' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:07:11.9088023Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "cmd"
}
2025-06-30T09:37:12.1392305+05:30 [Information] Run Asserter : {"Expression1":{"Value":"0","ErrorMesage":"Echo command failed"},"Expression2":{"JPath":"$curStep:$.output.exitCode","ErrorMesage":"Echo command failed"},"ErrorMesage":"Echo command failed"}
2025-06-30T09:37:12.1394517+05:30 [Information] Resolving path '$.output.exitCode' using $curStep:
2025-06-30T09:37:12.1395904+05:30 [Information] Resolved JPath '$curStep:$.output.exitCode' to '0'
2025-06-30T09:37:12.1397192+05:30 [Information] Run Asserter : {"Expression1":{"JPath":"$curStep:performance.executionTimeMs","ErrorMesage":"Performance budget exceeded: {{$curStep:performance.executionTimeMs}}ms vs budget {{$var:CriticalPath * $var:OptimizationFactor}}ms"},"Expression2":{"Value":"{{$var:CriticalPath * $var:OptimizationFactor}}","ErrorMesage":"Performance budget exceeded: {{$curStep:performance.executionTimeMs}}ms vs budget {{$var:CriticalPath * $var:OptimizationFactor}}ms"},"ErrorMesage":"Performance budget exceeded: {{$curStep:performance.executionTimeMs}}ms vs budget {{$var:CriticalPath * $var:OptimizationFactor}}ms"}
2025-06-30T09:37:12.1398530+05:30 [Information] Resolving path 'performance.executionTimeMs' using $curStep:
2025-06-30T09:37:12.1399272+05:30 [Information] Resolved JPath '$curStep:performance.executionTimeMs' to '0'
2025-06-30T09:37:12.1400323+05:30 [Information] Processing $var: path 'CriticalPath * $var:OptimizationFactor'
2025-06-30T09:37:12.1401374+05:30 [Information] Checking if 'CriticalPath * $var:OptimizationFactor' is mathematical expression: hasArithmetic=True, hasArray=False, hasProperty=False, result=True
2025-06-30T09:37:12.1402167+05:30 [Information] Detected mathematical expression in $var: 'CriticalPath * $var:OptimizationFactor'
2025-06-30T09:37:12.1402758+05:30 [Information] Evaluating mathematical expression: 'CriticalPath * $var:OptimizationFactor'
2025-06-30T09:37:12.1404174+05:30 [Information] Resolving variables in expression: 'CriticalPath * $var:OptimizationFactor'
2025-06-30T09:37:12.1405401+05:30 [Information] Resolved variable 'OptimizationFactor' from step variables: 0.8
2025-06-30T09:37:12.1406308+05:30 [Information] Resolved variable 'CriticalPath' from step variables: 5000
2025-06-30T09:37:12.1406877+05:30 [Information] Expression after variable resolution: '5000 * 0.8'
2025-06-30T09:37:12.1407315+05:30 [Information] Expression after variable resolution: '5000 * 0.8'
2025-06-30T09:37:12.1407870+05:30 [Information] Mathematical expression 'CriticalPath * $var:OptimizationFactor' evaluated to: 4000
2025-06-30T09:37:12.1408294+05:30 [Information] Mathematical expression result: '4000'
2025-06-30T09:37:12.1408786+05:30 [Information] Resolved JPath '$var:CriticalPath * $var:OptimizationFactor' to '4000'
2025-06-30T09:37:12.1409175+05:30 [Information] Resolved template pattern '{{$var:CriticalPath * $var:OptimizationFactor}}' to '4000' in '{{$var:CriticalPath * $var:OptimizationFactor}}'
2025-06-30T09:37:12.1409562+05:30 [Information] Resolved template '{{$var:CriticalPath * $var:OptimizationFactor}}' to '4000'
2025-06-30T09:37:12.1410197+05:30 [Information] Run Asserter : {"Expression1":{"JPath":"$curStep:performance.executionTimeMs","ErrorMesage":"Absolute performance limit exceeded: {{$curStep:performance.executionTimeMs}}ms vs {{$var:CriticalPath}}ms"},"Expression2":{"Value":"{{$var:CriticalPath}}","ErrorMesage":"Absolute performance limit exceeded: {{$curStep:performance.executionTimeMs}}ms vs {{$var:CriticalPath}}ms"},"ErrorMesage":"Absolute performance limit exceeded: {{$curStep:performance.executionTimeMs}}ms vs {{$var:CriticalPath}}ms"}
2025-06-30T09:37:12.1410672+05:30 [Information] Resolving path 'performance.executionTimeMs' using $curStep:
2025-06-30T09:37:12.1411652+05:30 [Information] Resolved JPath '$curStep:performance.executionTimeMs' to '0'
2025-06-30T09:37:12.1412773+05:30 [Information] Processing $var: path 'CriticalPath'
2025-06-30T09:37:12.1413375+05:30 [Information] Checking if 'CriticalPath' is mathematical expression: hasArithmetic=False, hasArray=False, hasProperty=False, result=False
2025-06-30T09:37:12.1413884+05:30 [Information] Resolved path 'CriticalPath' in step-specific variables.
2025-06-30T09:37:12.1414389+05:30 [Information] Resolved JPath '$var:CriticalPath' to '5000'
2025-06-30T09:37:12.1414780+05:30 [Information] Resolved template pattern '{{$var:CriticalPath}}' to '5000' in '{{$var:CriticalPath}}'
2025-06-30T09:37:12.1415215+05:30 [Information] Resolved template '{{$var:CriticalPath}}' to '5000'
2025-06-30T09:37:12.1415931+05:30 [Information] Set Value to Step '2bdb8cc8-a706-417a-8a53-4c51c257843c' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 232,
  "startTime": "2025-06-30T04:07:11.9088023Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "cmd"
}
2025-06-30T09:37:12.1417580+05:30 [Information] Step 'Performance Budget Test' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "f09c2ca4-f087-4d4d-8de0-c26dc6ee0c93",
        "tables": {},
        "variables": {},
        "name": "Fast Baseline Operation",
        "input": {
          "command": "echo",
          "arguments": [
            "Establishing baseline performance"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Establishing baseline performance",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 90,
          "startTime": "2025-06-30T04:07:11.298906Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "ad7331ee-d24e-45ad-b5a9-5cfffa1529e0",
        "tables": {},
        "variables": {},
        "name": "Regression Threshold Test",
        "input": {
          "command": "echo",
          "arguments": [
            "Testing mathematical expression: MathBaseline * MaxRegressionPercent / 100"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Testing mathematical expression: MathBaseline * MaxRegressionPercent / 100",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 234,
          "startTime": "2025-06-30T04:07:11.3917689Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "54584d1d-0595-4471-961c-26601edd84c2",
        "tables": {},
        "variables": {},
        "name": "Complex Expression Test",
        "input": {
          "command": "echo",
          "arguments": [
            "Testing complex expression: MathBaseline * MaxRegressionPercent / 100 + BufferTime"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Testing complex expression: MathBaseline * MaxRegressionPercent / 100 + BufferTime",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 182,
          "startTime": "2025-06-30T04:07:11.6282873Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "fdbfef01-e64d-4307-97f6-9a8a38b16b28",
        "tables": {},
        "variables": {},
        "name": "Operator Precedence Test",
        "input": {
          "command": "echo",
          "arguments": [
            "Testing operator precedence"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Testing operator precedence",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 94,
          "startTime": "2025-06-30T04:07:11.8124695Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "2bdb8cc8-a706-417a-8a53-4c51c257843c",
        "tables": {},
        "variables": {
          "CriticalPath": 5000,
          "OptimizationFactor": 0.8
        },
        "name": "Performance Budget Test",
        "input": {
          "command": "echo",
          "arguments": [
            "Testing performance budget: CriticalPath * OptimizationFactor"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Testing performance budget: CriticalPath * OptimizationFactor",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 232,
          "startTime": "2025-06-30T04:07:11.9088023Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaselineThreshold": 100,
      "MaxRegressionPercent": 1000,
      "BufferTime": 500,
      "FastThreshold": 10000,
      "SlowThreshold": 20000,
      "MathBaseline": 1
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:37:12.1418335+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:37:12.1419028+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:37:12.1419904+05:30 [Information] No performance data found for step 'Performance Budget Test' (cmd)
2025-06-30T09:37:12.1420456+05:30 [Information] [ OK       ]
2025-06-30T09:37:12.1423044+05:30 [Information] Set Value to Step '45266780-6b07-43fe-934f-74c78792046d' name:
"Error Message Demo"
2025-06-30T09:37:12.1423784+05:30 [Information] [ RUN      ] Simple Mathematical Expressions Demo - Passing Tests > Error Message Demo
2025-06-30T09:37:12.1426739+05:30 [Information] Executing single instance of step 'Error Message Demo'.
2025-06-30T09:37:12.1428774+05:30 [Information] Executing step 'Error Message Demo' in Test context:
{
  "test": {
    "steps": [
      {
        "id": "f09c2ca4-f087-4d4d-8de0-c26dc6ee0c93",
        "tables": {},
        "variables": {},
        "name": "Fast Baseline Operation",
        "input": {
          "command": "echo",
          "arguments": [
            "Establishing baseline performance"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Establishing baseline performance",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 90,
          "startTime": "2025-06-30T04:07:11.298906Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "ad7331ee-d24e-45ad-b5a9-5cfffa1529e0",
        "tables": {},
        "variables": {},
        "name": "Regression Threshold Test",
        "input": {
          "command": "echo",
          "arguments": [
            "Testing mathematical expression: MathBaseline * MaxRegressionPercent / 100"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Testing mathematical expression: MathBaseline * MaxRegressionPercent / 100",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 234,
          "startTime": "2025-06-30T04:07:11.3917689Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "54584d1d-0595-4471-961c-26601edd84c2",
        "tables": {},
        "variables": {},
        "name": "Complex Expression Test",
        "input": {
          "command": "echo",
          "arguments": [
            "Testing complex expression: MathBaseline * MaxRegressionPercent / 100 + BufferTime"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Testing complex expression: MathBaseline * MaxRegressionPercent / 100 + BufferTime",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 182,
          "startTime": "2025-06-30T04:07:11.6282873Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "fdbfef01-e64d-4307-97f6-9a8a38b16b28",
        "tables": {},
        "variables": {},
        "name": "Operator Precedence Test",
        "input": {
          "command": "echo",
          "arguments": [
            "Testing operator precedence"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Testing operator precedence",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 94,
          "startTime": "2025-06-30T04:07:11.8124695Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "2bdb8cc8-a706-417a-8a53-4c51c257843c",
        "tables": {},
        "variables": {
          "CriticalPath": 5000,
          "OptimizationFactor": 0.8
        },
        "name": "Performance Budget Test",
        "input": {
          "command": "echo",
          "arguments": [
            "Testing performance budget: CriticalPath * OptimizationFactor"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Testing performance budget: CriticalPath * OptimizationFactor",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 232,
          "startTime": "2025-06-30T04:07:11.9088023Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "45266780-6b07-43fe-934f-74c78792046d",
        "tables": {},
        "variables": {},
        "name": "Error Message Demo"
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaselineThreshold": 100,
      "MaxRegressionPercent": 1000,
      "BufferTime": 500,
      "FastThreshold": 10000,
      "SlowThreshold": 20000,
      "MathBaseline": 1
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:37:12.1430342+05:30 [Information] Set Value to Step '45266780-6b07-43fe-934f-74c78792046d' input:
{
  "command": "echo",
  "arguments": [
    "Mathematical expressions work in error messages too!"
  ],
  "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
  "printOutput": false
}
2025-06-30T09:37:12.1431020+05:30 [Information] Delaying step execution for 50ms
2025-06-30T09:37:12.2281496+05:30 [Information] Set Value to Step '45266780-6b07-43fe-934f-74c78792046d' output:
{
  "stdout": "Mathematical expressions work in error messages too!",
  "stderr": "",
  "exitCode": 0
}
2025-06-30T09:37:12.2283117+05:30 [Information] Set Value to Step '45266780-6b07-43fe-934f-74c78792046d' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 0,
  "startTime": "2025-06-30T04:07:12.1426715Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "cmd"
}
2025-06-30T09:37:12.2284281+05:30 [Information] Run Asserter : {"Expression1":{"Value":"0","ErrorMesage":"Echo command failed"},"Expression2":{"JPath":"$curStep:$.output.exitCode","ErrorMesage":"Echo command failed"},"ErrorMesage":"Echo command failed"}
2025-06-30T09:37:12.2285139+05:30 [Information] Resolving path '$.output.exitCode' using $curStep:
2025-06-30T09:37:12.2286061+05:30 [Information] Resolved JPath '$curStep:$.output.exitCode' to '0'
2025-06-30T09:37:12.2286859+05:30 [Information] Run Asserter : {"Expression1":{"JPath":"$curStep:performance.executionTimeMs","ErrorMesage":"Performance test failed: {{$curStep:performance.executionTimeMs}}ms exceeded {{$var:MathBaseline * 3}}ms (3x baseline of {{$var:MathBaseline}}ms)"},"Expression2":{"Value":"{{$var:MathBaseline * 3}}","ErrorMesage":"Performance test failed: {{$curStep:performance.executionTimeMs}}ms exceeded {{$var:MathBaseline * 3}}ms (3x baseline of {{$var:MathBaseline}}ms)"},"ErrorMesage":"Performance test failed: {{$curStep:performance.executionTimeMs}}ms exceeded {{$var:MathBaseline * 3}}ms (3x baseline of {{$var:MathBaseline}}ms)"}
2025-06-30T09:37:12.2287623+05:30 [Information] Resolving path 'performance.executionTimeMs' using $curStep:
2025-06-30T09:37:12.2288159+05:30 [Information] Resolved JPath '$curStep:performance.executionTimeMs' to '0'
2025-06-30T09:37:12.2288850+05:30 [Information] Processing $var: path 'MathBaseline * 3'
2025-06-30T09:37:12.2289391+05:30 [Information] Checking if 'MathBaseline * 3' is mathematical expression: hasArithmetic=True, hasArray=False, hasProperty=False, result=True
2025-06-30T09:37:12.2289783+05:30 [Information] Detected mathematical expression in $var: 'MathBaseline * 3'
2025-06-30T09:37:12.2290210+05:30 [Information] Evaluating mathematical expression: 'MathBaseline * 3'
2025-06-30T09:37:12.2290623+05:30 [Information] Resolving variables in expression: 'MathBaseline * 3'
2025-06-30T09:37:12.2291240+05:30 [Information] Resolved variable 'MathBaseline' from global variables: 1
2025-06-30T09:37:12.2291720+05:30 [Information] Expression after variable resolution: '1 * 3'
2025-06-30T09:37:12.2292099+05:30 [Information] Expression after variable resolution: '1 * 3'
2025-06-30T09:37:12.2292655+05:30 [Information] Mathematical expression 'MathBaseline * 3' evaluated to: 3
2025-06-30T09:37:12.2293028+05:30 [Information] Mathematical expression result: '3'
2025-06-30T09:37:12.2293473+05:30 [Information] Resolved JPath '$var:MathBaseline * 3' to '3'
2025-06-30T09:37:12.2293887+05:30 [Information] Resolved template pattern '{{$var:MathBaseline * 3}}' to '3' in '{{$var:MathBaseline * 3}}'
2025-06-30T09:37:12.2294402+05:30 [Information] Resolved template '{{$var:MathBaseline * 3}}' to '3'
2025-06-30T09:37:12.2295132+05:30 [Information] Set Value to Step '45266780-6b07-43fe-934f-74c78792046d' performance:
{
  "executionTimeMs": 0,
  "assertionTimeMs": 86,
  "startTime": "2025-06-30T04:07:12.1426715Z",
  "endTime": "0001-01-01T00:00:00",
  "stepType": "cmd"
}
2025-06-30T09:37:12.2296880+05:30 [Information] Step 'Error Message Demo' executed. Result added to context.:
{
  "test": {
    "steps": [
      {
        "id": "f09c2ca4-f087-4d4d-8de0-c26dc6ee0c93",
        "tables": {},
        "variables": {},
        "name": "Fast Baseline Operation",
        "input": {
          "command": "echo",
          "arguments": [
            "Establishing baseline performance"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Establishing baseline performance",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 90,
          "startTime": "2025-06-30T04:07:11.298906Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "ad7331ee-d24e-45ad-b5a9-5cfffa1529e0",
        "tables": {},
        "variables": {},
        "name": "Regression Threshold Test",
        "input": {
          "command": "echo",
          "arguments": [
            "Testing mathematical expression: MathBaseline * MaxRegressionPercent / 100"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Testing mathematical expression: MathBaseline * MaxRegressionPercent / 100",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 234,
          "startTime": "2025-06-30T04:07:11.3917689Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "54584d1d-0595-4471-961c-26601edd84c2",
        "tables": {},
        "variables": {},
        "name": "Complex Expression Test",
        "input": {
          "command": "echo",
          "arguments": [
            "Testing complex expression: MathBaseline * MaxRegressionPercent / 100 + BufferTime"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Testing complex expression: MathBaseline * MaxRegressionPercent / 100 + BufferTime",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 182,
          "startTime": "2025-06-30T04:07:11.6282873Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "fdbfef01-e64d-4307-97f6-9a8a38b16b28",
        "tables": {},
        "variables": {},
        "name": "Operator Precedence Test",
        "input": {
          "command": "echo",
          "arguments": [
            "Testing operator precedence"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Testing operator precedence",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 94,
          "startTime": "2025-06-30T04:07:11.8124695Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "2bdb8cc8-a706-417a-8a53-4c51c257843c",
        "tables": {},
        "variables": {
          "CriticalPath": 5000,
          "OptimizationFactor": 0.8
        },
        "name": "Performance Budget Test",
        "input": {
          "command": "echo",
          "arguments": [
            "Testing performance budget: CriticalPath * OptimizationFactor"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Testing performance budget: CriticalPath * OptimizationFactor",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 232,
          "startTime": "2025-06-30T04:07:11.9088023Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      },
      {
        "id": "45266780-6b07-43fe-934f-74c78792046d",
        "tables": {},
        "variables": {},
        "name": "Error Message Demo",
        "input": {
          "command": "echo",
          "arguments": [
            "Mathematical expressions work in error messages too!"
          ],
          "workingDirectory": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
          "printOutput": false
        },
        "output": {
          "stdout": "Mathematical expressions work in error messages too!",
          "stderr": "",
          "exitCode": 0
        },
        "performance": {
          "executionTimeMs": 0,
          "assertionTimeMs": 86,
          "startTime": "2025-06-30T04:07:12.1426715Z",
          "endTime": "0001-01-01T00:00:00",
          "stepType": "cmd"
        }
      }
    ],
    "variables": {
      "curDir": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testrunner\\etc",
      "baseUrl": "https://httpbin.org",
      "serverIp": "127.0.0.1",
      "serverPort": 60606,
      "dbInitStatus": "initialize_db",
      "BaselineThreshold": 100,
      "MaxRegressionPercent": 1000,
      "BufferTime": 500,
      "FastThreshold": 10000,
      "SlowThreshold": 20000,
      "MathBaseline": 1
    },
    "tables": {
      "UserTable": [
        {
          "Username": "user1",
          "Email": "<EMAIL>",
          "Roles": [
            "Admin",
            "Editor"
          ],
          "Preferences": {
            "Notifications": "true",
            "Theme": "dark"
          }
        },
        {
          "Username": "user2",
          "Email": "<EMAIL>",
          "Roles": [
            "Viewer"
          ],
          "Preferences": {
            "Notifications": "false",
            "Theme": "light"
          }
        }
      ]
    },
    "context": {}
  }
}
2025-06-30T09:37:12.2297642+05:30 [Information] Resolving path '$.performanceData' using $curStep:
2025-06-30T09:37:12.2298160+05:30 [Information] Resolved JPath '$curStep:$.performanceData' to ''
2025-06-30T09:37:12.2298565+05:30 [Information] No performance data found for step 'Error Message Demo' (cmd)
2025-06-30T09:37:12.2298958+05:30 [Information] [ OK       ]
2025-06-30T09:37:12.2301139+05:30 [Information] [----------] 6 steps from Simple Mathematical Expressions Demo - Passing Tests (933 ms total)
